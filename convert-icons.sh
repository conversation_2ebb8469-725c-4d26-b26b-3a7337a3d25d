#!/bin/bash

# Script to convert .razor icon files to .svelte files

# Find all .razor files in the icons directory
ICON_FILES=$(find src/lib/components/icons -name "*.razor")

# Process each file
for file in $ICON_FILES; do
    # Get the base name without extension
    base_name=$(basename "$file" .razor)
    # Create the new file path with .svelte extension
    new_file="src/lib/components/icons/${base_name}.svelte"

    echo "Converting $file to $new_file"

    # Read the content and convert Razor comments to Svelte comments
    # This handles both @* Comment *@ and @*Comment*@ formats
    content=$(cat "$file" | sed 's/@\*\s*\(.*\)\s*\*\//<!-- \1 -->/g')

    # Write the content to the new file
    echo "$content" > "$new_file"

    echo "Created $new_file"
done

echo "Conversion complete!"
