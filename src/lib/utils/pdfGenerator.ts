import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import type { Invoice, InvoiceLineItem } from '$lib/api/invoices';
import { formatCurrency } from '$lib/config/currency';

export interface InvoiceFormData {
  invoiceNumber: string;
  customerId: string;
  issueDate: string;
  dueDate: string;
  status: { id: string; name: string; color: string };
  lineItems: InvoiceLineItem[];
  customHeaderFields: Array<{ id: string; label: string; value: string; type: string }>;
  notes: string;
  terms: string;
  templateId: string;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
}

export async function generateInvoicePDF(
  invoiceData: Invoice | InvoiceFormData,
  customerName?: string,
  customerAddress?: any
): Promise<void> {
  try {
    // Create a temporary container for the invoice HTML
    const container = document.createElement('div');
    container.style.position = 'absolute';
    container.style.left = '-9999px';
    container.style.top = '-9999px';
    container.style.width = '794px'; // A4 width in pixels at 96 DPI
    container.style.backgroundColor = 'white';
    container.style.padding = '40px';
    container.style.fontFamily = 'Arial, sans-serif';
    container.style.fontSize = '12px';
    container.style.lineHeight = '1.4';
    container.style.color = '#000000';

    // Generate the invoice HTML
    container.innerHTML = generateInvoiceHTML(invoiceData, customerName, customerAddress);

    // Add to DOM temporarily
    document.body.appendChild(container);

    try {
      // Convert HTML to canvas
      const canvas = await html2canvas(container, {
        scale: 2, // Higher resolution
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        width: 794,
        height: container.scrollHeight
      });

      // Create PDF
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'px',
        format: [794, 1123] // A4 size in pixels
      });

      const imgData = canvas.toDataURL('image/png');
      const imgWidth = 794;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      // Add image to PDF
      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

      // Generate filename
      const invoiceNumber = 'invoiceNumber' in invoiceData 
        ? invoiceData.invoiceNumber 
        : `INV-${new Date().getFullYear()}-DRAFT`;
      const filename = `${invoiceNumber}.pdf`;

      // Download the PDF
      pdf.save(filename);
    } finally {
      // Clean up
      document.body.removeChild(container);
    }
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

function generateInvoiceHTML(
  invoiceData: Invoice | InvoiceFormData,
  customerName?: string,
  customerAddress?: any
): string {
  const isFormData = !('invoiceNumber' in invoiceData);
  const invoiceNumber = isFormData ? 'DRAFT' : invoiceData.invoiceNumber;
  const issueDate = new Date(invoiceData.issueDate).toLocaleDateString();
  const dueDate = new Date(invoiceData.dueDate).toLocaleDateString();
  
  // Get customer info
  const custName = customerName || (isFormData ? 'Customer Name' : (invoiceData as Invoice).customerName) || 'Customer Name';
  const custAddress = customerAddress || (isFormData ? null : (invoiceData as Invoice).customerAddress);

  return `
    <div style="max-width: 794px; margin: 0 auto; background: white; padding: 0;">
      <!-- Header -->
      <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 40px; border-bottom: 2px solid #2563eb; padding-bottom: 20px;">
        <div>
          <h1 style="margin: 0; font-size: 32px; font-weight: bold; color: #2563eb;">INVOICE</h1>
        </div>
        <div style="text-align: right;">
          <div style="font-size: 14px; margin-bottom: 5px;"><strong>Invoice #:</strong> ${invoiceNumber}</div>
          <div style="font-size: 14px; margin-bottom: 5px;"><strong>Date:</strong> ${issueDate}</div>
          <div style="font-size: 14px; margin-bottom: 5px;"><strong>Due Date:</strong> ${dueDate}</div>
          <div style="font-size: 14px;"><strong>Status:</strong> <span style="color: ${invoiceData.status.color};">${invoiceData.status.name}</span></div>
        </div>
      </div>

      <!-- Company and Customer Info -->
      <div style="display: flex; justify-content: space-between; margin-bottom: 40px;">
        <div style="width: 45%;">
          <h3 style="margin: 0 0 10px 0; font-size: 16px; color: #374151;">From:</h3>
          <div style="font-size: 12px; line-height: 1.6;">
            <strong>Your Company Name</strong><br>
            123 Business Street<br>
            City, State 12345<br>
            Phone: (*************<br>
            Email: <EMAIL>
          </div>
        </div>
        <div style="width: 45%;">
          <h3 style="margin: 0 0 10px 0; font-size: 16px; color: #374151;">Bill To:</h3>
          <div style="font-size: 12px; line-height: 1.6;">
            <strong>${custName}</strong><br>
            ${custAddress ? `
              ${custAddress.street}<br>
              ${custAddress.city}, ${custAddress.state} ${custAddress.zipCode}<br>
              ${custAddress.country}
            ` : 'Customer Address'}
          </div>
        </div>
      </div>

      <!-- Custom Header Fields -->
      ${invoiceData.customHeaderFields && invoiceData.customHeaderFields.length > 0 ? `
        <div style="margin-bottom: 30px;">
          <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 15px;">
            ${invoiceData.customHeaderFields.map(field => `
              <div style="font-size: 12px;">
                <strong>${field.label}:</strong> ${field.value}
              </div>
            `).join('')}
          </div>
        </div>
      ` : ''}

      <!-- Line Items Table -->
      <div style="margin-bottom: 30px;">
        <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
          <thead>
            <tr style="background-color: #f3f4f6;">
              <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: left; font-weight: bold;">Description</th>
              <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: center; font-weight: bold; width: 80px;">Qty</th>
              <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: right; font-weight: bold; width: 100px;">Unit Price</th>
              <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: right; font-weight: bold; width: 80px;">Tax %</th>
              <th style="border: 1px solid #e5e7eb; padding: 12px; text-align: right; font-weight: bold; width: 100px;">Total</th>
            </tr>
          </thead>
          <tbody>
            ${invoiceData.lineItems.map(item => `
              <tr>
                <td style="border: 1px solid #e5e7eb; padding: 12px; vertical-align: top;">
                  <div style="font-weight: 500;">${item.description}</div>
                  ${item.additionalInfo ? `<div style="font-size: 10px; color: #6b7280; margin-top: 4px;">${item.additionalInfo}</div>` : ''}
                </td>
                <td style="border: 1px solid #e5e7eb; padding: 12px; text-align: center;">${item.quantity}</td>
                <td style="border: 1px solid #e5e7eb; padding: 12px; text-align: right;">${formatCurrency(item.unitPrice)}</td>
                <td style="border: 1px solid #e5e7eb; padding: 12px; text-align: right;">${item.taxRate}%</td>
                <td style="border: 1px solid #e5e7eb; padding: 12px; text-align: right; font-weight: 500;">${formatCurrency(item.lineTotal)}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>

      <!-- Totals -->
      <div style="display: flex; justify-content: flex-end; margin-bottom: 40px;">
        <div style="width: 300px;">
          <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e5e7eb;">
            <span>Subtotal:</span>
            <span>${formatCurrency(invoiceData.subtotal)}</span>
          </div>
          <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e5e7eb;">
            <span>Tax:</span>
            <span>${formatCurrency(invoiceData.taxAmount)}</span>
          </div>
          ${invoiceData.discountAmount > 0 ? `
            <div style="display: flex; justify-content: space-between; padding: 8px 0; border-bottom: 1px solid #e5e7eb; color: #dc2626;">
              <span>Discount:</span>
              <span>-${formatCurrency(invoiceData.discountAmount)}</span>
            </div>
          ` : ''}
          <div style="display: flex; justify-content: space-between; padding: 12px 0; border-top: 2px solid #2563eb; font-size: 16px; font-weight: bold; color: #2563eb;">
            <span>Total:</span>
            <span>${formatCurrency(invoiceData.totalAmount)}</span>
          </div>
        </div>
      </div>

      <!-- Notes and Terms -->
      <div style="display: flex; justify-content: space-between; gap: 30px;">
        ${invoiceData.notes ? `
          <div style="width: 48%;">
            <h4 style="margin: 0 0 10px 0; font-size: 14px; color: #374151;">Notes:</h4>
            <div style="font-size: 11px; line-height: 1.5; color: #6b7280;">
              ${invoiceData.notes.replace(/\n/g, '<br>')}
            </div>
          </div>
        ` : ''}
        ${invoiceData.terms ? `
          <div style="width: 48%;">
            <h4 style="margin: 0 0 10px 0; font-size: 14px; color: #374151;">Terms & Conditions:</h4>
            <div style="font-size: 11px; line-height: 1.5; color: #6b7280;">
              ${invoiceData.terms.replace(/\n/g, '<br>')}
            </div>
          </div>
        ` : ''}
      </div>

      <!-- Footer -->
      <div style="margin-top: 50px; padding-top: 20px; border-top: 1px solid #e5e7eb; text-align: center; font-size: 10px; color: #9ca3af;">
        Thank you for your business!
      </div>
    </div>
  `;
} 