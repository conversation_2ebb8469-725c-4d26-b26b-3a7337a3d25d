import { writable } from 'svelte/store';
import { browser } from '$app/environment';
import { goto } from '$app/navigation';

// Define user type
export interface User {
  email: string;
  name?: string;
  token: string;
}

// API response type for login
interface LoginResponse {
  email: string;
  password: string | null;
  token: string;
}

// Initialize the auth store with data from localStorage if available
const storedUser = browser ? localStorage.getItem('user') : null;
const storedToken = browser ? localStorage.getItem('authToken') : null;
const initialUser = storedUser ? JSON.parse(storedUser) : null;

// Create the auth store
export const user = writable<User | null>(initialUser);
export const authToken = writable<string | null>(storedToken);

// Subscribe to changes and update localStorage
if (browser) {
  user.subscribe((value) => {
    if (value) {
      localStorage.setItem('user', JSON.stringify(value));
      localStorage.setItem('authToken', value.token);
      authToken.set(value.token);
    } else {
      localStorage.removeItem('user');
      localStorage.removeItem('authToken');
      authToken.set(null);
    }
  });
}

// Authentication functions
export async function login(email: string, password: string): Promise<{ success: boolean; error?: string }> {
  try {
    const response = await fetch('https://app-ejp-api.azurewebsites.net/Auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email,
        password
      })
    });

    if (!response.ok) {
      if (response.status === 401) {
        return { success: false, error: 'Invalid email or password' };
      }
      return { success: false, error: 'Login failed. Please try again.' };
    }

    const data: LoginResponse = await response.json();
    
    // Set user with token
    user.set({
      email: data.email,
      token: data.token
    });

    return { success: true };
  } catch (error) {
    console.error('Login error:', error);
    return { success: false, error: 'Network error. Please check your connection.' };
  }
}

export function logout() {
  user.set(null);
  if (browser) {
    goto('/login');
  }
}

export function isLoggedIn(): boolean {
  let loggedIn = false;
  user.subscribe(value => {
    loggedIn = !!value;
  })();
  return loggedIn;
}

// Utility function for making authenticated API requests
export async function authenticatedFetch(url: string, options: RequestInit = {}): Promise<Response> {
  let token: string | null = null;
  
  // Get current token
  authToken.subscribe(value => {
    token = value;
  })();

  if (!token) {
    logout();
    throw new Error('No authentication token available');
  }

  const response = await fetch(url, {
    ...options,
    headers: {
      ...options.headers,
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
  });

  // If unauthorized, logout and redirect
  if (response.status === 401) {
    logout();
    throw new Error('Session expired. Please login again.');
  }

  return response;
}
