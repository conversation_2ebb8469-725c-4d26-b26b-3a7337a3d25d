<script>
  export let color = "currentColor";
  export let size = "18";
  export let className = "";
</script>

<svg
  width={size}
  height={size}
  viewBox="0 0 18 20"
  fill="none"
  xmlns="http://www.w3.org/2000/svg"
  class="icon-bin {className}"
>
  <path d="M17 4C17.2549 4.00028 17.5 4.09788 17.6854 4.27285C17.8707 4.44782 17.9822 4.68695 17.9972 4.94139C18.0121 5.19584 17.9293 5.44638 17.7657 5.64183C17.6021 5.83729 17.3701 5.9629 17.117 5.993L17 6H16.919L16 17C16 17.7652 15.7077 18.5015 15.1827 19.0583C14.6577 19.615 13.9399 19.9501 13.176 19.995L13 20H4.99999C3.40199 20 2.09599 18.751 2.00799 17.25L2.00299 17.083L1.07999 6H0.999991C0.745111 5.99972 0.499959 5.90212 0.314622 5.72715C0.129286 5.55218 0.0177558 5.31305 0.00281867 5.05861C-0.0121185 4.80416 0.0706654 4.55362 0.234256 4.35817C0.397846 4.16271 0.629895 4.0371 0.882991 4.007L0.999991 4H17ZM11 0C11.5304 0 12.0391 0.210714 12.4142 0.585786C12.7893 0.960859 13 1.46957 13 2C12.9997 2.25488 12.9021 2.50003 12.7271 2.68537C12.5522 2.8707 12.313 2.98223 12.0586 2.99717C11.8042 3.01211 11.5536 2.92933 11.3582 2.76574C11.1627 2.60214 11.0371 2.3701 11.007 2.117L11 2H6.99999L6.99299 2.117C6.96289 2.3701 6.83728 2.60214 6.64182 2.76574C6.44637 2.92933 6.19583 3.01211 5.94139 2.99717C5.68694 2.98223 5.44781 2.8707 5.27284 2.68537C5.09787 2.50003 5.00027 2.25488 4.99999 2C4.99983 1.49542 5.1904 1.00943 5.53349 0.639452C5.87658 0.269471 6.34684 0.0428433 6.84999 0.00500011L6.99999 0H11Z" fill={color}/>
</svg>

<style lang="less">
  .icon-bin {
    transition: all 0.3s ease;

    &:hover {
      fill: @red;
      transform: scale(1.1);
    }
  }
</style>
