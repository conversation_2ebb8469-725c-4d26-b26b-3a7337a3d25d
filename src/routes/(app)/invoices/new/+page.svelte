<script lang="ts">
  import { onMount, onDestroy } from 'svelte';
  import { goto, beforeNavigate } from '$app/navigation';
  import { browser } from '$app/environment';
  import PageHeader from '$lib/components/PageHeader.svelte';
  import Button from '$lib/components/Button.svelte';
  import LoadingSpinner from '$lib/components/LoadingSpinner.svelte';
  import { addToast } from '$lib/stores/toastStore';
  import { contactStore, customers } from '$lib/stores/customerStore';
  import CustomerSelect from '$lib/components/CustomerSelect.svelte';
  import {
    saveInvoice,
    getProducts,
    getInvoiceTemplates,
    getInvoiceStatuses,
    calculateApiInvoiceTotals,
    calculateLineItemTotals,
    createEmptyApiLineItem,
    getStatusNumber,
    type ApiInvoice,
    type ApiInvoiceLine,
    type Product,
    type InvoiceTemplate,
    type InvoiceStatus
  } from '$lib/api/invoices';
  import { getUninvoicedJobs, type Job } from '$lib/api/jobs';
  import { getUninvoicedQuotes, type Quote } from '$lib/api/quotes';
  import type { Contact } from '$lib/api/contacts';

  // Define interfaces for our form data - now using API format directly
  interface InvoiceFormData {
    id?: string;
    invoiceNumber?: number;
    status: number; // API format: 0 = Draft, 1 = Sent, etc.
    issueDate: string;
    dueDate: string;
    notes?: string;
    paymentTerms?: string;
    invoiceLines: ApiInvoiceLine[];
    // Additional fields for UI only
    customerId: string;
    templateId?: string;
    discountAmount: number;
  }

  // State variables
  let isLoading = false;
  let isSaving = false;
  let customerContacts: Contact[] = [];
  let products: Product[] = [];
  let invoiceTemplates: InvoiceTemplate[] = [];
  let invoiceStatuses: InvoiceStatus[] = [];
  let uninvoicedJobs: Job[] = [];
  let uninvoicedQuotes: Quote[] = [];
  let isLoadingData = true;
  let isLoadingUninvoiced = false;
  let customerSearch = '';
  let showCustomerDropdown = false;
  let highlightedIndex = -1;
  let formChanged = false;
  let initialFormState: string;
  let showUninvoicedSection = false;
  
  // UI state for line item toggles
  let showProductSelect: Record<number, boolean> = {};
  let showAdditionalInfo: Record<number, boolean> = {};

  // Multi-job selection state
  let selectedJobIds: Set<string> = new Set();
  let selectedQuoteIds: Set<string> = new Set();

  // Initialize form data with default values - using API format
  let formData: InvoiceFormData = {
    status: 0, // Draft
    issueDate: new Date().toISOString().split('T')[0],
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    invoiceLines: [createEmptyApiLineItem()],
    notes: '',
    paymentTerms: 'Payment due within 30 days. Please make payment via bank transfer.',
    customerId: '',
    templateId: '',
    discountAmount: 0
  };

  // Form validation
  let errors: Record<string, string> = {};
  let formSubmitted = false;

  // Helper functions
  function generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  function updateFormChanged() {
    if (!initialFormState) return;
    const currentFormState = JSON.stringify(formData);
    formChanged = currentFormState !== initialFormState;
  }

  function calculateTotals() {
    // Calculate line totals and update each line
    const updatedLines = formData.invoiceLines.map(line => calculateLineItemTotals(line));
    
    formData = {
      ...formData,
      invoiceLines: updatedLines
    };

    updateFormChanged();
  }

  function addLineItem() {
    const newLineNumber = formData.invoiceLines.length + 1;
    formData = {
      ...formData,
      invoiceLines: [...formData.invoiceLines, createEmptyApiLineItem(newLineNumber)]
    };
    updateFormChanged();
  }

  function toggleProductSelect(index: number) {
    showProductSelect[index] = !showProductSelect[index];
    showProductSelect = { ...showProductSelect };
  }

  function toggleAdditionalInfo(index: number) {
    showAdditionalInfo[index] = !showAdditionalInfo[index];
    showAdditionalInfo = { ...showAdditionalInfo };
  }

  function removeLineItem(index: number) {
    formData = {
      ...formData,
      invoiceLines: formData.invoiceLines.filter((_, i) => i !== index)
    };
    
    // Clean up UI state for removed item and shift indices
    const newShowProductSelect: Record<number, boolean> = {};
    const newShowAdditionalInfo: Record<number, boolean> = {};
    
    Object.keys(showProductSelect).forEach(key => {
      const idx = parseInt(key);
      if (idx < index) {
        newShowProductSelect[idx] = showProductSelect[idx];
      } else if (idx > index) {
        newShowProductSelect[idx - 1] = showProductSelect[idx];
      }
    });
    
    Object.keys(showAdditionalInfo).forEach(key => {
      const idx = parseInt(key);
      if (idx < index) {
        newShowAdditionalInfo[idx] = showAdditionalInfo[idx];
      } else if (idx > index) {
        newShowAdditionalInfo[idx - 1] = showAdditionalInfo[idx];
      }
    });
    
    showProductSelect = newShowProductSelect;
    showAdditionalInfo = newShowAdditionalInfo;
    
    calculateTotals();
    updateFormChanged();
  }

  function selectProduct(lineItemIndex: number, product: Product) {
    const updatedLines = [...formData.invoiceLines];
    updatedLines[lineItemIndex] = {
      ...updatedLines[lineItemIndex],
      description: product.description,
      unitPrice: product.price,
      taxRate: product.taxRate / 100 // Convert percentage to decimal
    };

    formData = {
      ...formData,
      invoiceLines: updatedLines
    };

    calculateTotals();
  }

  function validateForm(): boolean {
    errors = {};

    if (!formData.customerId) {
      errors.customerId = 'Please select a customer';
    }

    if (!formData.issueDate) {
      errors.issueDate = 'Issue date is required';
    }

    if (!formData.dueDate) {
      errors.dueDate = 'Due date is required';
    }

    // Validate each line item
    formData.invoiceLines.forEach((item, index) => {
      if (!item.description) {
        errors[`invoiceLines[${index}].description`] = 'Description is required';
      }

      if (item.quantity <= 0) {
        errors[`invoiceLines[${index}].quantity`] = 'Quantity must be greater than 0';
      }

      if (item.unitPrice < 0) {
        errors[`invoiceLines[${index}].unitPrice`] = 'Unit price cannot be negative';
      }
    });

    return Object.keys(errors).length === 0;
  }

  async function loadData() {
    isLoadingData = true;
    try {
      // Load contacts/customers
      await contactStore.loadContacts();

      // Load other data in parallel
      const [productsData, templatesData, statusesData] = await Promise.all([
        getProducts(),
        getInvoiceTemplates(),
        getInvoiceStatuses()
      ]);

      products = productsData;
      invoiceTemplates = templatesData;
      invoiceStatuses = statusesData;

      // Set default template
      if (invoiceTemplates.length > 0) {
        const defaultTemplate = invoiceTemplates.find(t => t.isDefault);
        if (defaultTemplate) {
          formData.templateId = defaultTemplate.id;
        }
      }

    } catch (error) {
      console.error('Error loading data:', error);
      addToast({ message: 'Failed to load data', type: 'error' });
    } finally {
      isLoadingData = false;
    }
  }

  function filterCustomers(searchTerm: string): Contact[] {
    if (!searchTerm.trim()) {
      return $customers;
    }
    const term = searchTerm.toLowerCase();
    return $customers.filter(customer =>
      customer.fullName.toLowerCase().includes(term) ||
      (customer.companyName && customer.companyName.toLowerCase().includes(term)) ||
      customer.emails.some(email => email.email.toLowerCase().includes(term))
    );
  }

  function handleCustomerSearch(event: Event) {
    const target = event.target as HTMLInputElement;
    customerSearch = target.value;
    showCustomerDropdown = true;
    highlightedIndex = -1;
    updateFormChanged();
  }

  function selectCustomer(customer: Contact) {
    formData = {
      ...formData,
      customerId: customer.id
    };
    customerSearch = customer.companyName || customer.fullName;
    showCustomerDropdown = false;
    updateFormChanged();

    // Load uninvoiced items for this customer
    loadUninvoicedItems(customer.id);
  }

  async function loadUninvoicedItems(customerId: string) {
    if (!customerId) {
      uninvoicedJobs = [];
      uninvoicedQuotes = [];
      return;
    }

    isLoadingUninvoiced = true;
    try {
      const [jobs, quotes] = await Promise.all([
        getUninvoicedJobs(customerId),
        getUninvoicedQuotes(customerId)
      ]);

      uninvoicedJobs = jobs;
      uninvoicedQuotes = quotes;

      // Show the section if there are uninvoiced items
      showUninvoicedSection = jobs.length > 0 || quotes.length > 0;
    } catch (error) {
      console.error('Error loading uninvoiced items:', error);
      addToast({ message: 'Failed to load uninvoiced items', type: 'error' });
    } finally {
      isLoadingUninvoiced = false;
    }
  }

  function addJobToInvoice(job: Job) {
    const newLineNumber = formData.invoiceLines.length + 1;
    const newLineItem: ApiInvoiceLine = {
      lineNumber: newLineNumber,
      description: `${job.title} - ${job.description}`,
      quantity: 1,
      unitPrice: job.estimatedCost?.totalCost || 0,
      discountType: 0,
      discountValue: 0,
      discountAmount: 0,
      subtotal: 0,
      taxRate: 0.1, // 10% as decimal
      taxAmount: 0,
      total: 0
    };

    formData = {
      ...formData,
      invoiceLines: [...formData.invoiceLines, newLineItem]
    };

    calculateTotals();
    addToast({ message: 'Job added to invoice', type: 'success' });
  }

  function addQuoteToInvoice(quote: Quote) {
    // Add each line item from the quote
    const newLineItems = quote.lineItems.map((item, index) => ({
      lineNumber: formData.invoiceLines.length + index + 1,
      description: item.description,
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      discountType: 0,
      discountValue: 0,
      discountAmount: 0,
      subtotal: 0,
      taxRate: item.taxRate / 100, // Convert percentage to decimal
      taxAmount: 0,
      total: 0
    }));

    formData = {
      ...formData,
      invoiceLines: [...formData.invoiceLines, ...newLineItems]
    };

    calculateTotals();
    addToast({ message: 'Quote items added to invoice', type: 'success' });
  }

  function toggleJobSelection(jobId: string) {
    const newSelectedJobIds = new Set(selectedJobIds);
    if (newSelectedJobIds.has(jobId)) {
      newSelectedJobIds.delete(jobId);
    } else {
      newSelectedJobIds.add(jobId);
    }
    selectedJobIds = newSelectedJobIds;
  }

  function toggleQuoteSelection(quoteId: string) {
    const newSelectedQuoteIds = new Set(selectedQuoteIds);
    if (newSelectedQuoteIds.has(quoteId)) {
      newSelectedQuoteIds.delete(quoteId);
    } else {
      newSelectedQuoteIds.add(quoteId);
    }
    selectedQuoteIds = newSelectedQuoteIds;
  }

  function selectAllJobs() {
    selectedJobIds = new Set(uninvoicedJobs.map(job => job.id));
  }

  function deselectAllJobs() {
    selectedJobIds = new Set();
  }

  function selectAllQuotes() {
    selectedQuoteIds = new Set(uninvoicedQuotes.map(quote => quote.id));
  }

  function deselectAllQuotes() {
    selectedQuoteIds = new Set();
  }

  function addSelectedJobsToInvoice() {
    const selectedJobs = uninvoicedJobs.filter(job => selectedJobIds.has(job.id));
    
    if (selectedJobs.length === 0) {
      addToast({ message: 'No jobs selected', type: 'warning' });
      return;
    }

    const newLineItems = selectedJobs.map((job, index) => ({
      lineNumber: formData.invoiceLines.length + index + 1,
      description: `${job.title} - ${job.description}`,
      quantity: 1,
      unitPrice: job.estimatedCost?.totalCost || 0,
      discountType: 0,
      discountValue: 0,
      discountAmount: 0,
      subtotal: 0,
      taxRate: 0.1, // 10% as decimal
      taxAmount: 0,
      total: 0
    }));

    formData = {
      ...formData,
      invoiceLines: [...formData.invoiceLines, ...newLineItems]
    };

    calculateTotals();
    selectedJobIds = new Set(); // Clear selection
    addToast({ 
      message: `${selectedJobs.length} job${selectedJobs.length !== 1 ? 's' : ''} added to invoice`, 
      type: 'success' 
    });
  }

  function addSelectedQuotesToInvoice() {
    const selectedQuotes = uninvoicedQuotes.filter(quote => selectedQuoteIds.has(quote.id));
    
    if (selectedQuotes.length === 0) {
      addToast({ message: 'No quotes selected', type: 'warning' });
      return;
    }

    const newLineItems: ApiInvoiceLine[] = [];
    let lineNumber = formData.invoiceLines.length + 1;
    
    selectedQuotes.forEach(quote => {
      quote.lineItems.forEach(item => {
        newLineItems.push({
          lineNumber: lineNumber++,
          description: item.description,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discountType: 0,
          discountValue: 0,
          discountAmount: 0,
          subtotal: 0,
          taxRate: item.taxRate / 100, // Convert percentage to decimal
          taxAmount: 0,
          total: 0
        });
      });
    });

    formData = {
      ...formData,
      invoiceLines: [...formData.invoiceLines, ...newLineItems]
    };

    calculateTotals();
    selectedQuoteIds = new Set(); // Clear selection
    addToast({ 
      message: `${selectedQuotes.length} quote${selectedQuotes.length !== 1 ? 's' : ''} added to invoice`, 
      type: 'success' 
    });
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (!showCustomerDropdown) return;

    const filteredCustomers = filterCustomers(customerSearch);

    if (event.key === 'ArrowDown') {
      event.preventDefault();
      highlightedIndex = Math.min(highlightedIndex + 1, filteredCustomers.length - 1);
    } else if (event.key === 'ArrowUp') {
      event.preventDefault();
      highlightedIndex = Math.max(highlightedIndex - 1, -1);
    } else if (event.key === 'Enter' && highlightedIndex >= 0) {
      event.preventDefault();
      selectCustomer(filteredCustomers[highlightedIndex]);
    } else if (event.key === 'Escape') {
      showCustomerDropdown = false;
    }
  }

  function handleInputBlur() {
    // Small delay to allow click events to fire on dropdown items
    setTimeout(() => {
      showCustomerDropdown = false;
    }, 200);
  }

  async function handleSubmit() {
    formSubmitted = true;
    calculateTotals();

    if (!validateForm()) {
      addToast({
        message: 'Please fix the errors in the form before submitting',
        type: 'error'
      });
      return;
    }

    isSaving = true;

    try {
      // Prepare the API invoice data
      const apiInvoice: ApiInvoice = {
        status: formData.status,
        issueDate: formData.issueDate,
        dueDate: formData.dueDate,
        notes: formData.notes,
        paymentTerms: formData.paymentTerms,
        invoiceLines: formData.invoiceLines
      };

      const savedInvoice = await saveInvoice(apiInvoice);

      addToast({
        message: 'Invoice created successfully',
        type: 'success'
      });

      // Navigate back to invoices list
      goto('/invoices');
    } catch (err) {
      console.error('Error creating invoice:', err);
      addToast({
        message: err instanceof Error ? err.message : 'An unknown error occurred',
        type: 'error'
      });
    } finally {
      isSaving = false;
    }
  }

  function cancelForm() {
    updateFormChanged();
    if (formChanged) {
      if (window.confirm('You have unsaved changes. Are you sure you want to cancel?')) {
        goto('/invoices');
      }
    } else {
      goto('/invoices');
    }
  }

  // Get calculated totals for display
  $: totals = calculateApiInvoiceTotals(formData.invoiceLines);
  $: totalWithDiscount = totals.totalAmount - formData.discountAmount;

  // Watch for changes to recalculate totals and update form state
  $: if (formData.invoiceLines) {
    calculateTotals();
  }

  // React to form data changes
  $: if (initialFormState) {
    updateFormChanged();
  }

  // Handle navigation confirmation
  beforeNavigate(({ cancel, to }) => {
    // Skip if we're saving or if the form hasn't changed
    if (isSaving) return;

    updateFormChanged();

    if (formChanged && to?.url.pathname !== window.location.pathname) {
      if (!window.confirm('You have unsaved changes. Are you sure you want to leave?')) {
        cancel();
      }
    }
  });

  onMount(async () => {
    await loadData();
    // Set initial form state after data is loaded
    setTimeout(() => {
      initialFormState = JSON.stringify(formData);
    }, 100);
  });

  // Reactive statement for filtered customers
  $: filteredCustomers = filterCustomers(customerSearch);
</script>

<svelte:head>
  <title>Create Invoice</title>
</svelte:head>

<div class="container">
  <PageHeader title="Create Invoice">
    <svelte:fragment slot="actions">
      <div class="save-status-container">
        {#if formChanged}
          <div class="save-status unsaved">
            <div class="status-dot"></div>
            Unsaved changes
          </div>
        {:else}
          <div class="save-status saved">
            <div class="status-dot"></div>
            All changes saved
          </div>
        {/if}
      </div>
    </svelte:fragment>
  </PageHeader>

  
  {#if isLoadingData}
    <div class="loading-container">
      <LoadingSpinner />
      <p>Loading invoice data...</p>
    </div>
  {:else}
    <form on:submit|preventDefault={handleSubmit} class="invoice-form">

      <div class="invoice-editor">
        
        <main class="editor-content">

        
      <!-- Customer Information -->
      <div class="form-section">
        <h2>Customer Information</h2>

        <CustomerSelect
          bind:customerId={formData.customerId}
          bind:customerSearch={customerSearch}
          hasError={formSubmitted && !!errors.customerId}
          errorMessage={formSubmitted && errors.customerId ? errors.customerId : ''}
          on:selectcustomer={(event) => loadUninvoicedItems(event.detail)}
        />
      </div>

      <!-- Invoice Header -->
      <div class="form-section">

        <div class="layout-row" style="grid-template-columns: 1fr 1fr;">
          
          <div class="layout-column">
            <div class="form-group">
                <label for="sellerDetails">Seller Details</label>
                <textarea id="sellerDetails">123 Fake Street, 
London, 
UK, 
W12 3AB
                </textarea>
            </div>
          </div>

          <div class="layout-column">
            
            <div class="form-group horizontal">
              <label for="invoiceNumber">Invoice Number</label>
              <input
                type="text"
                id="invoiceNumber"
                bind:value={formData.invoiceNumber}
                placeholder="Auto-generated"
                readonly
              />
            </div>
            
              <div class="form-group horizontal">
                <label for="issueDate">Issue Date</label>
                <input
                  type="date"
                  id="issueDate"
                  bind:value={formData.issueDate}
                  class:error={formSubmitted && errors.issueDate}
                />
                {#if formSubmitted && errors.issueDate}
                  <div class="error-message">{errors.issueDate}</div>
                {/if}
              </div>
    
              <div class="form-group horizontal">
                <label for="dueDate">Due Date</label>
                <input
                  type="date"
                  id="dueDate"
                  bind:value={formData.dueDate}
                  class:error={formSubmitted && errors.dueDate}
                />
                {#if formSubmitted && errors.dueDate}
                  <div class="error-message">{errors.dueDate}</div>
                {/if}
              </div>

              <!-- Custom Header Fields -->
          {#if formData.invoiceLines.length > 0}

            {#each formData.invoiceLines as item, index}
                <div class="form-group horizontal">
                  <input
                    type="text"
                    id="fieldLabel{index}"
                    bind:value={item.description}
                    placeholder="Item description"
                    class:error={formSubmitted && errors[`invoiceLines[${index}].description`]}
                  />
                  
                  <input
                    type="number"
                    id="fieldQuantity{index}"
                    bind:value={item.quantity}
                    min="1"
                    step="1"
                    on:input={() => calculateTotals()}
                    class:error={formSubmitted && errors[`invoiceLines[${index}].quantity`]}
                  />
                  
                  <input
                    type="number"
                    id="fieldUnitPrice{index}"
                    bind:value={item.unitPrice}
                    min="0"
                    step="0.01"
                    on:input={() => calculateTotals()}
                    class:error={formSubmitted && errors[`invoiceLines[${index}].unitPrice`]}
                  />
                  
                  <div class="item-cell tax">
                    <input
                      type="number"
                      value={item.taxRate * 100}
                      min="0"
                      max="100"
                      step="0.1"
                      on:input={(e) => {
                        const target = e.target as HTMLInputElement;
                        item.taxRate = parseFloat(target.value) / 100;
                        calculateTotals();
                      }}
                    />
                  </div>
                  
                  <div class="item-controls">
                    <Button
                      variant="tertiary"
                      size="small"
                      on:click={() => removeLineItem(index)}
                    >
                      Remove
                    </Button>
                  </div>
                </div>
            {/each}
          
        {/if}

          <div>
            <Button variant="secondary" size="small" on:click={addLineItem}>
              Add Item
            </Button>
          </div>
 
          </div>
   
        </div>
      </div>


      <!-- Uninvoiced Items -->
      {#if showUninvoicedSection && formData.customerId}
        <div class="form-section">
          <h2>Uninvoiced Items</h2>
          <p class="section-description">Add existing jobs and quotes to this invoice</p>

          {#if isLoadingUninvoiced}
            <div class="loading-uninvoiced">
              <LoadingSpinner />
              <span>Loading uninvoiced items...</span>
            </div>
          {:else}
            <div class="uninvoiced-container">
              <!-- Uninvoiced Jobs -->
              {#if uninvoicedJobs.length > 0}
                <div class="uninvoiced-section">
                  <div class="section-header">
                    <h3>Uninvoiced Jobs ({uninvoicedJobs.length})</h3>
                    <div class="bulk-actions">
                      <Button variant="tertiary" size="small" on:click={selectAllJobs}>
                        Select All
                      </Button>
                      <Button variant="tertiary" size="small" on:click={deselectAllJobs}>
                        Deselect All
                      </Button>
                      {#if selectedJobIds.size > 0}
                        <Button variant="primary" size="small" on:click={addSelectedJobsToInvoice}>
                          Add Selected ({selectedJobIds.size}) to Invoice
                        </Button>
                      {/if}
                    </div>
                  </div>
                  <div class="uninvoiced-items">
                    {#each uninvoicedJobs as job}
                      <div class="uninvoiced-item" class:selected={selectedJobIds.has(job.id)}>
                        <div class="item-checkbox">
                          <input
                            type="checkbox"
                            checked={selectedJobIds.has(job.id)}
                            on:change={() => toggleJobSelection(job.id)}
                          />
                        </div>
                        <div class="item-info">
                          <div class="item-title">{job.title}</div>
                          <div class="item-details">
                            <span class="item-type">{job.jobType?.name || 'Job'}</span>
                            <span class="item-date">Scheduled: {job.scheduledDateTime ? new Date(job.scheduledDateTime).toLocaleDateString() : 'Not scheduled'}</span>
                            <span class="item-cost">${job.estimatedCost?.totalCost?.toFixed(2) || '0.00'}</span>
                          </div>
                          {#if job.description}
                            <div class="item-description">{job.description}</div>
                          {/if}
                        </div>
                        <div class="item-actions">
                          <Button variant="secondary" size="small" on:click={() => addJobToInvoice(job)}>
                            Add to Invoice
                          </Button>
                        </div>
                      </div>
                    {/each}
                  </div>
                </div>
              {/if}

              <!-- Uninvoiced Quotes -->
              {#if uninvoicedQuotes.length > 0}
                <div class="uninvoiced-section">
                  <div class="section-header">
                    <h3>Uninvoiced Quotes ({uninvoicedQuotes.length})</h3>
                    <div class="bulk-actions">
                      <Button variant="tertiary" size="small" on:click={selectAllQuotes}>
                        Select All
                      </Button>
                      <Button variant="tertiary" size="small" on:click={deselectAllQuotes}>
                        Deselect All
                      </Button>
                      {#if selectedQuoteIds.size > 0}
                        <Button variant="primary" size="small" on:click={addSelectedQuotesToInvoice}>
                          Add Selected ({selectedQuoteIds.size}) to Invoice
                        </Button>
                      {/if}
                    </div>
                  </div>
                  <div class="uninvoiced-items">
                    {#each uninvoicedQuotes as quote}
                      <div class="uninvoiced-item" class:selected={selectedQuoteIds.has(quote.id)}>
                        <div class="item-checkbox">
                          <input
                            type="checkbox"
                            checked={selectedQuoteIds.has(quote.id)}
                            on:change={() => toggleQuoteSelection(quote.id)}
                          />
                        </div>
                        <div class="item-info">
                          <div class="item-title">Quote #{quote.quoteNumber}</div>
                          <div class="item-details">
                            <span class="item-date">Issued: {new Date(quote.issueDate).toLocaleDateString()}</span>
                            <span class="item-cost">${quote.totalAmount.toFixed(2)}</span>
                            <span class="item-status" class:accepted={quote.status.name === 'Accepted'}>
                              {quote.status.name}
                            </span>
                          </div>
                          <div class="item-description">{quote.lineItems.length} line item{quote.lineItems.length !== 1 ? 's' : ''}</div>
                        </div>
                        <div class="item-actions">
                          <Button variant="secondary" size="small" on:click={() => addQuoteToInvoice(quote)}>
                            Add to Invoice
                          </Button>
                        </div>
                      </div>
                    {/each}
                  </div>
                </div>
              {/if}

              {#if uninvoicedJobs.length === 0 && uninvoicedQuotes.length === 0}
                <div class="no-uninvoiced">
                  <p>No uninvoiced jobs or quotes found for this customer.</p>
                </div>
              {/if}
            </div>
          {/if}
        </div>
      {/if}

      <!-- Invoice Items -->
      <div class="form-section">
        <div class="layout-row">
          <div class="layout-column">
        <div class="items-table">
          <div class="items-header">
            <div class="item-cell">Description</div>
            <div class="item-cell">Quantity</div>
            <div class="item-cell">Unit Price</div>
            <div class="item-cell">Tax Rate (%)</div>
            <div class="item-cell line-total">Line Total</div>
            <div class="item-cell actions"></div>
          </div>

          {#each formData.invoiceLines as item, index}
            <div class="items-row">
              <div class="item-cell">
                <div class="item-description">
                  

                  <!-- Product select (conditionally shown) -->
                  {#if showProductSelect[index]}
                    <select
                      on:change={(e) => {
                        const target = e.target as HTMLSelectElement;
                        const productId = target.value;
                        if (productId) {
                          const product = products.find(p => p.id === productId);
                          if (product) selectProduct(index, product);
                        }
                      }}
                      class="product-select"
                    >
                      <option value="">Select product</option>
                      {#each products as product}
                        <option value={product.id}>{product.name}</option>
                      {/each}
                    </select><!-- Main description input -->
                    {:else}
                    <input
                      type="text"
                      bind:value={item.description}
                      placeholder="Item description"
                      class:error={formSubmitted && errors[`invoiceLines[${index}].description`]}
                    />
                  {/if}

                  

                  

                  <!-- Additional info textarea (conditionally shown) -->
                  {#if showAdditionalInfo[index]}
                    <textarea
                      placeholder="Additional info (optional)"
                      rows="2"
                      class="additional-info"
                    ></textarea>
                  {/if}

                  <!-- Toggle buttons -->
                  <div class="item-controls">
                    <Button
                      variant={showProductSelect[index] ? 'primary' : 'secondary'}
                      size="small"
                      on:click={() => toggleProductSelect(index)}
                    >
                      {showProductSelect[index] ? 'Type Manually' : 'Select From List'}
                    </Button>
                    <Button
                      variant={showAdditionalInfo[index] ? 'primary' : 'secondary'}
                      size="small"
                      on:click={() => toggleAdditionalInfo(index)}
                    >
                      {showAdditionalInfo[index] ? '✕ Hide Additional Info' : 'Additional Info'}
                    </Button>
                  </div>
                </div>

                {#if formSubmitted && errors[`invoiceLines[${index}].description`]}
                  <div class="error-message">{errors[`invoiceLines[${index}].description`]}</div>
                {/if}
              </div>

              <div class="item-cell">
                <input
                  type="number"
                  bind:value={item.quantity}
                  min="1"
                  step="1"
                  on:input={() => calculateTotals()}
                  class:error={formSubmitted && errors[`invoiceLines[${index}].quantity`]}
                />
                {#if formSubmitted && errors[`invoiceLines[${index}].quantity`]}
                  <div class="error-message">{errors[`invoiceLines[${index}].quantity`]}</div>
                {/if}
              </div>

              <div class="item-cell">
                <input
                  type="number"
                  bind:value={item.unitPrice}
                  min="0"
                  step="0.01"
                  on:input={() => calculateTotals()}
                  class:error={formSubmitted && errors[`invoiceLines[${index}].unitPrice`]}
                />
                {#if formSubmitted && errors[`invoiceLines[${index}].unitPrice`]}
                  <div class="error-message">{errors[`invoiceLines[${index}].unitPrice`]}</div>
                {/if}
              </div>

              <div class="item-cell">
                <input
                  type="number"
                  value={item.taxRate * 100}
                  min="0"
                  max="100"
                  step="0.1"
                  on:input={(e) => {
                    const target = e.target as HTMLInputElement;
                    item.taxRate = parseFloat(target.value) / 100;
                    calculateTotals();
                  }}
                />
              </div>

              <div class="item-cell amount">
                £{item.subtotal.toFixed(2)}
              </div>

              <div class="item-cell actions">
                {#if formData.invoiceLines.length > 1}
                  <Button
                    variant="tertiary"
                    size="small"
                    on:click={() => removeLineItem(index)}
                  >
                    Remove
                  </Button>
                {/if}

                {#if showAdditionalInfo[index]}
                  <div class="additional-info">
                    <textarea
                      placeholder="Additional information..."
                      rows="2"
                    ></textarea>
                  </div>
                {/if}
              </div>
            </div>
          {/each}

          <div class="add-item-row">
            <Button variant="secondary" on:click={addLineItem}>
              Add Item
            </Button>
          </div>
        </div>

        <div class="invoice-totals">
          <div class="totals-row">
            <div class="totals-label">Subtotal:</div>
            <div class="totals-value">£{totals.subtotal.toFixed(2)}</div>
          </div>

          <div class="totals-row">
            <div class="totals-label">
              <label for="discountAmount">Discount:</label>
            </div>
            <div class="totals-value">
              <input
                type="number"
                id="discountAmount"
                bind:value={formData.discountAmount}
                min="0"
                step="0.01"
                on:input={() => calculateTotals()}
                class="discount-input"
              />
            </div>
          </div>

          <div class="totals-row">
            <div class="totals-label">Tax:</div>
            <div class="totals-value">£{totals.taxAmount.toFixed(2)}</div>
          </div>

          <div class="totals-row total">
            <div class="totals-label">Total:</div>
            <div class="totals-value">£{totalWithDiscount.toFixed(2)}</div>
          </div>
        </div>
      </div>
    </div>
      </div>

      <!-- Additional Information -->
      <div class="form-section">
        <div class="layout-row" style="grid-template-columns: 1fr 1fr;">
          <div class="layout-column">
            <div class="form-group">
              <label for="notes">Notes</label>
              <textarea id="notes" bind:value={formData.notes} rows="3" placeholder="Notes to the customer"></textarea>
            </div>
          </div>
          <div class="layout-column">
            <div class="form-group">
              <label for="terms">Terms and Conditions</label>
              <textarea id="terms" bind:value={formData.paymentTerms} rows="3"></textarea>
            </div>
          </div>          
        </div>
      </div>

    </main>
    <div class="properties-panel">
      <div class="property-section">

        <div class="form-actions">
          <Button variant="tertiary" size="small" on:click={cancelForm} disabled={isSaving}>Cancel</Button>
          <Button type="submit" disabled={isSaving}>
            {#if isSaving}
              Saving...
            {:else}
              Save
            {/if}
          </Button>
        </div>
      <div class="form-group">
        <label for="status">Status</label>
        <select id="status" bind:value={formData.status}>
          {#each invoiceStatuses as status}
            <option value={getStatusNumber(status.name)}>{status.name}</option>
          {/each}
        </select>
      </div>

      <div class="form-group">
        <label for="template">Template</label>
        <select id="template" bind:value={formData.templateId}>
          <option value="">Default Template</option>
          {#each invoiceTemplates as template}
            <option value={template.id}>{template.name}</option>
          {/each}
        </select>
      </div>
    </div>
    </div>
  </div>
    </form>
  {/if}
  

</div>

<style lang="less">

  .invoice-editor {
    display: grid;
    grid-template-columns: 1fr 300px;
    flex: 1;
    overflow: hidden;

    .editor-content {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .properties-panel {
      background: linear-gradient(135deg, #f8f9fa 0%, #f1f3f4 100%);
      border-left: 1px solid var(--border);
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      box-shadow: inset -2px 0 8px rgba(0, 0, 0, 0.05);
      .property-section {
        margin-bottom: 2rem;
        border-radius: 12px;
        padding: 1.5rem;
      }
    }
    .layout-row {
      display: grid;
      grid-template-columns: 1fr;
      gap: 2rem;
      .layout-column {
        display: flex;
        flex-direction: column;
        gap:1rem;
        .form-group.horizontal {
          flex-direction: row;
          margin-bottom: 0;
          &>*:first-child {
            width: 130px;
            flex-shrink: 0;
            margin-bottom: 0;
            box-sizing: border-box;
            display: flex;
            align-items: center;
          }
        }
      }
    }
  }

  .save-status-container {
    display: flex;
    align-items: center;
    margin-right: 15px;
  }

  .save-status {
    display: flex;
    align-items: center;
    font-size: 14px;
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: 500;

    &.saved {
      color: var(--green);
      background-color: var(--green-fade);
    }

    &.unsaved {
      color: var(--orange, #f59e0b);
      background-color: var(--orange-fade, rgba(245, 158, 11, 0.1));
    }

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 8px;
    }

    &.saved .status-dot {
      background-color: var(--green);
    }

    &.unsaved .status-dot {
      background-color: var(--orange, #f59e0b);
    }
  }

  .form-section {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    padding: 20px;

    h2 {
      margin-top: 0;
      margin-bottom: 20px;
      font-size: 18px;
      color: var(--primary);
      border-bottom: 1px solid var(--border);
      padding-bottom: 10px;
    }
  }

  .form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 15px;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 10px;
    }
  }

  .items-table {
    border: 1px solid var(--border);
    border-radius: 4px;
    margin-bottom: 20px;
  }

  .items-header {
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 1fr 1fr 80px;
    background-color: var(--bg);
    border-bottom: 1px solid var(--border);
    font-weight: 500;
    color: var(--grey);

    .item-cell {
      padding: 10px;
      font-size: 14px;
      &.line-total {
        text-align: right;
      }
    }
  }

  .items-row {
    display: grid;
    grid-template-columns: 3fr 1fr 1fr 1fr 1fr 80px;
    border-bottom: 1px solid var(--border);
    align-items: flex-start;
    font-size: 14px;

    .item-cell {
      padding: 10px;

      &.amount {
        font-weight: 500;
        height: 56.5px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
      &.actions {
        height: 56.5px;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }

      input {
        width: 100%;
        padding: 8px;
        border: 1px solid var(--border);
        border-radius: 4px;

        &:focus {
          outline: none;
          border-color: var(--primary);
        }

        &.error {
          border-color: var(--red);
        }
      }
    }
  }

  .add-item-row {
    padding: 10px;
    text-align: left;
  }

  .invoice-totals {
    margin-left: auto;
    width: 300px;
    border-top: 1px solid var(--border);
    padding-top: 15px;
  }

  .totals-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;

    &.total {
      font-weight: 700;
      font-size: 18px;
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px solid var(--border);
    }
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(-5px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-bottom: 20px;
  }

  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    gap: 1rem;

    p {
      color: var(--grey);
    }
  }

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h2 {
      margin: 0;
    }
  }

  .item-description {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    .item-controls {
      display: flex;
      gap: 0.5rem;
    }

    .product-select {
      width: 100%;
      padding: 8px;
      border: 1px solid var(--border);
      border-radius: 4px;
      font-size: 0.9rem;
      background: white;

      &:focus {
        outline: none;
        border-color: var(--primary);
      }
    }

    .additional-info {
      width: 100%;
      padding: 8px;
      border: 1px solid var(--border);
      border-radius: 4px;
      font-size: 0.9rem;
      resize: vertical;
      font-family: inherit;

      &:focus {
        outline: none;
        border-color: var(--primary);
      }
    }
  }

  // Uninvoiced Items Styles
  .uninvoiced-container {
    border: 1px solid var(--border);
    border-radius: var(--br);
    background: var(--bg);
    padding: 1rem;
  }

  .uninvoiced-section {
    margin-bottom: 1.5rem;

    &:last-child {
      margin-bottom: 0;
    }

    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;

      h3 {
        margin: 0;
        font-size: 16px;
        color: var(--primary);
        font-weight: 600;
      }

      .bulk-actions {
        display: flex;
        gap: 0.5rem;
        align-items: center;
      }
    }
  }

  .uninvoiced-items {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .uninvoiced-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--border);
    border-radius: var(--br);
    background: white;
    transition: all 0.2s ease;

    &:hover {
      border-color: var(--primary);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &.selected {
      border-color: var(--primary);
      background: var(--primary-fade);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .item-checkbox {
      margin-right: 1rem;
      
      input[type="checkbox"] {
        width: 18px;
        height: 18px;
        cursor: pointer;
      }
    }
  }

  .item-info {
    flex: 1;

    .item-title {
      font-weight: 600;
      color: var(--text);
      margin-bottom: 0.25rem;
    }

    .item-details {
      display: flex;
      gap: 1rem;
      margin-bottom: 0.25rem;
      font-size: 0.9rem;

      .item-type {
        color: var(--primary);
        font-weight: 500;
      }

      .item-date {
        color: var(--grey);
      }

      .item-cost {
        color: var(--green);
        font-weight: 600;
      }

      .item-status {
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 500;
        background: var(--grey-light);
        color: var(--grey);

        &.accepted {
          background: var(--green-light);
          color: var(--green);
        }
      }
    }

    .item-description {
      color: var(--grey);
      font-size: 0.9rem;
      line-height: 1.4;
    }
  }

  .item-actions {
    flex-shrink: 0;
  }

  .loading-uninvoiced {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 2rem;
    justify-content: center;
    color: var(--grey);
  }

  .no-uninvoiced {
    text-align: center;
    padding: 2rem;
    color: var(--grey);
    font-style: italic;
  }

  .section-description {
    color: var(--grey);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    margin-top: -0.5rem;
  }

  .discount-input {
    width: 100px;
    padding: 8px;
    border: 1px solid var(--border);
    border-radius: 4px;
    text-align: right;

    &:focus {
      outline: none;
      border-color: var(--primary);
    }
  }

  @media (max-width: 768px) {
    .custom-field-row {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }

    .items-header,
    .items-row {
      grid-template-columns: 1fr;
      gap: 0.5rem;
    }

    .item-cell {
      padding: 0.5rem;
    }
  }
</style>
