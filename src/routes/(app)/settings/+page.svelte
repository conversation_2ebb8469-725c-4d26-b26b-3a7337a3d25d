<script lang="ts">
	import VerticalMenu from '$lib/components/VerticalMenu.svelte';
	import GeneralSettings from '$lib/components/settings/GeneralSettings.svelte';
	import BrandSettings from '$lib/components/settings/BrandSettings.svelte';
	import PageHeader from '$lib/components/PageHeader.svelte';

	let activeSetting = 'general'; // Default to 'general'

	const menuItems = [
		{ id: 'general', label: 'General' },
		{ id: 'brand', label: 'Brand' }
	];

	function handleMenuSelect(itemId: string) {
		activeSetting = itemId;
	}
</script>

<svelte:head>
	<title>Settings</title>
</svelte:head>


<div class="container">
	<PageHeader title="Settings" />
	<main>
		<div class="vertical-sidebar-layout">
			<VerticalMenu items={menuItems} currentItem={activeSetting} onSelect={handleMenuSelect} />
		<div class="vertical-sidebar-content">
			{#if activeSetting === 'general'}
				<GeneralSettings />
			{:else if activeSetting === 'brand'}
				<BrandSettings />
			{/if}
			</div>
		</div>
	</main>
</div>

<style>

</style>
