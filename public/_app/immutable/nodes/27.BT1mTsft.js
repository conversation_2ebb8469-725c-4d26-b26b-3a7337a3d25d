var De=Object.defineProperty;var Ze=(n,e,t)=>e in n?De(n,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):n[e]=t;var k=(n,e,t)=>Ze(n,typeof e!="symbol"?e+"":e,t);import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as Me,g as Ge,ae as Oe,i as A,s as se,k as R,m as Z,j as _,r as L,t as je}from"../chunks/p3DoyA09.js";import{h as Ne,s as He}from"../chunks/BUelSUke.js";import{i as W}from"../chunks/DwdToawP.js";import{h as Qe}from"../chunks/FLKOLPZJ.js";import{t as N,a as M}from"../chunks/B67foYpL.js";import{i as Fe}from"../chunks/D3pqaimu.js";import{o as Ue}from"../chunks/C_WNR8j8.js";import{P as We}from"../chunks/CC9utfo3.js";import{V as Xe}from"../chunks/Bg6zarrZ.js";function ce(){return{async:!1,breaks:!1,extensions:null,gfm:!0,hooks:null,pedantic:!1,renderer:null,silent:!1,tokenizer:null,walkTokens:null}}var C=ce();function Se(n){C=n}var j={exec:()=>null};function f(n,e=""){let t=typeof n=="string"?n:n.source;const r={replace:(s,i)=>{let c=typeof i=="string"?i:i.source;return c=c.replace(w.caret,"$1"),t=t.replace(s,c),r},getRegex:()=>new RegExp(t,e)};return r}var w={codeRemoveIndent:/^(?: {1,4}| {0,3}\t)/gm,outputLinkReplace:/\\([\[\]])/g,indentCodeCompensation:/^(\s+)(?:```)/,beginningSpace:/^\s+/,endingHash:/#$/,startingSpaceChar:/^ /,endingSpaceChar:/ $/,nonSpaceChar:/[^ ]/,newLineCharGlobal:/\n/g,tabCharGlobal:/\t/g,multipleSpaceGlobal:/\s+/g,blankLine:/^[ \t]*$/,doubleBlankLine:/\n[ \t]*\n[ \t]*$/,blockquoteStart:/^ {0,3}>/,blockquoteSetextReplace:/\n {0,3}((?:=+|-+) *)(?=\n|$)/g,blockquoteSetextReplace2:/^ {0,3}>[ \t]?/gm,listReplaceTabs:/^\t+/,listReplaceNesting:/^ {1,4}(?=( {4})*[^ ])/g,listIsTask:/^\[[ xX]\] /,listReplaceTask:/^\[[ xX]\] +/,anyLine:/\n.*\n/,hrefBrackets:/^<(.*)>$/,tableDelimiter:/[:|]/,tableAlignChars:/^\||\| *$/g,tableRowBlankLine:/\n[ \t]*$/,tableAlignRight:/^ *-+: *$/,tableAlignCenter:/^ *:-+: *$/,tableAlignLeft:/^ *:-+ *$/,startATag:/^<a /i,endATag:/^<\/a>/i,startPreScriptTag:/^<(pre|code|kbd|script)(\s|>)/i,endPreScriptTag:/^<\/(pre|code|kbd|script)(\s|>)/i,startAngleBracket:/^</,endAngleBracket:/>$/,pedanticHrefTitle:/^([^'"]*[^\s])\s+(['"])(.*)\2/,unicodeAlphaNumeric:/[\p{L}\p{N}]/u,escapeTest:/[&<>"']/,escapeReplace:/[&<>"']/g,escapeTestNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/,escapeReplaceNoEncode:/[<>"']|&(?!(#\d{1,7}|#[Xx][a-fA-F0-9]{1,6}|\w+);)/g,unescapeTest:/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/ig,caret:/(^|[^\[])\^/g,percentDecode:/%25/g,findPipe:/\|/g,splitPipe:/ \|/,slashPipe:/\\\|/g,carriageReturn:/\r\n|\r/g,spaceLine:/^ +$/gm,notSpaceStart:/^\S*/,endingNewline:/\n$/,listItemRegex:n=>new RegExp(`^( {0,3}${n})((?:[	 ][^\\n]*)?(?:\\n|$))`),nextBulletRegex:n=>new RegExp(`^ {0,${Math.min(3,n-1)}}(?:[*+-]|\\d{1,9}[.)])((?:[ 	][^\\n]*)?(?:\\n|$))`),hrRegex:n=>new RegExp(`^ {0,${Math.min(3,n-1)}}((?:- *){3,}|(?:_ *){3,}|(?:\\* *){3,})(?:\\n+|$)`),fencesBeginRegex:n=>new RegExp(`^ {0,${Math.min(3,n-1)}}(?:\`\`\`|~~~)`),headingBeginRegex:n=>new RegExp(`^ {0,${Math.min(3,n-1)}}#`),htmlBeginRegex:n=>new RegExp(`^ {0,${Math.min(3,n-1)}}<(?:[a-z].*>|!--)`,"i")},Ve=/^(?:[ \t]*(?:\n|$))+/,Je=/^((?: {4}| {0,3}\t)[^\n]+(?:\n(?:[ \t]*(?:\n|$))*)?)+/,Ke=/^ {0,3}(`{3,}(?=[^`\n]*(?:\n|$))|~{3,})([^\n]*)(?:\n|$)(?:|([\s\S]*?)(?:\n|$))(?: {0,3}\1[~`]* *(?=\n|$)|$)/,H=/^ {0,3}((?:-[\t ]*){3,}|(?:_[ \t]*){3,}|(?:\*[ \t]*){3,})(?:\n+|$)/,Ye=/^ {0,3}(#{1,6})(?=\s|$)(.*)(?:\n+|$)/,he=/(?:[*+-]|\d{1,9}[.)])/,$e=/^(?!bull |blockCode|fences|blockquote|heading|html|table)((?:.|\n(?!\s*?\n|bull |blockCode|fences|blockquote|heading|html|table))+?)\n {0,3}(=+|-+) *(?:\n+|$)/,Re=f($e).replace(/bull/g,he).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/\|table/g,"").getRegex(),et=f($e).replace(/bull/g,he).replace(/blockCode/g,/(?: {4}| {0,3}\t)/).replace(/fences/g,/ {0,3}(?:`{3,}|~{3,})/).replace(/blockquote/g,/ {0,3}>/).replace(/heading/g,/ {0,3}#{1,6}/).replace(/html/g,/ {0,3}<[^\n>]+>\n/).replace(/table/g,/ {0,3}\|?(?:[:\- ]*\|)+[\:\- ]*\n/).getRegex(),pe=/^([^\n]+(?:\n(?!hr|heading|lheading|blockquote|fences|list|html|table| +\n)[^\n]+)*)/,tt=/^[^\n]+/,ue=/(?!\s*\])(?:\\.|[^\[\]\\])+/,nt=f(/^ {0,3}\[(label)\]: *(?:\n[ \t]*)?([^<\s][^\s]*|<.*?>)(?:(?: +(?:\n[ \t]*)?| *\n[ \t]*)(title))? *(?:\n+|$)/).replace("label",ue).replace("title",/(?:"(?:\\"?|[^"\\])*"|'[^'\n]*(?:\n[^'\n]+)*\n?'|\([^()]*\))/).getRegex(),rt=f(/^( {0,3}bull)([ \t][^\n]+?)?(?:\n|$)/).replace(/bull/g,he).getRegex(),ee="address|article|aside|base|basefont|blockquote|body|caption|center|col|colgroup|dd|details|dialog|dir|div|dl|dt|fieldset|figcaption|figure|footer|form|frame|frameset|h[1-6]|head|header|hr|html|iframe|legend|li|link|main|menu|menuitem|meta|nav|noframes|ol|optgroup|option|p|param|search|section|summary|table|tbody|td|tfoot|th|thead|title|tr|track|ul",fe=/<!--(?:-?>|[\s\S]*?(?:-->|$))/,st=f("^ {0,3}(?:<(script|pre|style|textarea)[\\s>][\\s\\S]*?(?:</\\1>[^\\n]*\\n+|$)|comment[^\\n]*(\\n+|$)|<\\?[\\s\\S]*?(?:\\?>\\n*|$)|<![A-Z][\\s\\S]*?(?:>\\n*|$)|<!\\[CDATA\\[[\\s\\S]*?(?:\\]\\]>\\n*|$)|</?(tag)(?: +|\\n|/?>)[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|<(?!script|pre|style|textarea)([a-z][\\w-]*)(?:attribute)*? */?>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$)|</(?!script|pre|style|textarea)[a-z][\\w-]*\\s*>(?=[ \\t]*(?:\\n|$))[\\s\\S]*?(?:(?:\\n[ 	]*)+\\n|$))","i").replace("comment",fe).replace("tag",ee).replace("attribute",/ +[a-zA-Z:_][\w.:-]*(?: *= *"[^"\n]*"| *= *'[^'\n]*'| *= *[^\s"'=<>`]+)?/).getRegex(),_e=f(pe).replace("hr",H).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("|table","").replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ee).getRegex(),it=f(/^( {0,3}> ?(paragraph|[^\n]*)(?:\n|$))+/).replace("paragraph",_e).getRegex(),ge={blockquote:it,code:Je,def:nt,fences:Ke,heading:Ye,hr:H,html:st,lheading:Re,list:rt,newline:Ve,paragraph:_e,table:j,text:tt},me=f("^ *([^\\n ].*)\\n {0,3}((?:\\| *)?:?-+:? *(?:\\| *:?-+:? *)*(?:\\| *)?)(?:\\n((?:(?! *\\n|hr|heading|blockquote|code|fences|list|html).*(?:\\n|$))*)\\n*|$)").replace("hr",H).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("blockquote"," {0,3}>").replace("code","(?: {4}| {0,3}	)[^\\n]").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ee).getRegex(),lt={...ge,lheading:et,table:me,paragraph:f(pe).replace("hr",H).replace("heading"," {0,3}#{1,6}(?:\\s|$)").replace("|lheading","").replace("table",me).replace("blockquote"," {0,3}>").replace("fences"," {0,3}(?:`{3,}(?=[^`\\n]*\\n)|~{3,})[^\\n]*\\n").replace("list"," {0,3}(?:[*+-]|1[.)]) ").replace("html","</?(?:tag)(?: +|\\n|/?>)|<(?:script|pre|style|textarea|!--)").replace("tag",ee).getRegex()},at={...ge,html:f(`^ *(?:comment *(?:\\n|\\s*$)|<(tag)[\\s\\S]+?</\\1> *(?:\\n{2,}|\\s*$)|<tag(?:"[^"]*"|'[^']*'|\\s[^'"/>\\s]*)*?/?> *(?:\\n{2,}|\\s*$))`).replace("comment",fe).replace(/tag/g,"(?!(?:a|em|strong|small|s|cite|q|dfn|abbr|data|time|code|var|samp|kbd|sub|sup|i|b|u|mark|ruby|rt|rp|bdi|bdo|span|br|wbr|ins|del|img)\\b)\\w+(?!:|[^\\w\\s@]*@)\\b").getRegex(),def:/^ *\[([^\]]+)\]: *<?([^\s>]+)>?(?: +(["(][^\n]+[")]))? *(?:\n+|$)/,heading:/^(#{1,6})(.*)(?:\n+|$)/,fences:j,lheading:/^(.+?)\n {0,3}(=+|-+) *(?:\n+|$)/,paragraph:f(pe).replace("hr",H).replace("heading",` *#{1,6} *[^
]`).replace("lheading",Re).replace("|table","").replace("blockquote"," {0,3}>").replace("|fences","").replace("|list","").replace("|html","").replace("|tag","").getRegex()},ot=/^\\([!"#$%&'()*+,\-./:;<=>?@\[\]\\^_`{|}~])/,ct=/^(`+)([^`]|[^`][\s\S]*?[^`])\1(?!`)/,Te=/^( {2,}|\\)\n(?!\s*$)/,ht=/^(`+|[^`])(?:(?= {2,}\n)|[\s\S]*?(?:(?=[\\<!\[`*_]|\b_|$)|[^ ](?= {2,}\n)))/,te=/[\p{P}\p{S}]/u,de=/[\s\p{P}\p{S}]/u,ze=/[^\s\p{P}\p{S}]/u,pt=f(/^((?![*_])punctSpace)/,"u").replace(/punctSpace/g,de).getRegex(),Ae=/(?!~)[\p{P}\p{S}]/u,ut=/(?!~)[\s\p{P}\p{S}]/u,ft=/(?:[^\s\p{P}\p{S}]|~)/u,gt=/\[[^[\]]*?\]\((?:\\.|[^\\\(\)]|\((?:\\.|[^\\\(\)])*\))*\)|`[^`]*?`|<[^<>]*?>/g,Le=/^(?:\*+(?:((?!\*)punct)|[^\s*]))|^_+(?:((?!_)punct)|([^\s_]))/,dt=f(Le,"u").replace(/punct/g,te).getRegex(),kt=f(Le,"u").replace(/punct/g,Ae).getRegex(),Pe="^[^_*]*?__[^_*]*?\\*[^_*]*?(?=__)|[^*]+(?=[^*])|(?!\\*)punct(\\*+)(?=[\\s]|$)|notPunctSpace(\\*+)(?!\\*)(?=punctSpace|$)|(?!\\*)punctSpace(\\*+)(?=notPunctSpace)|[\\s](\\*+)(?!\\*)(?=punct)|(?!\\*)punct(\\*+)(?!\\*)(?=punct)|notPunctSpace(\\*+)(?=notPunctSpace)",bt=f(Pe,"gu").replace(/notPunctSpace/g,ze).replace(/punctSpace/g,de).replace(/punct/g,te).getRegex(),mt=f(Pe,"gu").replace(/notPunctSpace/g,ft).replace(/punctSpace/g,ut).replace(/punct/g,Ae).getRegex(),xt=f("^[^_*]*?\\*\\*[^_*]*?_[^_*]*?(?=\\*\\*)|[^_]+(?=[^_])|(?!_)punct(_+)(?=[\\s]|$)|notPunctSpace(_+)(?!_)(?=punctSpace|$)|(?!_)punctSpace(_+)(?=notPunctSpace)|[\\s](_+)(?!_)(?=punct)|(?!_)punct(_+)(?!_)(?=punct)","gu").replace(/notPunctSpace/g,ze).replace(/punctSpace/g,de).replace(/punct/g,te).getRegex(),wt=f(/\\(punct)/,"gu").replace(/punct/g,te).getRegex(),vt=f(/^<(scheme:[^\s\x00-\x1f<>]*|email)>/).replace("scheme",/[a-zA-Z][a-zA-Z0-9+.-]{1,31}/).replace("email",/[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+(@)[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+(?![-_])/).getRegex(),yt=f(fe).replace("(?:-->|$)","-->").getRegex(),St=f("^comment|^</[a-zA-Z][\\w:-]*\\s*>|^<[a-zA-Z][\\w-]*(?:attribute)*?\\s*/?>|^<\\?[\\s\\S]*?\\?>|^<![a-zA-Z]+\\s[\\s\\S]*?>|^<!\\[CDATA\\[[\\s\\S]*?\\]\\]>").replace("comment",yt).replace("attribute",/\s+[a-zA-Z:_][\w.:-]*(?:\s*=\s*"[^"]*"|\s*=\s*'[^']*'|\s*=\s*[^\s"'=<>`]+)?/).getRegex(),J=/(?:\[(?:\\.|[^\[\]\\])*\]|\\.|`[^`]*`|[^\[\]\\`])*?/,$t=f(/^!?\[(label)\]\(\s*(href)(?:(?:[ \t]*(?:\n[ \t]*)?)(title))?\s*\)/).replace("label",J).replace("href",/<(?:\\.|[^\n<>\\])+>|[^ \t\n\x00-\x1f]*/).replace("title",/"(?:\\"?|[^"\\])*"|'(?:\\'?|[^'\\])*'|\((?:\\\)?|[^)\\])*\)/).getRegex(),Ce=f(/^!?\[(label)\]\[(ref)\]/).replace("label",J).replace("ref",ue).getRegex(),Ie=f(/^!?\[(ref)\](?:\[\])?/).replace("ref",ue).getRegex(),Rt=f("reflink|nolink(?!\\()","g").replace("reflink",Ce).replace("nolink",Ie).getRegex(),ke={_backpedal:j,anyPunctuation:wt,autolink:vt,blockSkip:gt,br:Te,code:ct,del:j,emStrongLDelim:dt,emStrongRDelimAst:bt,emStrongRDelimUnd:xt,escape:ot,link:$t,nolink:Ie,punctuation:pt,reflink:Ce,reflinkSearch:Rt,tag:St,text:ht,url:j},_t={...ke,link:f(/^!?\[(label)\]\((.*?)\)/).replace("label",J).getRegex(),reflink:f(/^!?\[(label)\]\s*\[([^\]]*)\]/).replace("label",J).getRegex()},le={...ke,emStrongRDelimAst:mt,emStrongLDelim:kt,url:f(/^((?:ftp|https?):\/\/|www\.)(?:[a-zA-Z0-9\-]+\.?)+[^\s<]*|^email/,"i").replace("email",/[A-Za-z0-9._+-]+(@)[a-zA-Z0-9-_]+(?:\.[a-zA-Z0-9-_]*[a-zA-Z0-9])+(?![-_])/).getRegex(),_backpedal:/(?:[^?!.,:;*_'"~()&]+|\([^)]*\)|&(?![a-zA-Z0-9]+;$)|[?!.,:;*_'"~)]+(?!$))+/,del:/^(~~?)(?=[^\s~])((?:\\.|[^\\])*?(?:\\.|[^\s~\\]))\1(?=[^~]|$)/,text:/^([`~]+|[^`~])(?:(?= {2,}\n)|(?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)|[\s\S]*?(?:(?=[\\<!\[`*~_]|\b_|https?:\/\/|ftp:\/\/|www\.|$)|[^ ](?= {2,}\n)|[^a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-](?=[a-zA-Z0-9.!#$%&'*+\/=?_`{\|}~-]+@)))/},Tt={...le,br:f(Te).replace("{2,}","*").getRegex(),text:f(le.text).replace("\\b_","\\b_| {2,}\\n").replace(/\{2,\}/g,"*").getRegex()},X={normal:ge,gfm:lt,pedantic:at},G={normal:ke,gfm:le,breaks:Tt,pedantic:_t},zt={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},xe=n=>zt[n];function $(n,e){if(e){if(w.escapeTest.test(n))return n.replace(w.escapeReplace,xe)}else if(w.escapeTestNoEncode.test(n))return n.replace(w.escapeReplaceNoEncode,xe);return n}function we(n){try{n=encodeURI(n).replace(w.percentDecode,"%")}catch{return null}return n}function ve(n,e){var i;const t=n.replace(w.findPipe,(c,l,h)=>{let a=!1,o=l;for(;--o>=0&&h[o]==="\\";)a=!a;return a?"|":" |"}),r=t.split(w.splitPipe);let s=0;if(r[0].trim()||r.shift(),r.length>0&&!((i=r.at(-1))!=null&&i.trim())&&r.pop(),e)if(r.length>e)r.splice(e);else for(;r.length<e;)r.push("");for(;s<r.length;s++)r[s]=r[s].trim().replace(w.slashPipe,"|");return r}function O(n,e,t){const r=n.length;if(r===0)return"";let s=0;for(;s<r&&n.charAt(r-s-1)===e;)s++;return n.slice(0,r-s)}function At(n,e){if(n.indexOf(e[1])===-1)return-1;let t=0;for(let r=0;r<n.length;r++)if(n[r]==="\\")r++;else if(n[r]===e[0])t++;else if(n[r]===e[1]&&(t--,t<0))return r;return t>0?-2:-1}function ye(n,e,t,r,s){const i=e.href,c=e.title||null,l=n[1].replace(s.other.outputLinkReplace,"$1");r.state.inLink=!0;const h={type:n[0].charAt(0)==="!"?"image":"link",raw:t,href:i,title:c,text:l,tokens:r.inlineTokens(l)};return r.state.inLink=!1,h}function Lt(n,e,t){const r=n.match(t.other.indentCodeCompensation);if(r===null)return e;const s=r[1];return e.split(`
`).map(i=>{const c=i.match(t.other.beginningSpace);if(c===null)return i;const[l]=c;return l.length>=s.length?i.slice(s.length):i}).join(`
`)}var K=class{constructor(n){k(this,"options");k(this,"rules");k(this,"lexer");this.options=n||C}space(n){const e=this.rules.block.newline.exec(n);if(e&&e[0].length>0)return{type:"space",raw:e[0]}}code(n){const e=this.rules.block.code.exec(n);if(e){const t=e[0].replace(this.rules.other.codeRemoveIndent,"");return{type:"code",raw:e[0],codeBlockStyle:"indented",text:this.options.pedantic?t:O(t,`
`)}}}fences(n){const e=this.rules.block.fences.exec(n);if(e){const t=e[0],r=Lt(t,e[3]||"",this.rules);return{type:"code",raw:t,lang:e[2]?e[2].trim().replace(this.rules.inline.anyPunctuation,"$1"):e[2],text:r}}}heading(n){const e=this.rules.block.heading.exec(n);if(e){let t=e[2].trim();if(this.rules.other.endingHash.test(t)){const r=O(t,"#");(this.options.pedantic||!r||this.rules.other.endingSpaceChar.test(r))&&(t=r.trim())}return{type:"heading",raw:e[0],depth:e[1].length,text:t,tokens:this.lexer.inline(t)}}}hr(n){const e=this.rules.block.hr.exec(n);if(e)return{type:"hr",raw:O(e[0],`
`)}}blockquote(n){const e=this.rules.block.blockquote.exec(n);if(e){let t=O(e[0],`
`).split(`
`),r="",s="";const i=[];for(;t.length>0;){let c=!1;const l=[];let h;for(h=0;h<t.length;h++)if(this.rules.other.blockquoteStart.test(t[h]))l.push(t[h]),c=!0;else if(!c)l.push(t[h]);else break;t=t.slice(h);const a=l.join(`
`),o=a.replace(this.rules.other.blockquoteSetextReplace,`
    $1`).replace(this.rules.other.blockquoteSetextReplace2,"");r=r?`${r}
${a}`:a,s=s?`${s}
${o}`:o;const g=this.lexer.state.top;if(this.lexer.state.top=!0,this.lexer.blockTokens(o,i,!0),this.lexer.state.top=g,t.length===0)break;const p=i.at(-1);if((p==null?void 0:p.type)==="code")break;if((p==null?void 0:p.type)==="blockquote"){const b=p,u=b.raw+`
`+t.join(`
`),x=this.blockquote(u);i[i.length-1]=x,r=r.substring(0,r.length-b.raw.length)+x.raw,s=s.substring(0,s.length-b.text.length)+x.text;break}else if((p==null?void 0:p.type)==="list"){const b=p,u=b.raw+`
`+t.join(`
`),x=this.list(u);i[i.length-1]=x,r=r.substring(0,r.length-p.raw.length)+x.raw,s=s.substring(0,s.length-b.raw.length)+x.raw,t=u.substring(i.at(-1).raw.length).split(`
`);continue}}return{type:"blockquote",raw:r,tokens:i,text:s}}}list(n){let e=this.rules.block.list.exec(n);if(e){let t=e[1].trim();const r=t.length>1,s={type:"list",raw:"",ordered:r,start:r?+t.slice(0,-1):"",loose:!1,items:[]};t=r?`\\d{1,9}\\${t.slice(-1)}`:`\\${t}`,this.options.pedantic&&(t=r?t:"[*+-]");const i=this.rules.other.listItemRegex(t);let c=!1;for(;n;){let h=!1,a="",o="";if(!(e=i.exec(n))||this.rules.block.hr.test(n))break;a=e[0],n=n.substring(a.length);let g=e[2].split(`
`,1)[0].replace(this.rules.other.listReplaceTabs,B=>" ".repeat(3*B.length)),p=n.split(`
`,1)[0],b=!g.trim(),u=0;if(this.options.pedantic?(u=2,o=g.trimStart()):b?u=e[1].length+1:(u=e[2].search(this.rules.other.nonSpaceChar),u=u>4?1:u,o=g.slice(u),u+=e[1].length),b&&this.rules.other.blankLine.test(p)&&(a+=p+`
`,n=n.substring(p.length+1),h=!0),!h){const B=this.rules.other.nextBulletRegex(u),F=this.rules.other.hrRegex(u),m=this.rules.other.fencesBeginRegex(u),v=this.rules.other.headingBeginRegex(u),S=this.rules.other.htmlBeginRegex(u);for(;n;){const E=n.split(`
`,1)[0];let y;if(p=E,this.options.pedantic?(p=p.replace(this.rules.other.listReplaceNesting,"  "),y=p):y=p.replace(this.rules.other.tabCharGlobal,"    "),m.test(p)||v.test(p)||S.test(p)||B.test(p)||F.test(p))break;if(y.search(this.rules.other.nonSpaceChar)>=u||!p.trim())o+=`
`+y.slice(u);else{if(b||g.replace(this.rules.other.tabCharGlobal,"    ").search(this.rules.other.nonSpaceChar)>=4||m.test(g)||v.test(g)||F.test(g))break;o+=`
`+p}!b&&!p.trim()&&(b=!0),a+=E+`
`,n=n.substring(E.length+1),g=y.slice(u)}}s.loose||(c?s.loose=!0:this.rules.other.doubleBlankLine.test(a)&&(c=!0));let x=null,Q;this.options.gfm&&(x=this.rules.other.listIsTask.exec(o),x&&(Q=x[0]!=="[ ] ",o=o.replace(this.rules.other.listReplaceTask,""))),s.items.push({type:"list_item",raw:a,task:!!x,checked:Q,loose:!1,text:o,tokens:[]}),s.raw+=a}const l=s.items.at(-1);if(l)l.raw=l.raw.trimEnd(),l.text=l.text.trimEnd();else return;s.raw=s.raw.trimEnd();for(let h=0;h<s.items.length;h++)if(this.lexer.state.top=!1,s.items[h].tokens=this.lexer.blockTokens(s.items[h].text,[]),!s.loose){const a=s.items[h].tokens.filter(g=>g.type==="space"),o=a.length>0&&a.some(g=>this.rules.other.anyLine.test(g.raw));s.loose=o}if(s.loose)for(let h=0;h<s.items.length;h++)s.items[h].loose=!0;return s}}html(n){const e=this.rules.block.html.exec(n);if(e)return{type:"html",block:!0,raw:e[0],pre:e[1]==="pre"||e[1]==="script"||e[1]==="style",text:e[0]}}def(n){const e=this.rules.block.def.exec(n);if(e){const t=e[1].toLowerCase().replace(this.rules.other.multipleSpaceGlobal," "),r=e[2]?e[2].replace(this.rules.other.hrefBrackets,"$1").replace(this.rules.inline.anyPunctuation,"$1"):"",s=e[3]?e[3].substring(1,e[3].length-1).replace(this.rules.inline.anyPunctuation,"$1"):e[3];return{type:"def",tag:t,raw:e[0],href:r,title:s}}}table(n){var c;const e=this.rules.block.table.exec(n);if(!e||!this.rules.other.tableDelimiter.test(e[2]))return;const t=ve(e[1]),r=e[2].replace(this.rules.other.tableAlignChars,"").split("|"),s=(c=e[3])!=null&&c.trim()?e[3].replace(this.rules.other.tableRowBlankLine,"").split(`
`):[],i={type:"table",raw:e[0],header:[],align:[],rows:[]};if(t.length===r.length){for(const l of r)this.rules.other.tableAlignRight.test(l)?i.align.push("right"):this.rules.other.tableAlignCenter.test(l)?i.align.push("center"):this.rules.other.tableAlignLeft.test(l)?i.align.push("left"):i.align.push(null);for(let l=0;l<t.length;l++)i.header.push({text:t[l],tokens:this.lexer.inline(t[l]),header:!0,align:i.align[l]});for(const l of s)i.rows.push(ve(l,i.header.length).map((h,a)=>({text:h,tokens:this.lexer.inline(h),header:!1,align:i.align[a]})));return i}}lheading(n){const e=this.rules.block.lheading.exec(n);if(e)return{type:"heading",raw:e[0],depth:e[2].charAt(0)==="="?1:2,text:e[1],tokens:this.lexer.inline(e[1])}}paragraph(n){const e=this.rules.block.paragraph.exec(n);if(e){const t=e[1].charAt(e[1].length-1)===`
`?e[1].slice(0,-1):e[1];return{type:"paragraph",raw:e[0],text:t,tokens:this.lexer.inline(t)}}}text(n){const e=this.rules.block.text.exec(n);if(e)return{type:"text",raw:e[0],text:e[0],tokens:this.lexer.inline(e[0])}}escape(n){const e=this.rules.inline.escape.exec(n);if(e)return{type:"escape",raw:e[0],text:e[1]}}tag(n){const e=this.rules.inline.tag.exec(n);if(e)return!this.lexer.state.inLink&&this.rules.other.startATag.test(e[0])?this.lexer.state.inLink=!0:this.lexer.state.inLink&&this.rules.other.endATag.test(e[0])&&(this.lexer.state.inLink=!1),!this.lexer.state.inRawBlock&&this.rules.other.startPreScriptTag.test(e[0])?this.lexer.state.inRawBlock=!0:this.lexer.state.inRawBlock&&this.rules.other.endPreScriptTag.test(e[0])&&(this.lexer.state.inRawBlock=!1),{type:"html",raw:e[0],inLink:this.lexer.state.inLink,inRawBlock:this.lexer.state.inRawBlock,block:!1,text:e[0]}}link(n){const e=this.rules.inline.link.exec(n);if(e){const t=e[2].trim();if(!this.options.pedantic&&this.rules.other.startAngleBracket.test(t)){if(!this.rules.other.endAngleBracket.test(t))return;const i=O(t.slice(0,-1),"\\");if((t.length-i.length)%2===0)return}else{const i=At(e[2],"()");if(i===-2)return;if(i>-1){const l=(e[0].indexOf("!")===0?5:4)+e[1].length+i;e[2]=e[2].substring(0,i),e[0]=e[0].substring(0,l).trim(),e[3]=""}}let r=e[2],s="";if(this.options.pedantic){const i=this.rules.other.pedanticHrefTitle.exec(r);i&&(r=i[1],s=i[3])}else s=e[3]?e[3].slice(1,-1):"";return r=r.trim(),this.rules.other.startAngleBracket.test(r)&&(this.options.pedantic&&!this.rules.other.endAngleBracket.test(t)?r=r.slice(1):r=r.slice(1,-1)),ye(e,{href:r&&r.replace(this.rules.inline.anyPunctuation,"$1"),title:s&&s.replace(this.rules.inline.anyPunctuation,"$1")},e[0],this.lexer,this.rules)}}reflink(n,e){let t;if((t=this.rules.inline.reflink.exec(n))||(t=this.rules.inline.nolink.exec(n))){const r=(t[2]||t[1]).replace(this.rules.other.multipleSpaceGlobal," "),s=e[r.toLowerCase()];if(!s){const i=t[0].charAt(0);return{type:"text",raw:i,text:i}}return ye(t,s,t[0],this.lexer,this.rules)}}emStrong(n,e,t=""){let r=this.rules.inline.emStrongLDelim.exec(n);if(!r||r[3]&&t.match(this.rules.other.unicodeAlphaNumeric))return;if(!(r[1]||r[2]||"")||!t||this.rules.inline.punctuation.exec(t)){const i=[...r[0]].length-1;let c,l,h=i,a=0;const o=r[0][0]==="*"?this.rules.inline.emStrongRDelimAst:this.rules.inline.emStrongRDelimUnd;for(o.lastIndex=0,e=e.slice(-1*n.length+i);(r=o.exec(e))!=null;){if(c=r[1]||r[2]||r[3]||r[4]||r[5]||r[6],!c)continue;if(l=[...c].length,r[3]||r[4]){h+=l;continue}else if((r[5]||r[6])&&i%3&&!((i+l)%3)){a+=l;continue}if(h-=l,h>0)continue;l=Math.min(l,l+h+a);const g=[...r[0]][0].length,p=n.slice(0,i+r.index+g+l);if(Math.min(i,l)%2){const u=p.slice(1,-1);return{type:"em",raw:p,text:u,tokens:this.lexer.inlineTokens(u)}}const b=p.slice(2,-2);return{type:"strong",raw:p,text:b,tokens:this.lexer.inlineTokens(b)}}}}codespan(n){const e=this.rules.inline.code.exec(n);if(e){let t=e[2].replace(this.rules.other.newLineCharGlobal," ");const r=this.rules.other.nonSpaceChar.test(t),s=this.rules.other.startingSpaceChar.test(t)&&this.rules.other.endingSpaceChar.test(t);return r&&s&&(t=t.substring(1,t.length-1)),{type:"codespan",raw:e[0],text:t}}}br(n){const e=this.rules.inline.br.exec(n);if(e)return{type:"br",raw:e[0]}}del(n){const e=this.rules.inline.del.exec(n);if(e)return{type:"del",raw:e[0],text:e[2],tokens:this.lexer.inlineTokens(e[2])}}autolink(n){const e=this.rules.inline.autolink.exec(n);if(e){let t,r;return e[2]==="@"?(t=e[1],r="mailto:"+t):(t=e[1],r=t),{type:"link",raw:e[0],text:t,href:r,tokens:[{type:"text",raw:t,text:t}]}}}url(n){var t;let e;if(e=this.rules.inline.url.exec(n)){let r,s;if(e[2]==="@")r=e[0],s="mailto:"+r;else{let i;do i=e[0],e[0]=((t=this.rules.inline._backpedal.exec(e[0]))==null?void 0:t[0])??"";while(i!==e[0]);r=e[0],e[1]==="www."?s="http://"+e[0]:s=e[0]}return{type:"link",raw:e[0],text:r,href:s,tokens:[{type:"text",raw:r,text:r}]}}}inlineText(n){const e=this.rules.inline.text.exec(n);if(e){const t=this.lexer.state.inRawBlock;return{type:"text",raw:e[0],text:e[0],escaped:t}}}},T=class ae{constructor(e){k(this,"tokens");k(this,"options");k(this,"state");k(this,"tokenizer");k(this,"inlineQueue");this.tokens=[],this.tokens.links=Object.create(null),this.options=e||C,this.options.tokenizer=this.options.tokenizer||new K,this.tokenizer=this.options.tokenizer,this.tokenizer.options=this.options,this.tokenizer.lexer=this,this.inlineQueue=[],this.state={inLink:!1,inRawBlock:!1,top:!0};const t={other:w,block:X.normal,inline:G.normal};this.options.pedantic?(t.block=X.pedantic,t.inline=G.pedantic):this.options.gfm&&(t.block=X.gfm,this.options.breaks?t.inline=G.breaks:t.inline=G.gfm),this.tokenizer.rules=t}static get rules(){return{block:X,inline:G}}static lex(e,t){return new ae(t).lex(e)}static lexInline(e,t){return new ae(t).inlineTokens(e)}lex(e){e=e.replace(w.carriageReturn,`
`),this.blockTokens(e,this.tokens);for(let t=0;t<this.inlineQueue.length;t++){const r=this.inlineQueue[t];this.inlineTokens(r.src,r.tokens)}return this.inlineQueue=[],this.tokens}blockTokens(e,t=[],r=!1){var s,i,c;for(this.options.pedantic&&(e=e.replace(w.tabCharGlobal,"    ").replace(w.spaceLine,""));e;){let l;if((i=(s=this.options.extensions)==null?void 0:s.block)!=null&&i.some(a=>(l=a.call({lexer:this},e,t))?(e=e.substring(l.raw.length),t.push(l),!0):!1))continue;if(l=this.tokenizer.space(e)){e=e.substring(l.raw.length);const a=t.at(-1);l.raw.length===1&&a!==void 0?a.raw+=`
`:t.push(l);continue}if(l=this.tokenizer.code(e)){e=e.substring(l.raw.length);const a=t.at(-1);(a==null?void 0:a.type)==="paragraph"||(a==null?void 0:a.type)==="text"?(a.raw+=`
`+l.raw,a.text+=`
`+l.text,this.inlineQueue.at(-1).src=a.text):t.push(l);continue}if(l=this.tokenizer.fences(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.heading(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.hr(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.blockquote(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.list(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.html(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.def(e)){e=e.substring(l.raw.length);const a=t.at(-1);(a==null?void 0:a.type)==="paragraph"||(a==null?void 0:a.type)==="text"?(a.raw+=`
`+l.raw,a.text+=`
`+l.raw,this.inlineQueue.at(-1).src=a.text):this.tokens.links[l.tag]||(this.tokens.links[l.tag]={href:l.href,title:l.title});continue}if(l=this.tokenizer.table(e)){e=e.substring(l.raw.length),t.push(l);continue}if(l=this.tokenizer.lheading(e)){e=e.substring(l.raw.length),t.push(l);continue}let h=e;if((c=this.options.extensions)!=null&&c.startBlock){let a=1/0;const o=e.slice(1);let g;this.options.extensions.startBlock.forEach(p=>{g=p.call({lexer:this},o),typeof g=="number"&&g>=0&&(a=Math.min(a,g))}),a<1/0&&a>=0&&(h=e.substring(0,a+1))}if(this.state.top&&(l=this.tokenizer.paragraph(h))){const a=t.at(-1);r&&(a==null?void 0:a.type)==="paragraph"?(a.raw+=`
`+l.raw,a.text+=`
`+l.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=a.text):t.push(l),r=h.length!==e.length,e=e.substring(l.raw.length);continue}if(l=this.tokenizer.text(e)){e=e.substring(l.raw.length);const a=t.at(-1);(a==null?void 0:a.type)==="text"?(a.raw+=`
`+l.raw,a.text+=`
`+l.text,this.inlineQueue.pop(),this.inlineQueue.at(-1).src=a.text):t.push(l);continue}if(e){const a="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(a);break}else throw new Error(a)}}return this.state.top=!0,t}inline(e,t=[]){return this.inlineQueue.push({src:e,tokens:t}),t}inlineTokens(e,t=[]){var l,h,a;let r=e,s=null;if(this.tokens.links){const o=Object.keys(this.tokens.links);if(o.length>0)for(;(s=this.tokenizer.rules.inline.reflinkSearch.exec(r))!=null;)o.includes(s[0].slice(s[0].lastIndexOf("[")+1,-1))&&(r=r.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.reflinkSearch.lastIndex))}for(;(s=this.tokenizer.rules.inline.anyPunctuation.exec(r))!=null;)r=r.slice(0,s.index)+"++"+r.slice(this.tokenizer.rules.inline.anyPunctuation.lastIndex);for(;(s=this.tokenizer.rules.inline.blockSkip.exec(r))!=null;)r=r.slice(0,s.index)+"["+"a".repeat(s[0].length-2)+"]"+r.slice(this.tokenizer.rules.inline.blockSkip.lastIndex);let i=!1,c="";for(;e;){i||(c=""),i=!1;let o;if((h=(l=this.options.extensions)==null?void 0:l.inline)!=null&&h.some(p=>(o=p.call({lexer:this},e,t))?(e=e.substring(o.raw.length),t.push(o),!0):!1))continue;if(o=this.tokenizer.escape(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.tag(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.link(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.reflink(e,this.tokens.links)){e=e.substring(o.raw.length);const p=t.at(-1);o.type==="text"&&(p==null?void 0:p.type)==="text"?(p.raw+=o.raw,p.text+=o.text):t.push(o);continue}if(o=this.tokenizer.emStrong(e,r,c)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.codespan(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.br(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.del(e)){e=e.substring(o.raw.length),t.push(o);continue}if(o=this.tokenizer.autolink(e)){e=e.substring(o.raw.length),t.push(o);continue}if(!this.state.inLink&&(o=this.tokenizer.url(e))){e=e.substring(o.raw.length),t.push(o);continue}let g=e;if((a=this.options.extensions)!=null&&a.startInline){let p=1/0;const b=e.slice(1);let u;this.options.extensions.startInline.forEach(x=>{u=x.call({lexer:this},b),typeof u=="number"&&u>=0&&(p=Math.min(p,u))}),p<1/0&&p>=0&&(g=e.substring(0,p+1))}if(o=this.tokenizer.inlineText(g)){e=e.substring(o.raw.length),o.raw.slice(-1)!=="_"&&(c=o.raw.slice(-1)),i=!0;const p=t.at(-1);(p==null?void 0:p.type)==="text"?(p.raw+=o.raw,p.text+=o.text):t.push(o);continue}if(e){const p="Infinite loop on byte: "+e.charCodeAt(0);if(this.options.silent){console.error(p);break}else throw new Error(p)}}return t}},Y=class{constructor(n){k(this,"options");k(this,"parser");this.options=n||C}space(n){return""}code({text:n,lang:e,escaped:t}){var i;const r=(i=(e||"").match(w.notSpaceStart))==null?void 0:i[0],s=n.replace(w.endingNewline,"")+`
`;return r?'<pre><code class="language-'+$(r)+'">'+(t?s:$(s,!0))+`</code></pre>
`:"<pre><code>"+(t?s:$(s,!0))+`</code></pre>
`}blockquote({tokens:n}){return`<blockquote>
${this.parser.parse(n)}</blockquote>
`}html({text:n}){return n}heading({tokens:n,depth:e}){return`<h${e}>${this.parser.parseInline(n)}</h${e}>
`}hr(n){return`<hr>
`}list(n){const e=n.ordered,t=n.start;let r="";for(let c=0;c<n.items.length;c++){const l=n.items[c];r+=this.listitem(l)}const s=e?"ol":"ul",i=e&&t!==1?' start="'+t+'"':"";return"<"+s+i+`>
`+r+"</"+s+`>
`}listitem(n){var t;let e="";if(n.task){const r=this.checkbox({checked:!!n.checked});n.loose?((t=n.tokens[0])==null?void 0:t.type)==="paragraph"?(n.tokens[0].text=r+" "+n.tokens[0].text,n.tokens[0].tokens&&n.tokens[0].tokens.length>0&&n.tokens[0].tokens[0].type==="text"&&(n.tokens[0].tokens[0].text=r+" "+$(n.tokens[0].tokens[0].text),n.tokens[0].tokens[0].escaped=!0)):n.tokens.unshift({type:"text",raw:r+" ",text:r+" ",escaped:!0}):e+=r+" "}return e+=this.parser.parse(n.tokens,!!n.loose),`<li>${e}</li>
`}checkbox({checked:n}){return"<input "+(n?'checked="" ':"")+'disabled="" type="checkbox">'}paragraph({tokens:n}){return`<p>${this.parser.parseInline(n)}</p>
`}table(n){let e="",t="";for(let s=0;s<n.header.length;s++)t+=this.tablecell(n.header[s]);e+=this.tablerow({text:t});let r="";for(let s=0;s<n.rows.length;s++){const i=n.rows[s];t="";for(let c=0;c<i.length;c++)t+=this.tablecell(i[c]);r+=this.tablerow({text:t})}return r&&(r=`<tbody>${r}</tbody>`),`<table>
<thead>
`+e+`</thead>
`+r+`</table>
`}tablerow({text:n}){return`<tr>
${n}</tr>
`}tablecell(n){const e=this.parser.parseInline(n.tokens),t=n.header?"th":"td";return(n.align?`<${t} align="${n.align}">`:`<${t}>`)+e+`</${t}>
`}strong({tokens:n}){return`<strong>${this.parser.parseInline(n)}</strong>`}em({tokens:n}){return`<em>${this.parser.parseInline(n)}</em>`}codespan({text:n}){return`<code>${$(n,!0)}</code>`}br(n){return"<br>"}del({tokens:n}){return`<del>${this.parser.parseInline(n)}</del>`}link({href:n,title:e,tokens:t}){const r=this.parser.parseInline(t),s=we(n);if(s===null)return r;n=s;let i='<a href="'+n+'"';return e&&(i+=' title="'+$(e)+'"'),i+=">"+r+"</a>",i}image({href:n,title:e,text:t,tokens:r}){r&&(t=this.parser.parseInline(r,this.parser.textRenderer));const s=we(n);if(s===null)return $(t);n=s;let i=`<img src="${n}" alt="${t}"`;return e&&(i+=` title="${$(e)}"`),i+=">",i}text(n){return"tokens"in n&&n.tokens?this.parser.parseInline(n.tokens):"escaped"in n&&n.escaped?n.text:$(n.text)}},be=class{strong({text:n}){return n}em({text:n}){return n}codespan({text:n}){return n}del({text:n}){return n}html({text:n}){return n}text({text:n}){return n}link({text:n}){return""+n}image({text:n}){return""+n}br(){return""}},z=class oe{constructor(e){k(this,"options");k(this,"renderer");k(this,"textRenderer");this.options=e||C,this.options.renderer=this.options.renderer||new Y,this.renderer=this.options.renderer,this.renderer.options=this.options,this.renderer.parser=this,this.textRenderer=new be}static parse(e,t){return new oe(t).parse(e)}static parseInline(e,t){return new oe(t).parseInline(e)}parse(e,t=!0){var s,i;let r="";for(let c=0;c<e.length;c++){const l=e[c];if((i=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&i[l.type]){const a=l,o=this.options.extensions.renderers[a.type].call({parser:this},a);if(o!==!1||!["space","hr","heading","code","table","blockquote","list","html","paragraph","text"].includes(a.type)){r+=o||"";continue}}const h=l;switch(h.type){case"space":{r+=this.renderer.space(h);continue}case"hr":{r+=this.renderer.hr(h);continue}case"heading":{r+=this.renderer.heading(h);continue}case"code":{r+=this.renderer.code(h);continue}case"table":{r+=this.renderer.table(h);continue}case"blockquote":{r+=this.renderer.blockquote(h);continue}case"list":{r+=this.renderer.list(h);continue}case"html":{r+=this.renderer.html(h);continue}case"paragraph":{r+=this.renderer.paragraph(h);continue}case"text":{let a=h,o=this.renderer.text(a);for(;c+1<e.length&&e[c+1].type==="text";)a=e[++c],o+=`
`+this.renderer.text(a);t?r+=this.renderer.paragraph({type:"paragraph",raw:o,text:o,tokens:[{type:"text",raw:o,text:o,escaped:!0}]}):r+=o;continue}default:{const a='Token with "'+h.type+'" type was not found.';if(this.options.silent)return console.error(a),"";throw new Error(a)}}}return r}parseInline(e,t=this.renderer){var s,i;let r="";for(let c=0;c<e.length;c++){const l=e[c];if((i=(s=this.options.extensions)==null?void 0:s.renderers)!=null&&i[l.type]){const a=this.options.extensions.renderers[l.type].call({parser:this},l);if(a!==!1||!["escape","html","link","image","strong","em","codespan","br","del","text"].includes(l.type)){r+=a||"";continue}}const h=l;switch(h.type){case"escape":{r+=t.text(h);break}case"html":{r+=t.html(h);break}case"link":{r+=t.link(h);break}case"image":{r+=t.image(h);break}case"strong":{r+=t.strong(h);break}case"em":{r+=t.em(h);break}case"codespan":{r+=t.codespan(h);break}case"br":{r+=t.br(h);break}case"del":{r+=t.del(h);break}case"text":{r+=t.text(h);break}default:{const a='Token with "'+h.type+'" type was not found.';if(this.options.silent)return console.error(a),"";throw new Error(a)}}}return r}},ie,V=(ie=class{constructor(n){k(this,"options");k(this,"block");this.options=n||C}preprocess(n){return n}postprocess(n){return n}processAllTokens(n){return n}provideLexer(){return this.block?T.lex:T.lexInline}provideParser(){return this.block?z.parse:z.parseInline}},k(ie,"passThroughHooks",new Set(["preprocess","postprocess","processAllTokens"])),ie),Pt=class{constructor(...n){k(this,"defaults",ce());k(this,"options",this.setOptions);k(this,"parse",this.parseMarkdown(!0));k(this,"parseInline",this.parseMarkdown(!1));k(this,"Parser",z);k(this,"Renderer",Y);k(this,"TextRenderer",be);k(this,"Lexer",T);k(this,"Tokenizer",K);k(this,"Hooks",V);this.use(...n)}walkTokens(n,e){var r,s;let t=[];for(const i of n)switch(t=t.concat(e.call(this,i)),i.type){case"table":{const c=i;for(const l of c.header)t=t.concat(this.walkTokens(l.tokens,e));for(const l of c.rows)for(const h of l)t=t.concat(this.walkTokens(h.tokens,e));break}case"list":{const c=i;t=t.concat(this.walkTokens(c.items,e));break}default:{const c=i;(s=(r=this.defaults.extensions)==null?void 0:r.childTokens)!=null&&s[c.type]?this.defaults.extensions.childTokens[c.type].forEach(l=>{const h=c[l].flat(1/0);t=t.concat(this.walkTokens(h,e))}):c.tokens&&(t=t.concat(this.walkTokens(c.tokens,e)))}}return t}use(...n){const e=this.defaults.extensions||{renderers:{},childTokens:{}};return n.forEach(t=>{const r={...t};if(r.async=this.defaults.async||r.async||!1,t.extensions&&(t.extensions.forEach(s=>{if(!s.name)throw new Error("extension name required");if("renderer"in s){const i=e.renderers[s.name];i?e.renderers[s.name]=function(...c){let l=s.renderer.apply(this,c);return l===!1&&(l=i.apply(this,c)),l}:e.renderers[s.name]=s.renderer}if("tokenizer"in s){if(!s.level||s.level!=="block"&&s.level!=="inline")throw new Error("extension level must be 'block' or 'inline'");const i=e[s.level];i?i.unshift(s.tokenizer):e[s.level]=[s.tokenizer],s.start&&(s.level==="block"?e.startBlock?e.startBlock.push(s.start):e.startBlock=[s.start]:s.level==="inline"&&(e.startInline?e.startInline.push(s.start):e.startInline=[s.start]))}"childTokens"in s&&s.childTokens&&(e.childTokens[s.name]=s.childTokens)}),r.extensions=e),t.renderer){const s=this.defaults.renderer||new Y(this.defaults);for(const i in t.renderer){if(!(i in s))throw new Error(`renderer '${i}' does not exist`);if(["options","parser"].includes(i))continue;const c=i,l=t.renderer[c],h=s[c];s[c]=(...a)=>{let o=l.apply(s,a);return o===!1&&(o=h.apply(s,a)),o||""}}r.renderer=s}if(t.tokenizer){const s=this.defaults.tokenizer||new K(this.defaults);for(const i in t.tokenizer){if(!(i in s))throw new Error(`tokenizer '${i}' does not exist`);if(["options","rules","lexer"].includes(i))continue;const c=i,l=t.tokenizer[c],h=s[c];s[c]=(...a)=>{let o=l.apply(s,a);return o===!1&&(o=h.apply(s,a)),o}}r.tokenizer=s}if(t.hooks){const s=this.defaults.hooks||new V;for(const i in t.hooks){if(!(i in s))throw new Error(`hook '${i}' does not exist`);if(["options","block"].includes(i))continue;const c=i,l=t.hooks[c],h=s[c];V.passThroughHooks.has(i)?s[c]=a=>{if(this.defaults.async)return Promise.resolve(l.call(s,a)).then(g=>h.call(s,g));const o=l.call(s,a);return h.call(s,o)}:s[c]=(...a)=>{let o=l.apply(s,a);return o===!1&&(o=h.apply(s,a)),o}}r.hooks=s}if(t.walkTokens){const s=this.defaults.walkTokens,i=t.walkTokens;r.walkTokens=function(c){let l=[];return l.push(i.call(this,c)),s&&(l=l.concat(s.call(this,c))),l}}this.defaults={...this.defaults,...r}}),this}setOptions(n){return this.defaults={...this.defaults,...n},this}lexer(n,e){return T.lex(n,e??this.defaults)}parser(n,e){return z.parse(n,e??this.defaults)}parseMarkdown(n){return(t,r)=>{const s={...r},i={...this.defaults,...s},c=this.onError(!!i.silent,!!i.async);if(this.defaults.async===!0&&s.async===!1)return c(new Error("marked(): The async option was set to true by an extension. Remove async: false from the parse options object to return a Promise."));if(typeof t>"u"||t===null)return c(new Error("marked(): input parameter is undefined or null"));if(typeof t!="string")return c(new Error("marked(): input parameter is of type "+Object.prototype.toString.call(t)+", string expected"));i.hooks&&(i.hooks.options=i,i.hooks.block=n);const l=i.hooks?i.hooks.provideLexer():n?T.lex:T.lexInline,h=i.hooks?i.hooks.provideParser():n?z.parse:z.parseInline;if(i.async)return Promise.resolve(i.hooks?i.hooks.preprocess(t):t).then(a=>l(a,i)).then(a=>i.hooks?i.hooks.processAllTokens(a):a).then(a=>i.walkTokens?Promise.all(this.walkTokens(a,i.walkTokens)).then(()=>a):a).then(a=>h(a,i)).then(a=>i.hooks?i.hooks.postprocess(a):a).catch(c);try{i.hooks&&(t=i.hooks.preprocess(t));let a=l(t,i);i.hooks&&(a=i.hooks.processAllTokens(a)),i.walkTokens&&this.walkTokens(a,i.walkTokens);let o=h(a,i);return i.hooks&&(o=i.hooks.postprocess(o)),o}catch(a){return c(a)}}}onError(n,e){return t=>{if(t.message+=`
Please report this to https://github.com/markedjs/marked.`,n){const r="<p>An error occurred:</p><pre>"+$(t.message+"",!0)+"</pre>";return e?Promise.resolve(r):r}if(e)return Promise.reject(t);throw t}}},P=new Pt;function d(n,e){return P.parse(n,e)}d.options=d.setOptions=function(n){return P.setOptions(n),d.defaults=P.defaults,Se(d.defaults),d};d.getDefaults=ce;d.defaults=C;d.use=function(...n){return P.use(...n),d.defaults=P.defaults,Se(d.defaults),d};d.walkTokens=function(n,e){return P.walkTokens(n,e)};d.parseInline=P.parseInline;d.Parser=z;d.parser=z.parse;d.Renderer=Y;d.TextRenderer=be;d.Lexer=T;d.lexer=T.lex;d.Tokenizer=K;d.Hooks=V;d.parse=d;d.options;d.setOptions;d.use;d.walkTokens;d.parseInline;z.parse;T.lex;var Ct=N('<div class="welcome-section svelte-106hvwq"><h2 class="svelte-106hvwq">Welcome to Documentation</h2> <p class="svelte-106hvwq">Select a document from the menu to view its contents.</p></div>'),It=N('<div class="loading-section svelte-106hvwq"><p class="svelte-106hvwq">Loading document...</p></div>'),Bt=N('<div class="error-section svelte-106hvwq"><h2 class="svelte-106hvwq">Error</h2> <p class="svelte-106hvwq"> </p></div>'),Et=N('<div class="markdown-content svelte-106hvwq"><!></div>'),qt=N('<div class="container"><!> <main><div class="vertical-sidebar-layout"><!> <div class="vertical-sidebar-content"><!></div></div></main></div>');function Xt(n,e){Me(e,!1);let t=Z([]),r=Z(""),s="",i=Z(""),c=Z(!1),l=Z("");Ue(async()=>{try{const m=await fetch("/api/docs");if(m.ok)_(t,await m.json());else throw new Error("Failed to load documentation files")}catch(m){console.error("Error loading doc files:",m),_(l,"Failed to load documentation files")}});function h(m){_(r,m),a(m)}async function a(m){const v=R(t).find(S=>S.id===m);if(v){_(c,!0),_(l,"");try{const S=await fetch(`/api/docs?file=${v.filename}`);if(!S.ok)throw new Error(`Failed to load ${v.filename}`);s=await S.text(),_(i,await d(s))}catch(S){console.error("Error loading markdown file:",S),_(l,`Failed to load ${(v==null?void 0:v.label)||"document"}`),_(i,"")}finally{_(c,!1)}}}Fe();var o=qt();Ne(m=>{Oe.title="Documentation"});var g=A(o);We(g,{title:"Documentation"});var p=se(g,2),b=A(p),u=A(b);Xe(u,{get items(){return R(t)},get currentItem(){return R(r)},onSelect:h});var x=se(u,2),Q=A(x);{var B=m=>{var v=Ct();M(m,v)},F=(m,v)=>{{var S=y=>{var ne=It();M(y,ne)},E=(y,ne)=>{{var Be=I=>{var q=Bt(),U=se(A(q),2),D=A(U,!0);L(U),L(q),je(()=>He(D,R(l))),M(I,q)},Ee=(I,q)=>{{var U=D=>{var re=Et(),qe=A(re);Qe(qe,()=>R(i)),L(re),M(D,re)};W(I,D=>{R(i)&&D(U)},q)}};W(y,I=>{R(l)?I(Be):I(Ee,!1)},ne)}};W(m,y=>{R(c)?y(S):y(E,!1)},v)}};W(Q,m=>{R(r)?m(F,!1):m(B)})}L(x),L(b),L(p),L(o),M(n,o),Ge()}export{Xt as component};
