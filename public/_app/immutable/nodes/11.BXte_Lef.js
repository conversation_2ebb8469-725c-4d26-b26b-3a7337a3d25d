import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as Hs,m as S,u as Ue,v as Ws,g as Ys,ae as Ks,i as s,k as e,j as d,r as t,s as o,l as k,o as Q,n as oe,t as w,q as ue,f as Xt}from"../chunks/p3DoyA09.js";import{h as Gs,s as $,e as re,r as Zt}from"../chunks/BUelSUke.js";import{i as A}from"../chunks/DwdToawP.js";import{e as Ie,i as Se}from"../chunks/DEqeA9IH.js";import{t as h,a as l,b as q,e as jt}from"../chunks/B67foYpL.js";import{r as M,a as es,s as pt,b as ts}from"../chunks/DdRd56Yq.js";import{s as ne}from"../chunks/D7jLSc-x.js";import{b as Z}from"../chunks/WI3NPOEW.js";import{b as ss}from"../chunks/DSjDIsro.js";import{p as Xs}from"../chunks/Bfc47y5P.js";import{i as Zs}from"../chunks/D3pqaimu.js";import{s as js,a as ei}from"../chunks/qYb16FSw.js";import{o as ti}from"../chunks/C_WNR8j8.js";import{b as si,g as _t}from"../chunks/CSyJhG7e.js";import{P as ii}from"../chunks/CC9utfo3.js";import{B as U}from"../chunks/6Zk3JFqZ.js";import{L as is}from"../chunks/C8F602cz.js";import{a as ve}from"../chunks/Ce-0qAhV.js";import{a as ai,c as oi}from"../chunks/Atsggda0.js";import{C as ri}from"../chunks/CxRlB3U5.js";import{c as as,b as ni,d as vi,e as li,f as ci,h as di,i as ui,s as mi}from"../chunks/DJsmiAoD.js";import{g as pi}from"../chunks/D9cKHHet.js";import{g as _i}from"../chunks/SnLwHqso.js";var gi=h('<div class="save-status unsaved svelte-18epg0m"><div class="status-dot svelte-18epg0m"></div> Unsaved changes</div>'),fi=h('<div class="save-status saved svelte-18epg0m"><div class="status-dot svelte-18epg0m"></div> All changes saved</div>'),hi=h('<div class="save-status-container svelte-18epg0m"><!></div>'),$i=h('<div class="loading-container svelte-18epg0m"><!> <p class="svelte-18epg0m">Loading invoice data...</p></div>'),yi=h('<div class="error-message svelte-18epg0m"> </div>'),bi=h('<div class="error-message svelte-18epg0m"> </div>'),Li=h('<div class="form-group horizontal svelte-18epg0m"><input type="text" placeholder="Item description"> <input type="number" min="1" step="1"> <input type="number" min="0" step="0.01"> <div class="item-cell tax svelte-18epg0m"><input type="number" min="0" max="100" step="0.1"></div> <div class="item-controls svelte-18epg0m"><!></div></div>'),xi=h('<div class="loading-uninvoiced svelte-18epg0m"><!> <span>Loading uninvoiced items...</span></div>'),wi=h('<div class="item-description svelte-18epg0m"> </div>'),Ii=h('<div><div class="item-checkbox svelte-18epg0m"><input type="checkbox" class="svelte-18epg0m"></div> <div class="item-info svelte-18epg0m"><div class="item-title svelte-18epg0m"> </div> <div class="item-details svelte-18epg0m"><span class="item-type svelte-18epg0m"> </span> <span class="item-date svelte-18epg0m"> </span> <span class="item-cost svelte-18epg0m"> </span></div> <!></div> <div class="item-actions svelte-18epg0m"><!></div></div>'),Si=h('<div class="uninvoiced-section svelte-18epg0m"><div class="section-header svelte-18epg0m"><h3 class="svelte-18epg0m"> </h3> <div class="bulk-actions svelte-18epg0m"><!> <!> <!></div></div> <div class="uninvoiced-items svelte-18epg0m"></div></div>'),Di=h('<div><div class="item-checkbox svelte-18epg0m"><input type="checkbox" class="svelte-18epg0m"></div> <div class="item-info svelte-18epg0m"><div class="item-title svelte-18epg0m"> </div> <div class="item-details svelte-18epg0m"><span class="item-date svelte-18epg0m"> </span> <span class="item-cost svelte-18epg0m"> </span> <span> </span></div> <div class="item-description svelte-18epg0m"> </div></div> <div class="item-actions svelte-18epg0m"><!></div></div>'),Pi=h('<div class="uninvoiced-section svelte-18epg0m"><div class="section-header svelte-18epg0m"><h3 class="svelte-18epg0m"> </h3> <div class="bulk-actions svelte-18epg0m"><!> <!> <!></div></div> <div class="uninvoiced-items svelte-18epg0m"></div></div>'),Ai=h('<div class="no-uninvoiced svelte-18epg0m"><p>No uninvoiced jobs or quotes found for this customer.</p></div>'),Ti=h('<div class="uninvoiced-container svelte-18epg0m"><!> <!> <!></div>'),ki=h('<div class="form-section svelte-18epg0m"><h2 class="svelte-18epg0m">Uninvoiced Items</h2> <p class="section-description svelte-18epg0m">Add existing jobs and quotes to this invoice</p> <!></div>'),Ni=h("<option> </option>"),zi=h('<select class="product-select svelte-18epg0m"><option>Select product</option><!></select>'),Ci=h('<input type="text" placeholder="Item description">'),Fi=h('<textarea placeholder="Additional info (optional)" rows="2" class="additional-info svelte-18epg0m"></textarea>'),Qi=h('<div class="error-message"> </div>'),qi=h('<div class="error-message"> </div>'),Ji=h('<div class="error-message"> </div>'),Ri=h('<div class="additional-info"><textarea placeholder="Additional information..." rows="2"></textarea></div>'),Ui=h('<div class="items-row svelte-18epg0m"><div class="item-cell svelte-18epg0m"><div class="item-description svelte-18epg0m"><!> <!> <div class="item-controls svelte-18epg0m"><!> <!></div></div> <!></div> <div class="item-cell svelte-18epg0m"><input type="number" min="1" step="1"> <!></div> <div class="item-cell svelte-18epg0m"><input type="number" min="0" step="0.01"> <!></div> <div class="item-cell svelte-18epg0m"><input type="number" min="0" max="100" step="0.1" class="svelte-18epg0m"></div> <div class="item-cell amount svelte-18epg0m"> </div> <div class="item-cell actions svelte-18epg0m"><!> <!></div></div>'),Ei=h("<option> </option>"),Oi=h("<option> </option>"),Vi=h(`<form class="invoice-form"><div class="invoice-editor svelte-18epg0m"><main class="editor-content svelte-18epg0m"><div class="form-section svelte-18epg0m"><h2 class="svelte-18epg0m">Customer Information</h2> <!></div> <div class="form-section svelte-18epg0m"><div class="layout-row svelte-18epg0m" style="grid-template-columns: 1fr 1fr;"><div class="layout-column svelte-18epg0m"><div class="form-group"><label for="sellerDetails">Seller Details</label> <textarea id="sellerDetails">123 Fake Street, 
London, 
UK, 
W12 3AB
                </textarea></div></div> <div class="layout-column svelte-18epg0m"><div class="form-group horizontal svelte-18epg0m"><label for="invoiceNumber" class="svelte-18epg0m">Invoice Number</label> <input type="text" id="invoiceNumber" placeholder="Auto-generated" readonly="" class="svelte-18epg0m"></div> <div class="form-group horizontal svelte-18epg0m"><label for="issueDate" class="svelte-18epg0m">Issue Date</label> <input type="date" id="issueDate"> <!></div> <div class="form-group horizontal svelte-18epg0m"><label for="dueDate" class="svelte-18epg0m">Due Date</label> <input type="date" id="dueDate"> <!></div> <!> <div><!></div></div></div></div> <!> <div class="form-section svelte-18epg0m"><div class="layout-row svelte-18epg0m"><div class="layout-column svelte-18epg0m"><div class="items-table svelte-18epg0m"><div class="items-header svelte-18epg0m"><div class="item-cell svelte-18epg0m">Description</div> <div class="item-cell svelte-18epg0m">Quantity</div> <div class="item-cell svelte-18epg0m">Unit Price</div> <div class="item-cell svelte-18epg0m">Tax Rate (%)</div> <div class="item-cell line-total svelte-18epg0m">Line Total</div> <div class="item-cell actions svelte-18epg0m"></div></div> <!> <div class="add-item-row svelte-18epg0m"><!></div></div> <div class="invoice-totals svelte-18epg0m"><div class="totals-row svelte-18epg0m"><div class="totals-label">Subtotal:</div> <div class="totals-value"> </div></div> <div class="totals-row svelte-18epg0m"><div class="totals-label"><label for="discountAmount">Discount:</label></div> <div class="totals-value"><input type="number" id="discountAmount" min="0" step="0.01" class="discount-input svelte-18epg0m"></div></div> <div class="totals-row svelte-18epg0m"><div class="totals-label">Tax:</div> <div class="totals-value"> </div></div> <div class="totals-row total svelte-18epg0m"><div class="totals-label">Total:</div> <div class="totals-value"> </div></div></div></div></div></div> <div class="form-section svelte-18epg0m"><div class="layout-row svelte-18epg0m" style="grid-template-columns: 1fr 1fr;"><div class="layout-column svelte-18epg0m"><div class="form-group"><label for="notes">Notes</label> <textarea id="notes" rows="3" placeholder="Notes to the customer"></textarea></div></div> <div class="layout-column svelte-18epg0m"><div class="form-group"><label for="terms">Terms and Conditions</label> <textarea id="terms" rows="3"></textarea></div></div></div></div></main> <div class="properties-panel svelte-18epg0m"><div class="property-section svelte-18epg0m"><div class="form-actions svelte-18epg0m"><!> <!></div> <div class="form-group"><label for="status">Status</label> <select id="status"></select></div> <div class="form-group"><label for="template">Template</label> <select id="template"><option>Default Template</option><!></select></div></div></div></div></form>`),Bi=h('<div class="container"><!> <!></div>');function ga(os,rs){Hs(rs,!1);const[ns,vs]=js(),gt=()=>ei(oi,"$customers",ns),Ne=S(),ft=S(),ls=S();let De=S(!1),He=S([]),ze=S([]),We=S([]),he=S([]),$e=S([]),Ye=S(!0),Ke=S(!1),Ee=S(""),Oe=S(!1),Ce=S(),ht=S(!1),K=S({}),H=S({}),le=S(new Set),ce=S(new Set),i=S({status:0,issueDate:new Date().toISOString().split("T")[0],dueDate:new Date(Date.now()+30*24*60*60*1e3).toISOString().split("T")[0],invoiceLines:[as()],notes:"",paymentTerms:"Payment due within 30 days. Please make payment via bank transfer.",customerId:"",templateId:"",discountAmount:0}),_=S({}),J=S(!1);function Pe(){if(!e(Ce))return;const a=JSON.stringify(e(i));d(Oe,a!==e(Ce))}function E(){const a=e(i).invoiceLines.map(r=>di(r));d(i,{...e(i),invoiceLines:a}),Pe()}function $t(){const a=e(i).invoiceLines.length+1;d(i,{...e(i),invoiceLines:[...e(i).invoiceLines,as(a)]}),Pe()}function cs(a){k(K,e(K)[a]=!e(K)[a]),d(K,{...e(K)})}function ds(a){k(H,e(H)[a]=!e(H)[a]),d(H,{...e(H)})}function yt(a){d(i,{...e(i),invoiceLines:e(i).invoiceLines.filter((y,f)=>f!==a)});const r={},u={};Object.keys(e(K)).forEach(y=>{const f=parseInt(y);f<a?r[f]=e(K)[f]:f>a&&(r[f-1]=e(K)[f])}),Object.keys(e(H)).forEach(y=>{const f=parseInt(y);f<a?u[f]=e(H)[f]:f>a&&(u[f-1]=e(H)[f])}),d(K,r),d(H,u),E(),Pe()}function us(a,r){const u=[...e(i).invoiceLines];u[a]={...u[a],description:r.description,unitPrice:r.price,taxRate:r.taxRate/100},d(i,{...e(i),invoiceLines:u}),E()}function ms(){return d(_,{}),e(i).customerId||k(_,e(_).customerId="Please select a customer"),e(i).issueDate||k(_,e(_).issueDate="Issue date is required"),e(i).dueDate||k(_,e(_).dueDate="Due date is required"),e(i).invoiceLines.forEach((a,r)=>{a.description||k(_,e(_)[`invoiceLines[${r}].description`]="Description is required"),a.quantity<=0&&k(_,e(_)[`invoiceLines[${r}].quantity`]="Quantity must be greater than 0"),a.unitPrice<0&&k(_,e(_)[`invoiceLines[${r}].unitPrice`]="Unit price cannot be negative")}),Object.keys(e(_)).length===0}async function ps(){d(Ye,!0);try{await ai.loadContacts();const[a,r,u]=await Promise.all([vi(),li(),ci()]);if(d(He,a),d(ze,r),d(We,u),e(ze).length>0){const y=e(ze).find(f=>f.isDefault);y&&k(i,e(i).templateId=y.id)}}catch(a){console.error("Error loading data:",a),ve({message:"Failed to load data",type:"error"})}finally{d(Ye,!1)}}function _s(a){if(!a.trim())return gt();const r=a.toLowerCase();return gt().filter(u=>u.fullName.toLowerCase().includes(r)||u.companyName&&u.companyName.toLowerCase().includes(r)||u.emails.some(y=>y.email.toLowerCase().includes(r)))}async function gs(a){if(!a){d(he,[]),d($e,[]);return}d(Ke,!0);try{const[r,u]=await Promise.all([pi(a),_i(a)]);d(he,r),d($e,u),d(ht,r.length>0||u.length>0)}catch(r){console.error("Error loading uninvoiced items:",r),ve({message:"Failed to load uninvoiced items",type:"error"})}finally{d(Ke,!1)}}function fs(a){var y;const u={lineNumber:e(i).invoiceLines.length+1,description:`${a.title} - ${a.description}`,quantity:1,unitPrice:((y=a.estimatedCost)==null?void 0:y.totalCost)||0,discountType:0,discountValue:0,discountAmount:0,subtotal:0,taxRate:.1,taxAmount:0,total:0};d(i,{...e(i),invoiceLines:[...e(i).invoiceLines,u]}),E(),ve({message:"Job added to invoice",type:"success"})}function hs(a){const r=a.lineItems.map((u,y)=>({lineNumber:e(i).invoiceLines.length+y+1,description:u.description,quantity:u.quantity,unitPrice:u.unitPrice,discountType:0,discountValue:0,discountAmount:0,subtotal:0,taxRate:u.taxRate/100,taxAmount:0,total:0}));d(i,{...e(i),invoiceLines:[...e(i).invoiceLines,...r]}),E(),ve({message:"Quote items added to invoice",type:"success"})}function $s(a){const r=new Set(e(le));r.has(a)?r.delete(a):r.add(a),d(le,r)}function ys(a){const r=new Set(e(ce));r.has(a)?r.delete(a):r.add(a),d(ce,r)}function bs(){d(le,new Set(e(he).map(a=>a.id)))}function Ls(){d(le,new Set)}function xs(){d(ce,new Set(e($e).map(a=>a.id)))}function ws(){d(ce,new Set)}function Is(){const a=e(he).filter(u=>e(le).has(u.id));if(a.length===0){ve({message:"No jobs selected",type:"warning"});return}const r=a.map((u,y)=>{var f;return{lineNumber:e(i).invoiceLines.length+y+1,description:`${u.title} - ${u.description}`,quantity:1,unitPrice:((f=u.estimatedCost)==null?void 0:f.totalCost)||0,discountType:0,discountValue:0,discountAmount:0,subtotal:0,taxRate:.1,taxAmount:0,total:0}});d(i,{...e(i),invoiceLines:[...e(i).invoiceLines,...r]}),E(),d(le,new Set),ve({message:`${a.length} job${a.length!==1?"s":""} added to invoice`,type:"success"})}function Ss(){const a=e($e).filter(y=>e(ce).has(y.id));if(a.length===0){ve({message:"No quotes selected",type:"warning"});return}const r=[];let u=e(i).invoiceLines.length+1;a.forEach(y=>{y.lineItems.forEach(f=>{r.push({lineNumber:u++,description:f.description,quantity:f.quantity,unitPrice:f.unitPrice,discountType:0,discountValue:0,discountAmount:0,subtotal:0,taxRate:f.taxRate/100,taxAmount:0,total:0})})}),d(i,{...e(i),invoiceLines:[...e(i).invoiceLines,...r]}),E(),d(ce,new Set),ve({message:`${a.length} quote${a.length!==1?"s":""} added to invoice`,type:"success"})}async function Ds(){if(d(J,!0),E(),!ms()){ve({message:"Please fix the errors in the form before submitting",type:"error"});return}d(De,!0);try{const a={status:e(i).status,issueDate:e(i).issueDate,dueDate:e(i).dueDate,notes:e(i).notes,paymentTerms:e(i).paymentTerms,invoiceLines:e(i).invoiceLines},r=await mi(a);ve({message:"Invoice created successfully",type:"success"}),_t("/invoices")}catch(a){console.error("Error creating invoice:",a),ve({message:a instanceof Error?a.message:"An unknown error occurred",type:"error"})}finally{d(De,!1)}}function Ps(){Pe(),e(Oe)?window.confirm("You have unsaved changes. Are you sure you want to cancel?")&&_t("/invoices"):_t("/invoices")}si(({cancel:a,to:r})=>{e(De)||(Pe(),e(Oe)&&(r==null?void 0:r.url.pathname)!==window.location.pathname&&(window.confirm("You have unsaved changes. Are you sure you want to leave?")||a()))}),ti(async()=>{await ps(),setTimeout(()=>{d(Ce,JSON.stringify(e(i)))},100)}),Ue(()=>e(i),()=>{d(Ne,ni(e(i).invoiceLines))}),Ue(()=>(e(Ne),e(i)),()=>{d(ft,e(Ne).totalAmount-e(i).discountAmount)}),Ue(()=>e(i),()=>{e(i).invoiceLines&&E()}),Ue(()=>e(Ce),()=>{e(Ce)&&Pe()}),Ue(()=>e(Ee),()=>{d(ls,_s(e(Ee)))}),Ws(),Zs();var Ge=Bi();Gs(a=>{Ks.title="Create Invoice"});var bt=s(Ge);ii(bt,{title:"Create Invoice",$$slots:{actions:(a,r)=>{var u=hi(),y=s(u);{var f=ye=>{var Fe=gi();l(ye,Fe)},Xe=ye=>{var Fe=fi();l(ye,Fe)};A(y,ye=>{e(Oe)?ye(f):ye(Xe,!1)})}t(u),l(a,u)}}});var As=o(bt,2);{var Ts=a=>{var r=$i(),u=s(r);is(u,{}),Q(2),t(r),l(a,r)},ks=a=>{var r=Vi(),u=s(r),y=s(u),f=s(y),Xe=o(s(f),2);const ye=oe(()=>e(J)&&!!e(_).customerId),Fe=oe(()=>e(J)&&e(_).customerId?e(_).customerId:"");ri(Xe,{get hasError(){return e(ye)},get errorMessage(){return e(Fe)},get customerId(){return e(i).customerId},set customerId(n){k(i,e(i).customerId=n)},get customerSearch(){return e(Ee)},set customerSearch(n){d(Ee,n)},$$events:{selectcustomer:n=>gs(n.detail)},$$legacy:!0}),t(f);var Ze=o(f,2),Lt=s(Ze),xt=o(s(Lt),2),je=s(xt),wt=o(s(je),2);M(wt),t(je);var et=o(je,2),Ve=o(s(et),2);M(Ve);let It;var Ns=o(Ve,2);{var zs=n=>{var p=yi(),v=s(p,!0);t(p),w(()=>$(v,e(_).issueDate)),l(n,p)};A(Ns,n=>{e(J)&&e(_).issueDate&&n(zs)})}t(et);var tt=o(et,2),Be=o(s(tt),2);M(Be);let St;var Cs=o(Be,2);{var Fs=n=>{var p=bi(),v=s(p,!0);t(p),w(()=>$(v,e(_).dueDate)),l(n,p)};A(Cs,n=>{e(J)&&e(_).dueDate&&n(Fs)})}t(tt);var Dt=o(tt,2);{var Qs=n=>{var p=jt(),v=Xt(p);Ie(v,1,()=>e(i).invoiceLines,Se,(N,I,x)=>{var D=Li(),R=s(D);M(R),pt(R,"id",`fieldLabel${x}`);let Ae;var G=o(R,2);M(G),pt(G,"id",`fieldQuantity${x}`);let Te;var j=o(G,2);M(j),pt(j,"id",`fieldUnitPrice${x}`);let we;var z=o(j,2),O=s(z);M(O),t(z);var X=o(z,2),ee=s(X);U(ee,{variant:"tertiary",size:"small",$$events:{click:()=>yt(x)},children:(C,B)=>{Q();var V=q("Remove");l(C,V)},$$slots:{default:!0}}),t(X),t(D),w((C,B,V)=>{Ae=ne(R,1,"svelte-18epg0m",null,Ae,C),Te=ne(G,1,"svelte-18epg0m",null,Te,B),we=ne(j,1,"svelte-18epg0m",null,we,V),es(O,e(I).taxRate*100)},[()=>({error:e(J)&&e(_)[`invoiceLines[${x}].description`]}),()=>({error:e(J)&&e(_)[`invoiceLines[${x}].quantity`]}),()=>({error:e(J)&&e(_)[`invoiceLines[${x}].unitPrice`]})],oe),Z(R,()=>e(I).description,C=>(e(I).description=C,ue(()=>e(i).invoiceLines))),Z(G,()=>e(I).quantity,C=>(e(I).quantity=C,ue(()=>e(i).invoiceLines))),re("input",G,()=>E()),Z(j,()=>e(I).unitPrice,C=>(e(I).unitPrice=C,ue(()=>e(i).invoiceLines))),re("input",j,()=>E()),re("input",O,C=>{const B=C.target;e(I).taxRate=parseFloat(B.value)/100,ue(()=>e(i).invoiceLines),E()}),l(N,D)}),l(n,p)};A(Dt,n=>{e(i).invoiceLines.length>0&&n(Qs)})}var Pt=o(Dt,2),qs=s(Pt);U(qs,{variant:"secondary",size:"small",$$events:{click:$t},children:(n,p)=>{Q();var v=q("Add Item");l(n,v)},$$slots:{default:!0}}),t(Pt),t(xt),t(Lt),t(Ze);var At=o(Ze,2);{var Js=n=>{var p=ki(),v=o(s(p),4);{var N=x=>{var D=xi(),R=s(D);is(R,{}),Q(2),t(D),l(x,D)},I=x=>{var D=Ti(),R=s(D);{var Ae=z=>{var O=Si(),X=s(O),ee=s(X),C=s(ee);t(ee);var B=o(ee,2),V=s(B);U(V,{variant:"tertiary",size:"small",$$events:{click:bs},children:(L,g)=>{Q();var P=q("Select All");l(L,P)},$$slots:{default:!0}});var be=o(V,2);U(be,{variant:"tertiary",size:"small",$$events:{click:Ls},children:(L,g)=>{Q();var P=q("Deselect All");l(L,P)},$$slots:{default:!0}});var Qe=o(be,2);{var qe=L=>{U(L,{variant:"primary",size:"small",$$events:{click:Is},children:(g,P)=>{Q();var W=q();w(()=>$(W,`Add Selected (${e(le).size??""}) to Invoice`)),l(g,W)},$$slots:{default:!0}})};A(Qe,L=>{e(le).size>0&&L(qe)})}t(B),t(X);var me=o(X,2);Ie(me,5,()=>e(he),Se,(L,g)=>{var P=Ii();let W;var te=s(P),se=s(te);M(se),t(te);var ie=o(te,2),pe=s(ie),ke=s(pe,!0);t(pe);var de=o(pe,2),_e=s(de),Je=s(_e,!0);t(_e);var ge=o(_e,2),c=s(ge);t(ge);var m=o(ge,2),b=s(m);t(m),t(de);var ae=o(de,2);{var Le=T=>{var F=wi(),xe=s(F,!0);t(F),w(()=>$(xe,e(g).description)),l(T,F)};A(ae,T=>{e(g).description&&T(Le)})}t(ie);var fe=o(ie,2),Y=s(fe);U(Y,{variant:"secondary",size:"small",$$events:{click:()=>fs(e(g))},children:(T,F)=>{Q();var xe=q("Add to Invoice");l(T,xe)},$$slots:{default:!0}}),t(fe),t(P),w((T,F,xe,Re)=>{var Me;W=ne(P,1,"uninvoiced-item svelte-18epg0m",null,W,T),ts(se,F),$(ke,e(g).title),$(Je,((Me=e(g).jobType)==null?void 0:Me.name)||"Job"),$(c,`Scheduled: ${xe??""}`),$(b,`$${Re??""}`)},[()=>({selected:e(le).has(e(g).id)}),()=>e(le).has(e(g).id),()=>e(g).scheduledDateTime?new Date(e(g).scheduledDateTime).toLocaleDateString():"Not scheduled",()=>{var T,F;return((F=(T=e(g).estimatedCost)==null?void 0:T.totalCost)==null?void 0:F.toFixed(2))||"0.00"}],oe),re("change",se,()=>$s(e(g).id)),l(L,P)}),t(me),t(O),w(()=>$(C,`Uninvoiced Jobs (${e(he).length??""})`)),l(z,O)};A(R,z=>{e(he).length>0&&z(Ae)})}var G=o(R,2);{var Te=z=>{var O=Pi(),X=s(O),ee=s(X),C=s(ee);t(ee);var B=o(ee,2),V=s(B);U(V,{variant:"tertiary",size:"small",$$events:{click:xs},children:(L,g)=>{Q();var P=q("Select All");l(L,P)},$$slots:{default:!0}});var be=o(V,2);U(be,{variant:"tertiary",size:"small",$$events:{click:ws},children:(L,g)=>{Q();var P=q("Deselect All");l(L,P)},$$slots:{default:!0}});var Qe=o(be,2);{var qe=L=>{U(L,{variant:"primary",size:"small",$$events:{click:Ss},children:(g,P)=>{Q();var W=q();w(()=>$(W,`Add Selected (${e(ce).size??""}) to Invoice`)),l(g,W)},$$slots:{default:!0}})};A(Qe,L=>{e(ce).size>0&&L(qe)})}t(B),t(X);var me=o(X,2);Ie(me,5,()=>e($e),Se,(L,g)=>{var P=Di();let W;var te=s(P),se=s(te);M(se),t(te);var ie=o(te,2),pe=s(ie),ke=s(pe);t(pe);var de=o(pe,2),_e=s(de),Je=s(_e);t(_e);var ge=o(_e,2),c=s(ge);t(ge);var m=o(ge,2);let b;var ae=s(m,!0);t(m),t(de);var Le=o(de,2),fe=s(Le);t(Le),t(ie);var Y=o(ie,2),T=s(Y);U(T,{variant:"secondary",size:"small",$$events:{click:()=>hs(e(g))},children:(F,xe)=>{Q();var Re=q("Add to Invoice");l(F,Re)},$$slots:{default:!0}}),t(Y),t(P),w((F,xe,Re,Me,Ms)=>{W=ne(P,1,"uninvoiced-item svelte-18epg0m",null,W,F),ts(se,xe),$(ke,`Quote #${e(g).quoteNumber??""}`),$(Je,`Issued: ${Re??""}`),$(c,`$${Me??""}`),b=ne(m,1,"item-status svelte-18epg0m",null,b,Ms),$(ae,e(g).status.name),$(fe,`${e(g).lineItems.length??""} line item${e(g).lineItems.length!==1?"s":""}`)},[()=>({selected:e(ce).has(e(g).id)}),()=>e(ce).has(e(g).id),()=>new Date(e(g).issueDate).toLocaleDateString(),()=>e(g).totalAmount.toFixed(2),()=>({accepted:e(g).status.name==="Accepted"})],oe),re("change",se,()=>ys(e(g).id)),l(L,P)}),t(me),t(O),w(()=>$(C,`Uninvoiced Quotes (${e($e).length??""})`)),l(z,O)};A(G,z=>{e($e).length>0&&z(Te)})}var j=o(G,2);{var we=z=>{var O=Ai();l(z,O)};A(j,z=>{e(he).length===0&&e($e).length===0&&z(we)})}t(D),l(x,D)};A(v,x=>{e(Ke)?x(N):x(I,!1)})}t(p),l(n,p)};A(At,n=>{e(ht)&&e(i).customerId&&n(Js)})}var st=o(At,2),Tt=s(st),kt=s(Tt),it=s(kt),Nt=o(s(it),2);Ie(Nt,1,()=>e(i).invoiceLines,Se,(n,p,v)=>{var N=Ui(),I=s(N),x=s(I),D=s(x);{var R=c=>{var m=zi(),b=s(m);b.value=b.__value="";var ae=o(b);Ie(ae,1,()=>e(He),Se,(Le,fe)=>{var Y=Ni(),T={},F=s(Y,!0);t(Y),w(()=>{T!==(T=e(fe).id)&&(Y.value=(Y.__value=e(fe).id)??""),$(F,e(fe).name)}),l(Le,Y)}),t(m),re("change",m,Le=>{const Y=Le.target.value;if(Y){const T=e(He).find(F=>F.id===Y);T&&us(v,T)}}),l(c,m)},Ae=c=>{var m=Ci();M(m);let b;w(ae=>b=ne(m,1,"svelte-18epg0m",null,b,ae),[()=>({error:e(J)&&e(_)[`invoiceLines[${v}].description`]})],oe),Z(m,()=>e(p).description,ae=>(e(p).description=ae,ue(()=>e(i).invoiceLines))),l(c,m)};A(D,c=>{e(K)[v]?c(R):c(Ae,!1)})}var G=o(D,2);{var Te=c=>{var m=Fi();l(c,m)};A(G,c=>{e(H)[v]&&c(Te)})}var j=o(G,2),we=s(j);const z=oe(()=>e(K)[v]?"primary":"secondary");U(we,{get variant(){return e(z)},size:"small",$$events:{click:()=>cs(v)},children:(c,m)=>{Q();var b=q();w(()=>$(b,e(K)[v]?"Type Manually":"Select From List")),l(c,b)},$$slots:{default:!0}});var O=o(we,2);const X=oe(()=>e(H)[v]?"primary":"secondary");U(O,{get variant(){return e(X)},size:"small",$$events:{click:()=>ds(v)},children:(c,m)=>{Q();var b=q();w(()=>$(b,e(H)[v]?"✕ Hide Additional Info":"Additional Info")),l(c,b)},$$slots:{default:!0}}),t(j),t(x);var ee=o(x,2);{var C=c=>{var m=Qi(),b=s(m,!0);t(m),w(()=>$(b,e(_)[`invoiceLines[${v}].description`])),l(c,m)};A(ee,c=>{e(J)&&e(_)[`invoiceLines[${v}].description`]&&c(C)})}t(I);var B=o(I,2),V=s(B);M(V);let be;var Qe=o(V,2);{var qe=c=>{var m=qi(),b=s(m,!0);t(m),w(()=>$(b,e(_)[`invoiceLines[${v}].quantity`])),l(c,m)};A(Qe,c=>{e(J)&&e(_)[`invoiceLines[${v}].quantity`]&&c(qe)})}t(B);var me=o(B,2),L=s(me);M(L);let g;var P=o(L,2);{var W=c=>{var m=Ji(),b=s(m,!0);t(m),w(()=>$(b,e(_)[`invoiceLines[${v}].unitPrice`])),l(c,m)};A(P,c=>{e(J)&&e(_)[`invoiceLines[${v}].unitPrice`]&&c(W)})}t(me);var te=o(me,2),se=s(te);M(se),t(te);var ie=o(te,2),pe=s(ie);t(ie);var ke=o(ie,2),de=s(ke);{var _e=c=>{U(c,{variant:"tertiary",size:"small",$$events:{click:()=>yt(v)},children:(m,b)=>{Q();var ae=q("Remove");l(m,ae)},$$slots:{default:!0}})};A(de,c=>{e(i).invoiceLines.length>1&&c(_e)})}var Je=o(de,2);{var ge=c=>{var m=Ri();l(c,m)};A(Je,c=>{e(H)[v]&&c(ge)})}t(ke),t(N),w((c,m,b)=>{be=ne(V,1,"svelte-18epg0m",null,be,c),g=ne(L,1,"svelte-18epg0m",null,g,m),es(se,e(p).taxRate*100),$(pe,`£${b??""}`)},[()=>({error:e(J)&&e(_)[`invoiceLines[${v}].quantity`]}),()=>({error:e(J)&&e(_)[`invoiceLines[${v}].unitPrice`]}),()=>e(p).subtotal.toFixed(2)],oe),Z(V,()=>e(p).quantity,c=>(e(p).quantity=c,ue(()=>e(i).invoiceLines))),re("input",V,()=>E()),Z(L,()=>e(p).unitPrice,c=>(e(p).unitPrice=c,ue(()=>e(i).invoiceLines))),re("input",L,()=>E()),re("input",se,c=>{const m=c.target;e(p).taxRate=parseFloat(m.value)/100,ue(()=>e(i).invoiceLines),E()}),l(n,N)});var zt=o(Nt,2),Rs=s(zt);U(Rs,{variant:"secondary",$$events:{click:$t},children:(n,p)=>{Q();var v=q("Add Item");l(n,v)},$$slots:{default:!0}}),t(zt),t(it);var Ct=o(it,2),at=s(Ct),Ft=o(s(at),2),Us=s(Ft);t(Ft),t(at);var ot=o(at,2),Qt=o(s(ot),2),rt=s(Qt);M(rt),t(Qt),t(ot);var nt=o(ot,2),qt=o(s(nt),2),Es=s(qt);t(qt),t(nt);var Jt=o(nt,2),Rt=o(s(Jt),2),Os=s(Rt);t(Rt),t(Jt),t(Ct),t(kt),t(Tt),t(st);var Ut=o(st,2),Et=s(Ut),vt=s(Et),Ot=s(vt),Vt=o(s(Ot),2);Zt(Vt),t(Ot),t(vt);var Bt=o(vt,2),Mt=s(Bt),Ht=o(s(Mt),2);Zt(Ht),t(Mt),t(Bt),t(Et),t(Ut),t(y);var Wt=o(y,2),Yt=s(Wt),lt=s(Yt),Kt=s(lt);U(Kt,{variant:"tertiary",size:"small",get disabled(){return e(De)},$$events:{click:Ps},children:(n,p)=>{Q();var v=q("Cancel");l(n,v)},$$slots:{default:!0}});var Vs=o(Kt,2);U(Vs,{type:"submit",get disabled(){return e(De)},children:(n,p)=>{var v=jt(),N=Xt(v);{var I=D=>{var R=q("Saving...");l(D,R)},x=D=>{var R=q("Save");l(D,R)};A(N,D=>{e(De)?D(I):D(x,!1)})}l(n,v)},$$slots:{default:!0}}),t(lt);var ct=o(lt,2),dt=o(s(ct),2);w(()=>{e(i),ue(()=>{e(We)})}),Ie(dt,5,()=>e(We),Se,(n,p)=>{var v=Ei(),N={},I=s(v,!0);t(v),w(x=>{N!==(N=x)&&(v.value=(v.__value=x)??""),$(I,e(p).name)},[()=>ui(e(p).name)],oe),l(n,v)}),t(dt),t(ct);var Gt=o(ct,2),ut=o(s(Gt),2);w(()=>{e(i),ue(()=>{e(ze)})});var mt=s(ut);mt.value=mt.__value="";var Bs=o(mt);Ie(Bs,1,()=>e(ze),Se,(n,p)=>{var v=Oi(),N={},I=s(v,!0);t(v),w(()=>{N!==(N=e(p).id)&&(v.value=(v.__value=e(p).id)??""),$(I,e(p).name)}),l(n,v)}),t(ut),t(Gt),t(Yt),t(Wt),t(u),t(r),w((n,p,v,N,I)=>{It=ne(Ve,1,"svelte-18epg0m",null,It,n),St=ne(Be,1,"svelte-18epg0m",null,St,p),$(Us,`£${v??""}`),$(Es,`£${N??""}`),$(Os,`£${I??""}`)},[()=>({error:e(J)&&e(_).issueDate}),()=>({error:e(J)&&e(_).dueDate}),()=>e(Ne).subtotal.toFixed(2),()=>e(Ne).taxAmount.toFixed(2),()=>e(ft).toFixed(2)],oe),Z(wt,()=>e(i).invoiceNumber,n=>k(i,e(i).invoiceNumber=n)),Z(Ve,()=>e(i).issueDate,n=>k(i,e(i).issueDate=n)),Z(Be,()=>e(i).dueDate,n=>k(i,e(i).dueDate=n)),Z(rt,()=>e(i).discountAmount,n=>k(i,e(i).discountAmount=n)),re("input",rt,()=>E()),Z(Vt,()=>e(i).notes,n=>k(i,e(i).notes=n)),Z(Ht,()=>e(i).paymentTerms,n=>k(i,e(i).paymentTerms=n)),ss(dt,()=>e(i).status,n=>k(i,e(i).status=n)),ss(ut,()=>e(i).templateId,n=>k(i,e(i).templateId=n)),re("submit",r,Xs(Ds)),l(a,r)};A(As,a=>{e(Ye)?a(Ts):a(ks,!1)})}t(Ge),l(os,Ge),Ys(),vs()}export{ga as component};
