import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as ct,f as vt,g as ft,i as l,t as R,s as a,j as n,m as q,k as e,l as y,n as Fe,o as F,r as i,q as ze}from"../chunks/p3DoyA09.js";import{e as _e,r as pt,s as b}from"../chunks/BUelSUke.js";import{i as N}from"../chunks/DwdToawP.js";import{e as V,i as W}from"../chunks/DEqeA9IH.js";import{t as _,a as u,b as C}from"../chunks/B67foYpL.js";import{r as S,a as mt,b as _t}from"../chunks/DdRd56Yq.js";import{b as H}from"../chunks/WI3NPOEW.js";import{b as Be}from"../chunks/DSjDIsro.js";import{p as gt}from"../chunks/Bfc47y5P.js";import{i as xt}from"../chunks/D3pqaimu.js";import{o as yt}from"../chunks/C_WNR8j8.js";import{e as bt,j as $t,k as ht,i as Rt,l as kt}from"../chunks/D9cKHHet.js";import{B as U}from"../chunks/6Zk3JFqZ.js";import{M as wt}from"../chunks/BWn8tY11.js";import{C as Dt}from"../chunks/BbevrtJW.js";import{P as Pt}from"../chunks/CC9utfo3.js";import{L as qt}from"../chunks/C8F602cz.js";import{a as ie}from"../chunks/Ce-0qAhV.js";var Ft=_('<div slot="actions"><!></div>'),St=_('<div class="text-sm text-gray-600"> </div>'),Mt=_('<div class="text-sm"><span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded-full"> </span></div>'),Jt=_('<span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full"> </span>'),It=_('<div><p class="text-sm font-medium mb-2">Required Skills:</p> <div class="flex flex-wrap gap-1"></div></div>'),Ct=_('<div class="flex justify-between items-center text-sm"><span> </span> <span class="text-gray-500"> </span></div>'),Ht=_('<div><p class="text-sm font-medium mb-2">Default Resources:</p> <div class="space-y-1"></div></div>'),Ut=_('<div class="bg-white rounded-lg shadow-md p-6 border border-gray-200"><div class="flex justify-between items-start mb-4"><div><h3 class="text-lg font-semibold text-gray-900"> </h3> <p class="text-sm text-gray-600"> </p></div> <div class="flex gap-2"><!> <!></div></div> <div class="space-y-3"><div class="flex items-center gap-2"><span class="font-medium"> </span> <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"> </span></div> <!> <!> <!> <!></div></div>'),At=_('<div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3"></div>'),Et=_('<div><label for="hourlyRate" class="block text-sm font-medium text-gray-700 mb-1">Default Hourly Rate ($)</label> <input id="hourlyRate" type="number" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></div>'),Nt=_('<div><label for="fixedPrice" class="block text-sm font-medium text-gray-700 mb-1">Fixed Price ($)</label> <input id="fixedPrice" type="number" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></div>'),zt=_('<div><label for="unitPrice" class="block text-sm font-medium text-gray-700 mb-1">Price Per Unit ($)</label> <input id="unitPrice" type="number" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></div>'),Bt=_('<span class="inline-flex items-center px-2 py-1 bg-green-100 text-green-800 text-sm rounded-full"> <button type="button" class="ml-1 hover:text-red-500">×</button></span>'),Lt=_("<option> </option>"),Ot=_('<div class="flex items-center gap-2 p-2 border rounded"><span class="flex-1"> </span> <input type="number" min="1" class="w-20 px-2 py-1 border border-gray-300 rounded"> <label class="flex items-center gap-1"><input type="checkbox"> Required</label> <!></div>'),Kt=_('<div class="flex items-center gap-2 p-2 border rounded"><span class="flex-1"> </span> <span class="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded"> </span> <!></div>'),Qt=_('<form class="space-y-6"><div class="grid gap-4 md:grid-cols-2"><div><label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name *</label> <input id="name" type="text" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></div> <div><label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label> <input id="category" type="text" placeholder="e.g., Residential, Commercial" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></div></div> <div><label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label> <textarea id="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea></div> <div><label for="pricingModel" class="block text-sm font-medium text-gray-700 mb-1">Pricing Model *</label> <select id="pricingModel" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option>Hourly Rate</option><option>Fixed Price</option><option>Per Unit</option></select></div> <div class="grid gap-4 md:grid-cols-2"><div><label for="duration" class="block text-sm font-medium text-gray-700 mb-1">Default Duration (minutes)</label> <input id="duration" type="number" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></div> <!></div> <div><label class="block text-sm font-medium text-gray-700 mb-1">Required Skills</label> <div class="flex gap-2 mb-2"><input placeholder="Add skill" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"> <!></div> <div class="flex flex-wrap gap-2"></div></div> <div><label class="block text-sm font-medium text-gray-700 mb-1">Default Resources</label> <div class="mb-2"><select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option>Add resource</option><!></select></div> <div class="space-y-2"></div></div> <div><label class="block text-sm font-medium text-gray-700 mb-1">Default Custom Fields</label> <div class="flex gap-2 mb-2"><input placeholder="Field name" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"> <select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option>Text</option><option>Number</option><option>Date</option><option>Boolean</option><option>Select</option></select> <!></div> <div class="space-y-2"></div></div> <div class="flex justify-end gap-2"><!> <!></div></form>'),Gt=_("<!> <!> <!> <!>",1);function fr(Le,Oe){ct(Oe,!1);let Se=q([]),ke=q([]),X=q(!1),le=q(!1),Y=q(null),ge=null,M=q(!1),t=q({name:"",description:"",pricingModel:"hourly",defaultDuration:120,defaultFixedPrice:0,defaultHourlyRate:35,defaultUnitPrice:0,category:"",requiredSkills:[],defaultResources:[],defaultFields:[]}),z=q(""),Z=q(""),B=q("text");yt(async()=>{await we()});async function we(){n(M,!0);try{await(async r=>{let[c,f]=r;n(Se,c),n(ke,f)})(await Promise.all([bt(),$t()]))}catch(r){console.error("Error loading data:",r),ie({type:"error",message:"Error loading job types"})}finally{n(M,!1)}}function Ke(){Me(),n(X,!0),n(Y,null)}function Qe(r){n(t,{name:r.name,description:r.description||"",pricingModel:r.pricingModel,defaultDuration:r.defaultDuration||120,defaultFixedPrice:r.defaultFixedPrice||0,defaultHourlyRate:r.defaultHourlyRate||35,defaultUnitPrice:r.defaultUnitPrice||0,category:r.category||"",requiredSkills:[...r.requiredSkills],defaultResources:[...r.defaultResources],defaultFields:[...r.defaultFields]}),n(Y,r),n(X,!0)}function Me(){n(t,{name:"",description:"",pricingModel:"hourly",defaultDuration:120,defaultFixedPrice:0,defaultHourlyRate:35,defaultUnitPrice:0,category:"",requiredSkills:[],defaultResources:[],defaultFields:[]}),n(z,""),n(Z,""),n(B,"text")}async function Ge(){n(M,!0);try{const r={...e(t),isActive:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};e(Y)?(await ht(e(Y).id,r),ie({type:"success",message:"Job type updated successfully"})):(await Rt(r),ie({type:"success",message:"Job type created successfully"})),await we(),n(X,!1),Me()}catch(r){console.error("Error saving job type:",r),ie({type:"error",message:"Error saving job type"})}finally{n(M,!1)}}function Ve(r){ge=r,n(le,!0)}async function We(){if(ge){n(M,!0);try{await kt(ge.id),await we(),ie({type:"success",message:"Job type deleted successfully"})}catch(r){console.error("Error deleting job type:",r),ie({type:"error",message:"Error deleting job type"})}finally{n(M,!1),n(le,!1),ge=null}}}function Xe(){e(z).trim()&&!e(t).requiredSkills.includes(e(z).trim())&&(y(t,e(t).requiredSkills=[...e(t).requiredSkills,e(z).trim()]),n(z,""))}function Ye(r){y(t,e(t).requiredSkills=e(t).requiredSkills.filter(c=>c!==r))}function Ze(){if(e(Z).trim()){const r={id:Date.now().toString(),key:e(Z).trim(),value:"",type:e(B),options:e(B)==="select"?[]:void 0};y(t,e(t).defaultFields=[...e(t).defaultFields,r]),n(Z,""),n(B,"text")}}function Te(r){y(t,e(t).defaultFields=e(t).defaultFields.filter(c=>c.id!==r))}function je(r){const c=e(ke).find(f=>f.id===r);if(c&&!e(t).defaultResources.some(f=>f.resourceId===r)){const f={resourceId:c.id,resourceName:c.name,resourceType:c.type,quantity:1,isRequired:!0};y(t,e(t).defaultResources=[...e(t).defaultResources,f])}}function et(r){y(t,e(t).defaultResources=e(t).defaultResources.filter(c=>c.resourceId!==r))}function tt(r,c){y(t,e(t).defaultResources=e(t).defaultResources.map(f=>f.resourceId===r?{...f,quantity:c}:f))}function rt(r){y(t,e(t).defaultResources=e(t).defaultResources.map(c=>c.resourceId===r?{...c,isRequired:!c.isRequired}:c))}function at(r){switch(r.pricingModel){case"hourly":return`$${r.defaultHourlyRate||0}/hour`;case"fixed":return`$${r.defaultFixedPrice||0} fixed`;case"per_unit":return`$${r.defaultUnitPrice||0}/unit`;default:return"Not set"}}xt();var Je=Gt(),Ie=vt(Je);Pt(Ie,{title:"Job Types",$$slots:{actions:(r,c)=>{var f=Ft(),p=l(f);U(p,{variant:"primary",$$events:{click:Ke},children:(J,L)=>{F();var A=C("Add Job Type");u(J,A)},$$slots:{default:!0}}),i(f),u(r,f)}}});var Ce=a(Ie,2);{var it=r=>{qt(r,{})},lt=r=>{var c=At();V(c,5,()=>e(Se),W,(f,p)=>{var J=Ut(),L=l(J),A=l(L),T=l(A),se=l(T,!0);i(T);var oe=a(T,2),de=l(oe,!0);i(oe),i(A);var j=a(A,2),ee=l(j);U(ee,{variant:"secondary",size:"small",$$events:{click:()=>Qe(e(p))},children:(v,m)=>{F();var x=C("Edit");u(v,x)},$$slots:{default:!0}});var ne=a(ee,2);U(ne,{variant:"danger",size:"small",$$events:{click:()=>Ve(e(p))},children:(v,m)=>{F();var x=C("Delete");u(v,x)},$$slots:{default:!0}}),i(j),i(L);var ue=a(L,2),O=l(ue),K=l(O),xe=l(K,!0);i(K);var ye=a(K,2),De=l(ye,!0);i(ye),i(O);var be=a(O,2);{var ce=v=>{var m=St(),x=l(m);i(m),R(D=>b(x,`Duration: ${D??""}h ${e(p).defaultDuration%60}m`),[()=>Math.floor(e(p).defaultDuration/60)],Fe),u(v,m)};N(be,v=>{e(p).defaultDuration&&v(ce)})}var te=a(be,2);{var ve=v=>{var m=Mt(),x=l(m),D=l(x,!0);i(x),i(m),R(()=>b(D,e(p).category)),u(v,m)};N(te,v=>{e(p).category&&v(ve)})}var $e=a(te,2);{var he=v=>{var m=It(),x=a(l(m),2);V(x,5,()=>e(p).requiredSkills,W,(D,E)=>{var k=Jt(),I=l(k,!0);i(k),R(()=>b(I,e(E))),u(D,k)}),i(x),i(m),u(v,m)};N($e,v=>{e(p).requiredSkills.length>0&&v(he)})}var fe=a($e,2);{var pe=v=>{var m=Ht(),x=a(l(m),2);V(x,5,()=>e(p).defaultResources,W,(D,E)=>{var k=Ct(),I=l(k),re=l(I,!0);i(I);var ae=a(I,2),me=l(ae);i(ae),i(k),R(()=>{b(re,e(E).resourceName),b(me,`×${e(E).quantity??""}`)}),u(D,k)}),i(x),i(m),u(v,m)};N(fe,v=>{e(p).defaultResources.length>0&&v(pe)})}i(ue),i(J),R(v=>{b(se,e(p).name),b(de,e(p).description||"No description"),b(xe,v),b(De,e(p).pricingModel)},[()=>at(e(p))],Fe),u(f,J)}),i(c),u(r,c)};N(Ce,r=>{e(M)?r(it):r(lt,!1)})}var He=a(Ce,2);const st=Fe(()=>e(Y)?"Edit Job Type":"Create Job Type");wt(He,{get title(){return e(st)},get show(){return e(X)},set show(r){n(X,r)},children:(r,c)=>{var f=Qt(),p=l(f),J=l(p),L=a(l(J),2);S(L),i(J);var A=a(J,2),T=a(l(A),2);S(T),i(A),i(p);var se=a(p,2),oe=a(l(se),2);pt(oe),i(se);var de=a(se,2),j=a(l(de),2);R(()=>{e(t),ze(()=>{})});var ee=l(j);ee.value=ee.__value="hourly";var ne=a(ee);ne.value=ne.__value="fixed";var ue=a(ne);ue.value=ue.__value="per_unit",i(j),i(de);var O=a(de,2),K=l(O),xe=a(l(K),2);S(xe),i(K);var ye=a(K,2);{var De=s=>{var d=Et(),o=a(l(d),2);S(o),i(d),H(o,()=>e(t).defaultHourlyRate,g=>y(t,e(t).defaultHourlyRate=g)),u(s,d)},be=(s,d)=>{{var o=$=>{var h=Nt(),P=a(l(h),2);S(P),i(h),H(P,()=>e(t).defaultFixedPrice,w=>y(t,e(t).defaultFixedPrice=w)),u($,h)},g=($,h)=>{{var P=w=>{var Q=zt(),G=a(l(Q),2);S(G),i(Q),H(G,()=>e(t).defaultUnitPrice,Re=>y(t,e(t).defaultUnitPrice=Re)),u(w,Q)};N($,w=>{e(t).pricingModel==="per_unit"&&w(P)},h)}};N(s,$=>{e(t).pricingModel==="fixed"?$(o):$(g,!1)},d)}};N(ye,s=>{e(t).pricingModel==="hourly"?s(De):s(be,!1)})}i(O);var ce=a(O,2),te=a(l(ce),2),ve=l(te);S(ve);var $e=a(ve,2);U($e,{type:"button",$$events:{click:Xe},children:(s,d)=>{F();var o=C("Add");u(s,o)},$$slots:{default:!0}}),i(te);var he=a(te,2);V(he,5,()=>e(t).requiredSkills,W,(s,d)=>{var o=Bt(),g=l(o),$=a(g);i(o),R(()=>b(g,`${e(d)??""} `)),_e("click",$,()=>Ye(e(d))),u(s,o)}),i(he),i(ce);var fe=a(ce,2),pe=a(l(fe),2),v=l(pe),m=l(v);m.value=m.__value="";var x=a(m);V(x,1,()=>e(ke).filter(s=>!e(t).defaultResources.some(d=>d.resourceId===s.id)),W,(s,d)=>{var o=Lt(),g={},$=l(o);i(o),R(()=>{g!==(g=e(d).id)&&(o.value=(o.__value=e(d).id)??""),b($,`${e(d).name??""} (${e(d).type??""})`)}),u(s,o)}),i(v),i(pe);var D=a(pe,2);V(D,5,()=>e(t).defaultResources,W,(s,d)=>{var o=Ot(),g=l(o),$=l(g,!0);i(g);var h=a(g,2);S(h);var P=a(h,2),w=l(P);S(w),F(),i(P);var Q=a(P,2);U(Q,{type:"button",variant:"danger",size:"small",$$events:{click:()=>et(e(d).resourceId)},children:(G,Re)=>{F();var ut=C("Delete");u(G,ut)},$$slots:{default:!0}}),i(o),R(()=>{b($,e(d).resourceName),mt(h,e(d).quantity),_t(w,e(d).isRequired)}),_e("input",h,G=>tt(e(d).resourceId,parseInt(G.target.value)||1)),_e("change",w,()=>rt(e(d).resourceId)),u(s,o)}),i(D),i(fe);var E=a(fe,2),k=a(l(E),2),I=l(k);S(I);var re=a(I,2);R(()=>{e(B),ze(()=>{})});var ae=l(re);ae.value=ae.__value="text";var me=a(ae);me.value=me.__value="number";var Pe=a(me);Pe.value=Pe.__value="date";var qe=a(Pe);qe.value=qe.__value="boolean";var Ue=a(qe);Ue.value=Ue.__value="select",i(re);var dt=a(re,2);U(dt,{type:"button",$$events:{click:Ze},children:(s,d)=>{F();var o=C("Add");u(s,o)},$$slots:{default:!0}}),i(k);var Ae=a(k,2);V(Ae,5,()=>e(t).defaultFields,W,(s,d)=>{var o=Kt(),g=l(o),$=l(g,!0);i(g);var h=a(g,2),P=l(h,!0);i(h);var w=a(h,2);U(w,{type:"button",variant:"danger",size:"small",$$events:{click:()=>Te(e(d).id)},children:(Q,G)=>{F();var Re=C("Delete");u(Q,Re)},$$slots:{default:!0}}),i(o),R(()=>{b($,e(d).key),b(P,e(d).type)}),u(s,o)}),i(Ae),i(E);var Ee=a(E,2),Ne=l(Ee);U(Ne,{type:"button",variant:"secondary",$$events:{click:()=>n(X,!1)},children:(s,d)=>{F();var o=C("Cancel");u(s,o)},$$slots:{default:!0}});var nt=a(Ne,2);U(nt,{type:"submit",variant:"primary",get disabled(){return e(M)},children:(s,d)=>{F();var o=C();R(()=>b(o,e(M)?"Saving...":e(Y)?"Update":"Create")),u(s,o)},$$slots:{default:!0}}),i(Ee),i(f),H(L,()=>e(t).name,s=>y(t,e(t).name=s)),H(T,()=>e(t).category,s=>y(t,e(t).category=s)),H(oe,()=>e(t).description,s=>y(t,e(t).description=s)),Be(j,()=>e(t).pricingModel,s=>y(t,e(t).pricingModel=s)),H(xe,()=>e(t).defaultDuration,s=>y(t,e(t).defaultDuration=s)),H(ve,()=>e(z),s=>n(z,s)),_e("change",v,s=>s.target.value&&je(s.target.value)),H(I,()=>e(Z),s=>n(Z,s)),Be(re,()=>e(B),s=>n(B,s)),_e("submit",f,gt(Ge)),u(r,f)},$$slots:{default:!0},$$legacy:!0});var ot=a(He,2);Dt(ot,{title:"Delete Job Type",message:"Are you sure you want to delete this job type? This action cannot be undone.",get show(){return e(le)},set show(r){n(le,r)},$$events:{confirm:We,cancel:()=>n(le,!1)},$$legacy:!0}),u(Le,Je),ft()}export{fr as component};
