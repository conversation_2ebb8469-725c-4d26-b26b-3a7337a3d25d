import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as gt,u as de,v as ht,g as bt,ae as Dt,f as W,k as e,m as g,i as a,j as n,s,r as t,o as fe,t as te,q as ze,n as ge}from"../chunks/p3DoyA09.js";import{h as Ct,e as $t,s as c}from"../chunks/BUelSUke.js";import{i as j}from"../chunks/DwdToawP.js";import{e as Ue,i as He}from"../chunks/DEqeA9IH.js";import{t as F,a as v,e as he,b as X}from"../chunks/B67foYpL.js";import{r as Je}from"../chunks/DdRd56Yq.js";import{b as Ke}from"../chunks/WI3NPOEW.js";import{b as Qe}from"../chunks/DSjDIsro.js";import{i as Pt}from"../chunks/D3pqaimu.js";import{s as St,a as It}from"../chunks/qYb16FSw.js";import{o as wt}from"../chunks/C_WNR8j8.js";import{P as Tt}from"../chunks/CC9utfo3.js";import{B as be}from"../chunks/6Zk3JFqZ.js";import{L as Rt}from"../chunks/C8F602cz.js";import{a as H}from"../chunks/Ce-0qAhV.js";import{f as $}from"../chunks/Chsk6cZE.js";import{k as Et,a as Lt}from"../chunks/DJsmiAoD.js";import"../chunks/D9cKHHet.js";import{c as kt,a as Ot}from"../chunks/Atsggda0.js";var At=F('<div class="loading-container svelte-c50lxy"><!> <p class="svelte-c50lxy">Loading report data...</p></div>'),Vt=F('<div class="control-group svelte-c50lxy"><label for="start-date" class="svelte-c50lxy">Start Date</label> <input id="start-date" type="date" class="svelte-c50lxy"></div> <div class="control-group svelte-c50lxy"><label for="end-date" class="svelte-c50lxy">End Date</label> <input id="end-date" type="date" class="svelte-c50lxy"></div>',1),jt=F('<div class="control-group svelte-c50lxy"><label for="date-range" class="svelte-c50lxy">Date Range</label> <select id="date-range" class="svelte-c50lxy"><option>Last 7 days</option><option>Last 30 days</option><option>Last 90 days</option><option>Last year</option><option>Custom range</option></select></div> <!> <!>',1),Ft=F('<div class="data-point svelte-c50lxy"><span class="date svelte-c50lxy"> </span> <span class="invoiced svelte-c50lxy"> </span> <span class="paid svelte-c50lxy"> </span></div>'),Bt=F('<div class="data-point svelte-c50lxy"><span class="more svelte-c50lxy"> </span></div>'),Nt=F('<tr class="svelte-c50lxy"><td class="svelte-c50lxy"> </td><td class="svelte-c50lxy"> </td><td class="svelte-c50lxy"> </td><td class="svelte-c50lxy"> </td><td class="svelte-c50lxy"> </td><td class="outstanding svelte-c50lxy"> </td></tr>'),Gt=F('<div class="summary-cards svelte-c50lxy"><div class="summary-card svelte-c50lxy"><h3 class="svelte-c50lxy">Total Invoiced</h3> <div class="amount svelte-c50lxy"> </div> <div class="subtitle svelte-c50lxy"> </div></div> <div class="summary-card svelte-c50lxy"><h3 class="svelte-c50lxy">Total Paid</h3> <div class="amount paid svelte-c50lxy"> </div> <div class="subtitle svelte-c50lxy"> </div></div> <div class="summary-card svelte-c50lxy"><h3 class="svelte-c50lxy">Outstanding</h3> <div class="amount outstanding svelte-c50lxy"> </div> <div class="subtitle svelte-c50lxy"> </div></div> <div class="summary-card svelte-c50lxy"><h3 class="svelte-c50lxy">Daily Average</h3> <div class="amount svelte-c50lxy"> </div> <div class="subtitle svelte-c50lxy"> </div></div></div> <div class="chart-container svelte-c50lxy"><h3 class="svelte-c50lxy">Revenue Trend</h3> <div class="chart-placeholder svelte-c50lxy"><p class="svelte-c50lxy">📊 Chart visualization would be displayed here</p> <p class="svelte-c50lxy">In a real implementation, you would integrate with a charting library like Chart.js or D3.js</p> <div class="chart-data-preview svelte-c50lxy"><h4 class="svelte-c50lxy">Chart Data Preview:</h4> <div class="data-points svelte-c50lxy"><!> <!></div></div></div></div> <div class="table-section svelte-c50lxy"><div class="table-header svelte-c50lxy"><h3 class="svelte-c50lxy">Daily Revenue Breakdown</h3> <!></div> <div class="table-container svelte-c50lxy"><table class="revenue-table svelte-c50lxy"><thead><tr class="svelte-c50lxy"><th class="svelte-c50lxy">Date</th><th class="svelte-c50lxy">Invoices</th><th class="svelte-c50lxy">Total Invoiced</th><th class="svelte-c50lxy">Paid</th><th class="svelte-c50lxy">Total Paid</th><th class="svelte-c50lxy">Outstanding</th></tr></thead><tbody></tbody></table></div></div>',1),Mt=F('<div class="export-section svelte-c50lxy"><div class="export-card svelte-c50lxy"><h3 class="svelte-c50lxy">Customer Data Export</h3> <p class="svelte-c50lxy">Export all customer information including contact details, addresses, and revenue data to CSV format.</p> <div class="export-details svelte-c50lxy"><h4 class="svelte-c50lxy">Export includes:</h4> <ul class="svelte-c50lxy"><li class="svelte-c50lxy">Customer ID and names</li> <li class="svelte-c50lxy">Primary contact information</li> <li class="svelte-c50lxy">Addresses and status</li> <li class="svelte-c50lxy">Total invoices and revenue</li> <li class="svelte-c50lxy">Account creation dates</li></ul></div> <div class="export-stats svelte-c50lxy"><div class="stat svelte-c50lxy"><span class="number svelte-c50lxy"> </span> <span class="label svelte-c50lxy">Total Customers</span></div> <div class="stat svelte-c50lxy"><span class="number svelte-c50lxy"> </span> <span class="label svelte-c50lxy">Active Customers</span></div> <div class="stat svelte-c50lxy"><span class="number svelte-c50lxy"> </span> <span class="label svelte-c50lxy">Leads</span></div></div> <!></div></div>'),qt=F('<div class="report-controls svelte-c50lxy"><div class="control-group svelte-c50lxy"><label for="report-type" class="svelte-c50lxy">Report Type</label> <select id="report-type" class="svelte-c50lxy"><option>Revenue Report</option><option>Customer Export</option></select></div> <!></div> <!>',1),zt=F('<div class="container"><!> <main class="svelte-c50lxy"><!></main></div>');function ua(We,Xe){gt(Xe,!1);const[Ye,Ze]=St(),ue=()=>It(kt,"$customers",Ye),ae=g(),pe=g(),Te=g(),De=g(),Re=g();let Ce=g(!0),ye=[],d=g([]),se=g("30"),re=g("revenue"),le=g(""),oe=g(""),me=g(!1),E=g(!1),ie=g({labels:[],datasets:[]});wt(async()=>{await et(),at(),await Ee()});async function et(){n(Ce,!0);try{await Promise.all([tt(),Ot.loadContacts()])}catch(r){console.error("Error loading data:",r),H({message:"Failed to load report data",type:"error"})}finally{n(Ce,!1)}}async function tt(){try{ye=await Et()}catch(r){throw console.error("Error loading invoices:",r),r}}function at(){const r=new Date,o=new Date(r.getTime()-30*24*60*60*1e3);n(oe,r.toISOString().split("T")[0]),n(le,o.toISOString().split("T")[0])}async function Ee(){if(!e(le)||!e(oe)){H({message:"Please select a valid date range",type:"error"});return}n(me,!0);try{const r=new Date(e(le)),o=new Date(e(oe)),y=ye.filter(l=>{const i=new Date(l.issueDate);return i>=r&&i<=o}),h=new Map;for(let l=new Date(r);l<=o;l.setDate(l.getDate()+1)){const i=l.toISOString().split("T")[0];h.set(i,{date:i,totalInvoiced:0,totalPaid:0,invoiceCount:0,paidCount:0})}y.forEach(l=>{const i=l.issueDate.split("T")[0],x=h.get(i);if(x){const L=l.invoiceLines.reduce((P,b)=>P+b.total,0);x.totalInvoiced+=L,x.invoiceCount+=1,Lt(l.status).name==="Paid"&&(x.totalPaid+=L,x.paidCount+=1)}}),n(d,Array.from(h.values()).sort((l,i)=>new Date(l.date).getTime()-new Date(i.date).getTime())),n(ie,{labels:e(d).map(l=>new Date(l.date).toLocaleDateString()),datasets:[{label:"Total Invoiced",data:e(d).map(l=>l.totalInvoiced),borderColor:"#3b82f6",backgroundColor:"#3b82f620",tension:.1},{label:"Total Paid",data:e(d).map(l=>l.totalPaid),borderColor:"#10b981",backgroundColor:"#10b98120",tension:.1}]}),H({message:"Revenue report generated successfully",type:"success"})}catch(r){console.error("Error generating report:",r),H({message:"Failed to generate revenue report",type:"error"})}finally{n(me,!1)}}async function Le(){n(E,!0);try{const r=ue().map(o=>{var y,h;return{"Customer ID":o.id,"Full Name":o.fullName,"Company Name":o.companyName||"","Primary Email":((y=o.emails.find(l=>l.isPrimary))==null?void 0:y.email)||"","Primary Phone":((h=o.phones.find(l=>l.isPrimary))==null?void 0:h.phone)||"",Status:o.status,"Primary Address":(()=>{const l=o.addresses.find(i=>i.isPrimary);return l?`${l.street}, ${l.city}, ${l.state} ${l.zipCode}`:""})(),"Created Date":new Date(o.createdAt).toLocaleDateString(),"Total Invoices":ye.filter(l=>!1).length,"Total Revenue":$(ye.filter(l=>!1).reduce((l,i)=>l+i.invoiceLines.reduce((x,L)=>x+L.total,0),0))}});ke(r,"customers-export"),H({message:"Customer data exported successfully",type:"success"})}catch(r){console.error("Error exporting CSV:",r),H({message:"Failed to export customer data",type:"error"})}finally{n(E,!1)}}async function st(){if(e(d).length===0){H({message:"Please generate a revenue report first",type:"error"});return}n(E,!0);try{const r=e(d).map(o=>({Date:new Date(o.date).toLocaleDateString(),"Total Invoiced":$(o.totalInvoiced),"Total Paid":$(o.totalPaid),"Invoice Count":o.invoiceCount,"Paid Count":o.paidCount,Outstanding:$(o.totalInvoiced-o.totalPaid)}));ke(r,"revenue-report"),H({message:"Revenue report exported successfully",type:"success"})}catch(r){console.error("Error exporting revenue CSV:",r),H({message:"Failed to export revenue report",type:"error"})}finally{n(E,!1)}}function ke(r,o){if(r.length===0)return;const y=Object.keys(r[0]),h=[y.join(","),...r.map(L=>y.map(P=>{const b=L[P];return typeof b=="string"&&(b.includes(",")||b.includes('"'))?`"${b.replace(/"/g,'""')}"`:b}).join(","))].join(`
`),l=new Blob([h],{type:"text/csv;charset=utf-8;"}),i=document.createElement("a"),x=URL.createObjectURL(l);i.setAttribute("href",x),i.setAttribute("download",`${o}-${new Date().toISOString().split("T")[0]}.csv`),i.style.visibility="hidden",document.body.appendChild(i),i.click(),document.body.removeChild(i)}function rt(){if(e(se)!=="custom"){const r=new Date,o=new Date(r.getTime()-parseInt(e(se))*24*60*60*1e3);n(oe,r.toISOString().split("T")[0]),n(le,o.toISOString().split("T")[0])}}de(()=>e(d),()=>{n(ae,e(d).reduce((r,o)=>r+o.totalInvoiced,0))}),de(()=>e(d),()=>{n(pe,e(d).reduce((r,o)=>r+o.totalPaid,0))}),de(()=>(e(ae),e(pe)),()=>{n(Te,e(ae)-e(pe))}),de(()=>e(d),()=>{n(De,e(d).reduce((r,o)=>r+o.invoiceCount,0))}),de(()=>(e(d),e(ae)),()=>{n(Re,e(d).length>0?e(ae)/e(d).length:0)}),ht(),Pt();var $e=zt();Ct(r=>{Dt.title="Reports - Easy Job Planner"});var Oe=a($e);Tt(Oe,{title:"Reports",$$slots:{actions:(r,o)=>{be(r,{variant:"secondary",get disabled(){return e(E)},$$events:{click:Le},children:(y,h)=>{var l=he(),i=W(l);{var x=P=>{var b=X("Exporting...");v(P,b)},L=P=>{var b=X("Export Customers");v(P,b)};j(i,P=>{e(E)?P(x):P(L,!1)})}v(y,l)},$$slots:{default:!0}})}}});var Ae=s(Oe,2),lt=a(Ae);{var ot=r=>{var o=At(),y=a(o);Rt(y,{}),fe(2),t(o),v(r,o)},it=r=>{var o=qt(),y=W(o),h=a(y),l=s(a(h),2);te(()=>{e(re),ze(()=>{})});var i=a(l);i.value=i.__value="revenue";var x=s(i);x.value=x.__value="customers",t(l),t(h);var L=s(h,2);{var P=S=>{var Y=jt(),B=W(Y),I=s(a(B),2);te(()=>{e(se),ze(()=>{})});var T=a(I);T.value=T.__value="7";var J=s(T);J.value=J.__value="30";var k=s(J);k.value=k.__value="90";var N=s(k);N.value=N.__value="365";var G=s(N);G.value=G.__value="custom",t(I),t(B);var K=s(B,2);{var Z=R=>{var O=Vt(),D=W(O),Q=s(a(D),2);Je(Q),t(D);var M=s(D,2),_=s(a(M),2);Je(_),t(M),Ke(Q,()=>e(le),p=>n(le,p)),Ke(_,()=>e(oe),p=>n(oe,p)),v(R,O)};j(K,R=>{e(se)==="custom"&&R(Z)})}var ee=s(K,2);be(ee,{variant:"primary",get disabled(){return e(me)},$$events:{click:Ee},children:(R,O)=>{var D=he(),Q=W(D);{var M=p=>{var A=X("Generating...");v(p,A)},_=p=>{var A=X("Generate Report");v(p,A)};j(Q,p=>{e(me)?p(M):p(_,!1)})}v(R,D)},$$slots:{default:!0}}),Qe(I,()=>e(se),R=>n(se,R)),$t("change",I,rt),v(S,Y)};j(L,S=>{e(re)==="revenue"&&S(P)})}t(y);var b=s(y,2);{var nt=S=>{var Y=Gt(),B=W(Y),I=a(B),T=s(a(I),2),J=a(T,!0);t(T);var k=s(T,2),N=a(k);t(k),t(I);var G=s(I,2),K=s(a(G),2),Z=a(K,!0);t(K);var ee=s(K,2),R=a(ee);t(ee),t(G);var O=s(G,2),D=s(a(O),2),Q=a(D,!0);t(D);var M=s(D,2),_=a(M);t(M),t(O);var p=s(O,2),A=s(a(p),2),Pe=a(A,!0);t(A);var xe=s(A,2),Se=a(xe);t(xe),t(p),t(B);var V=s(B,2),ne=s(a(V),2),Ve=s(a(ne),4),je=s(a(Ve),2),Fe=a(je);Ue(Fe,1,()=>e(ie).labels.slice(0,5),He,(m,u,f)=>{var C=Ft(),q=a(C),z=a(q,!0);t(q);var w=s(q,2),U=a(w);t(w);var _e=s(w,2),ve=a(_e);t(_e),t(C),te((we,ce)=>{c(z,e(u)),c(U,`Invoiced: ${we??""}`),c(ve,`Paid: ${ce??""}`)},[()=>$(e(ie).datasets[0].data[f]),()=>$(e(ie).datasets[1].data[f])],ge),v(m,C)});var ct=s(Fe,2);{var dt=m=>{var u=Bt(),f=a(u),C=a(f);t(f),t(u),te(()=>c(C,`... and ${e(ie).labels.length-5} more data points`)),v(m,u)};j(ct,m=>{e(ie).labels.length>5&&m(dt)})}t(je),t(Ve),t(ne),t(V);var Be=s(V,2),Ie=a(Be),ut=s(a(Ie),2);be(ut,{variant:"secondary",get disabled(){return e(E)},$$events:{click:st},children:(m,u)=>{var f=he(),C=W(f);{var q=w=>{var U=X("Exporting...");v(w,U)},z=w=>{var U=X("Export CSV");v(w,U)};j(C,w=>{e(E)?w(q):w(z,!1)})}v(m,f)},$$slots:{default:!0}}),t(Ie);var Ne=s(Ie,2),Ge=a(Ne),Me=s(a(Ge));Ue(Me,5,()=>e(d),He,(m,u)=>{var f=Nt(),C=a(f),q=a(C,!0);t(C);var z=s(C),w=a(z,!0);t(z);var U=s(z),_e=a(U,!0);t(U);var ve=s(U),we=a(ve,!0);t(ve);var ce=s(ve),pt=a(ce,!0);t(ce);var qe=s(ce),yt=a(qe,!0);t(qe),t(f),te((mt,xt,_t,ft)=>{c(q,mt),c(w,e(u).invoiceCount),c(_e,xt),c(we,e(u).paidCount),c(pt,_t),c(yt,ft)},[()=>new Date(e(u).date).toLocaleDateString(),()=>$(e(u).totalInvoiced),()=>$(e(u).totalPaid),()=>$(e(u).totalInvoiced-e(u).totalPaid)],ge),v(m,f)}),t(Me),t(Ge),t(Ne),t(Be),te((m,u,f,C,q,z)=>{c(J,m),c(N,`${e(De)??""} invoices`),c(Z,u),c(R,`${f??""} payments`),c(Q,C),c(_,`${q??""} unpaid`),c(Pe,z),c(Se,`${e(d).length??""} days`)},[()=>$(e(ae)),()=>$(e(pe)),()=>e(d).reduce((m,u)=>m+u.paidCount,0),()=>$(e(Te)),()=>e(De)-e(d).reduce((m,u)=>m+u.paidCount,0),()=>$(e(Re))],ge),v(S,Y)},vt=(S,Y)=>{{var B=I=>{var T=Mt(),J=a(T),k=s(a(J),6),N=a(k),G=a(N),K=a(G,!0);t(G),fe(2),t(N);var Z=s(N,2),ee=a(Z),R=a(ee,!0);t(ee),fe(2),t(Z);var O=s(Z,2),D=a(O),Q=a(D,!0);t(D),fe(2),t(O),t(k);var M=s(k,2);be(M,{variant:"primary",get disabled(){return e(E)},$$events:{click:Le},children:(_,p)=>{var A=he(),Pe=W(A);{var xe=V=>{var ne=X("Exporting Customer Data...");v(V,ne)},Se=V=>{var ne=X("Export Customer Data to CSV");v(V,ne)};j(Pe,V=>{e(E)?V(xe):V(Se,!1)})}v(_,A)},$$slots:{default:!0}}),t(J),t(T),te((_,p)=>{c(K,ue().length),c(R,_),c(Q,p)},[()=>ue().filter(_=>_.status==="Customer").length,()=>ue().filter(_=>_.status==="Lead").length],ge),v(I,T)};j(S,I=>{e(re)==="customers"&&I(B)},Y)}};j(b,S=>{e(re)==="revenue"&&e(d).length>0?S(nt):S(vt,!1)})}Qe(l,()=>e(re),S=>n(re,S)),v(r,o)};j(lt,r=>{e(Ce)?r(ot):r(it,!1)})}t(Ae),t($e),v(We,$e),bt(),Ze()}export{ua as component};
