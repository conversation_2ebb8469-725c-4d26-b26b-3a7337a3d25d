import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as qr,f as zr,g as Br,i as n,t as I,s as t,k as e,m as C,n as G,j as m,l as y,o as H,r as o,q as dr}from"../chunks/p3DoyA09.js";import{s as x,e as vr,r as Gr}from"../chunks/BUelSUke.js";import{i as P}from"../chunks/DwdToawP.js";import{e as ce,i as de}from"../chunks/DEqeA9IH.js";import{t as g,a as p,b as L}from"../chunks/B67foYpL.js";import{r as w,b as Hr}from"../chunks/DdRd56Yq.js";import{s as te}from"../chunks/D7jLSc-x.js";import{b as A,c as ur}from"../chunks/WI3NPOEW.js";import{b as fr}from"../chunks/DSjDIsro.js";import{p as Lr}from"../chunks/Bfc47y5P.js";import{i as Yr}from"../chunks/D3pqaimu.js";import{s as Zr,a as pr}from"../chunks/qYb16FSw.js";import{o as Kr}from"../chunks/C_WNR8j8.js";import{B as Y}from"../chunks/6Zk3JFqZ.js";import{M as Qr}from"../chunks/BWn8tY11.js";import{C as Vr}from"../chunks/BbevrtJW.js";import{P as Xr}from"../chunks/CC9utfo3.js";import{L as et}from"../chunks/C8F602cz.js";import{C as rt}from"../chunks/CxRlB3U5.js";import{a as N}from"../chunks/Ce-0qAhV.js";import{c as tt}from"../chunks/Atsggda0.js";import{a as at}from"../chunks/BGnz0MpO.js";import{e as st,j as ot,m as nt,n as it}from"../chunks/D9cKHHet.js";var lt=g('<div slot="actions"><!></div>'),ct=g('<p class="svelte-ffh941"><strong class="svelte-ffh941">Description:</strong> </p>'),dt=g('<p class="svelte-ffh941"><strong class="svelte-ffh941">Assigned Staff:</strong> </p>'),vt=g('<p class="svelte-ffh941"><strong class="svelte-ffh941">Resources:</strong> </p>'),ut=g('<div class="template-card svelte-ffh941"><div class="card-header svelte-ffh941"><h3 class="svelte-ffh941"> </h3> <div class="card-actions svelte-ffh941"><!> <!> <!></div></div> <div class="card-content svelte-ffh941"><div class="template-info svelte-ffh941"><p class="svelte-ffh941"><strong class="svelte-ffh941">Job Type:</strong> </p> <p class="svelte-ffh941"><strong class="svelte-ffh941">Customer:</strong> </p> <p class="svelte-ffh941"><strong class="svelte-ffh941">Recurrence:</strong> </p> <!> <!> <!> <div class="template-status svelte-ffh941"><span> </span></div></div></div></div>'),ft=g('<div class="empty-state svelte-ffh941"><h3 class="svelte-ffh941">No Recurring Jobs</h3> <p class="svelte-ffh941">Create your first recurring job template to automate job scheduling.</p> <!></div>'),pt=g('<div class="recurring-jobs-grid svelte-ffh941"><!> <!></div>'),mt=g('<div class="error-message"> </div>'),_t=g("<option> </option>"),yt=g('<div class="error-message"> </div>'),ht=g('<div class="error-message"> </div>'),gt=g('<div class="error-message"> </div>'),bt=g('<label class="checkbox-label svelte-ffh941"><input type="checkbox" class="svelte-ffh941"> <span class="checkbox-custom"></span> </label>'),$t=g('<label class="checkbox-label svelte-ffh941"><input type="checkbox" class="svelte-ffh941"> <span class="checkbox-custom"></span> </label>'),xt=g('<div class="error-message"> </div>'),jt=g('<label class="day-checkbox svelte-ffh941"><input type="checkbox" class="svelte-ffh941"> <span class="day-label svelte-ffh941"> </span></label>'),It=g('<div class="error-message"> </div>'),kt=g('<div class="form-group"><label>Days of Week *</label> <div class="days-of-week svelte-ffh941"></div> <!></div>'),St=g('<div class="form-group"><label for="dayOfMonth">Day of Month</label> <input id="dayOfMonth" type="number" min="1" max="31"></div>'),Pt=g('<form class="recurring-form svelte-ffh941"><div class="form-section svelte-ffh941"><h4 class="svelte-ffh941">Basic Information</h4> <div class="form-group"><label for="name">Template Name *</label> <input id="name" type="text" placeholder="e.g., Weekly Office Cleaning"> <!></div> <div class="form-row svelte-ffh941"><div class="form-group"><label for="jobType">Job Type *</label> <select id="jobType"><option>Select job type</option><!></select> <!></div> <div class="form-group"><!></div></div> <div class="form-group"><label for="description">Description</label> <textarea id="description" rows="3" placeholder="Job description"></textarea></div></div> <div class="form-section svelte-ffh941"><h4 class="svelte-ffh941">Job Address</h4> <div class="form-group"><label for="street">Street Address *</label> <input id="street" type="text" placeholder="123 Main St"> <!></div> <div class="form-row svelte-ffh941"><div class="form-group"><label for="city">City *</label> <input id="city" type="text" placeholder="City"> <!></div> <div class="form-group"><label for="state">State</label> <input id="state" type="text" placeholder="State"></div> <div class="form-group"><label for="zipCode">ZIP Code</label> <input id="zipCode" type="text" placeholder="ZIP"></div></div></div> <div class="form-section svelte-ffh941"><h4 class="svelte-ffh941">Assigned Staff</h4> <div class="staff-checkboxes svelte-ffh941"></div></div> <div class="form-section svelte-ffh941"><h4 class="svelte-ffh941">Resources</h4> <div class="resource-checkboxes svelte-ffh941"></div></div> <div class="form-section svelte-ffh941"><h4 class="svelte-ffh941">Recurrence Pattern</h4> <div class="form-row svelte-ffh941"><div class="form-group"><label for="recurrenceType">Frequency *</label> <select id="recurrenceType"><option>Daily</option><option>Weekly</option><option>Monthly</option><option>Yearly</option></select></div> <div class="form-group"><label for="interval">Every</label> <input id="interval" type="number" min="1"> <span class="interval-label svelte-ffh941"> </span> <!></div></div> <!> <!> <div class="form-row svelte-ffh941"><div class="form-group"><label for="endDate">End Date (optional)</label> <input id="endDate" type="date"></div> <div class="form-group"><label for="maxOccurrences">Max Occurrences (optional)</label> <input id="maxOccurrences" type="number" min="1" placeholder="Unlimited"></div></div></div> <div class="form-actions svelte-ffh941"><!> <!></div></form>'),Tt=g("<!> <!> <!> <!>",1);function ea(mr,_r){qr(_r,!1);const[We,yr]=Zr(),Ue=()=>pr(at,"$staff",We),Fe=()=>pr(tt,"$customers",We),hr=[],gr=[];let D=C([]),ve=C([]),xe=C([]),Z=C(!1),ae=C(!1),F=C(null),ue=null,q=C(!1),r=C({name:"",jobTypeId:"",customerId:"",customerSearch:"",description:"",assignedStaffIds:[],jobAddress:{street:"",city:"",state:"",zipCode:"",country:"US"},customFields:[],resourceIds:[],recurrencePattern:{type:"weekly",interval:1,daysOfWeek:[1],dayOfMonth:1,endDate:"",maxOccurrences:void 0}}),l=C({}),S=C(!1);Kr(async()=>{await br()});async function br(){m(q,!0);try{await(async u=>{let[c,_]=u;m(ve,c),m(xe,_)})(await Promise.all([st(),ot()]));const s=localStorage.getItem("ejp_recurring_jobs");m(D,s?JSON.parse(s):[])}catch(s){console.error("Error loading data:",s),N({type:"error",message:"Error loading data"})}finally{m(q,!1)}}function qe(){ze(),m(Z,!0),m(F,null)}function $r(s){m(r,{name:s.name,jobTypeId:s.jobTypeId,customerId:s.customerId,customerSearch:Be(s.customerId),description:s.description,assignedStaffIds:s.assignedStaff.map(u=>u.staffId),jobAddress:{...s.jobAddress},customFields:[...s.customFields],resourceIds:s.resources.map(u=>u.resourceId),recurrencePattern:{...s.recurrencePattern}}),m(F,s),m(Z,!0)}function ze(){m(r,{name:"",jobTypeId:"",customerId:"",customerSearch:"",description:"",assignedStaffIds:[],jobAddress:{street:"",city:"",state:"",zipCode:"",country:"US"},customFields:[],resourceIds:[],recurrencePattern:{type:"weekly",interval:1,daysOfWeek:[1],dayOfMonth:1,endDate:"",maxOccurrences:void 0}}),m(l,{}),m(S,!1)}function xr(){var s;return m(l,{}),e(r).name.trim()||y(l,e(l).name="Template name is required"),e(r).jobTypeId||y(l,e(l).jobTypeId="Job type is required"),e(r).customerId||y(l,e(l).customerId="Customer is required"),e(r).jobAddress.street.trim()||y(l,e(l).street="Street address is required"),e(r).jobAddress.city.trim()||y(l,e(l).city="City is required"),e(r).recurrencePattern.interval<1&&y(l,e(l).interval="Interval must be at least 1"),e(r).recurrencePattern.type==="weekly"&&((s=e(r).recurrencePattern.daysOfWeek)==null?void 0:s.length)===0&&y(l,e(l).daysOfWeek="At least one day must be selected for weekly recurrence"),Object.keys(e(l)).length===0}async function jr(){if(m(S,!0),!xr()){N({type:"error",message:"Please fix the errors in the form"});return}m(q,!0);try{const s=e(r).assignedStaffIds.map(_=>{var j;const v=Ue().find(f=>f.id===_);return{staffId:_,staffName:(v==null?void 0:v.fullName)||"Unknown",hourlyRate:((j=v==null?void 0:v.wageInfo)==null?void 0:j.rate)||0,estimatedHours:2,role:(v==null?void 0:v.position)||""}}),u=e(r).resourceIds.map(_=>{const v=e(xe).find(j=>j.id===_);return{id:Date.now().toString()+Math.random().toString(36).substr(2),resourceId:_,resourceName:(v==null?void 0:v.name)||"Unknown",resourceType:(v==null?void 0:v.type)||"Equipment",quantity:1,costPerUnit:(v==null?void 0:v.costPerUnit)||0,totalCost:(v==null?void 0:v.costPerUnit)||0}}),c={name:e(r).name,jobTypeId:e(r).jobTypeId,customerId:e(r).customerId,description:e(r).description,assignedStaff:s,jobAddress:e(r).jobAddress,customFields:e(r).customFields,resources:u,recurrencePattern:e(r).recurrencePattern,isActive:!0};if(e(F)){const _={...e(F),...c,updatedAt:new Date().toISOString()};m(D,e(D).map(v=>v.id===e(F).id?_:v)),localStorage.setItem("ejp_recurring_jobs",JSON.stringify(e(D))),N({type:"success",message:"Recurring job template updated successfully"})}else{const _=await nt(c);m(D,[...e(D),_]),N({type:"success",message:"Recurring job template created successfully"})}m(Z,!1),ze()}catch(s){console.error("Error saving template:",s),N({type:"error",message:"Error saving recurring job template"})}finally{m(q,!1)}}function Ir(s){ue=s,m(ae,!0)}async function kr(){if(ue)try{m(D,e(D).filter(s=>s.id!==ue.id)),localStorage.setItem("ejp_recurring_jobs",JSON.stringify(e(D))),N({type:"success",message:"Recurring job template deleted successfully"})}catch(s){console.error("Error deleting template:",s),N({type:"error",message:"Error deleting recurring job template"})}finally{m(ae,!1),ue=null}}async function Sr(s){try{const u=new Date().toISOString().split("T")[0],c=new Date;c.setMonth(c.getMonth()+3);const _=await it(s.id,u,c.toISOString().split("T")[0]);N({type:"success",message:`Generated ${_.length} job instances for the next 3 months`})}catch(u){console.error("Error generating jobs:",u),N({type:"error",message:"Error generating job instances"})}}function Pr(s){y(r,e(r).customerId=s.detail);const u=Fe().find(c=>c.id===s.detail);u&&y(r,e(r).customerSearch=u.companyName||u.fullName)}function Be(s){const u=Fe().find(c=>c.id===s);return u?u.companyName||u.fullName:"Unknown Customer"}function Tr(s){const u=e(ve).find(c=>c.id===s);return u?u.name:"Unknown Job Type"}function wr(s){const{type:u,interval:c,daysOfWeek:_,dayOfMonth:v}=s;switch(u){case"daily":return c===1?"Daily":`Every ${c} days`;case"weekly":const j=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],f=(_==null?void 0:_.map(J=>j[J]).join(", "))||"";return c===1?`Weekly on ${f}`:`Every ${c} weeks on ${f}`;case"monthly":return c===1?`Monthly on day ${v}`:`Every ${c} months on day ${v}`;case"yearly":return c===1?"Yearly":`Every ${c} years`;default:return"Unknown pattern"}}function Dr(s){const u=e(r).recurrencePattern.daysOfWeek||[];u.includes(s)?y(r,e(r).recurrencePattern.daysOfWeek=u.filter(c=>c!==s)):y(r,e(r).recurrencePattern.daysOfWeek=[...u,s].sort())}Yr();var Ge=Tt(),He=zr(Ge);Xr(He,{title:"Recurring Jobs",$$slots:{actions:(s,u)=>{var c=lt(),_=n(c);Y(_,{variant:"primary",$$events:{click:qe},children:(v,j)=>{H();var f=L("Create Recurring Job");p(v,f)},$$slots:{default:!0}}),o(c),p(s,c)}}});var Le=t(He,2);{var Or=s=>{et(s,{})},Cr=s=>{var u=pt(),c=n(u);ce(c,1,()=>e(D),de,(j,f)=>{var J=ut(),z=n(J),M=n(z),B=n(M,!0);o(M);var W=t(M,2),se=n(W);Y(se,{variant:"secondary",size:"small",$$events:{click:()=>$r(e(f))},children:(h,b)=>{H();var k=L("Edit");p(h,k)},$$slots:{default:!0}});var K=t(se,2);Y(K,{variant:"primary",size:"small",$$events:{click:()=>Sr(e(f))},children:(h,b)=>{H();var k=L("Generate Jobs");p(h,k)},$$slots:{default:!0}});var je=t(K,2);Y(je,{variant:"danger",size:"small",$$events:{click:()=>Ir(e(f))},children:(h,b)=>{H();var k=L("Delete");p(h,k)},$$slots:{default:!0}}),o(W),o(z);var fe=t(z,2),pe=n(fe),Q=n(pe),Ie=t(n(Q));o(Q);var oe=t(Q,2),me=t(n(oe));o(oe);var V=t(oe,2),ne=t(n(V));o(V);var X=t(V,2);{var ee=h=>{var b=ct(),k=t(n(b));o(b),I(()=>x(k,` ${e(f).description??""}`)),p(h,b)};P(X,h=>{e(f).description&&h(ee)})}var ie=t(X,2);{var ke=h=>{var b=dt(),k=t(n(b));o(b),I(T=>x(k,` ${T??""}`),[()=>e(f).assignedStaff.map(T=>T.staffName).join(", ")],G),p(h,b)};P(ie,h=>{e(f).assignedStaff.length>0&&h(ke)})}var _e=t(ie,2);{var ye=h=>{var b=vt(),k=t(n(b));o(b),I(T=>x(k,` ${T??""}`),[()=>e(f).resources.map(T=>T.resourceName).join(", ")],G),p(h,b)};P(_e,h=>{e(f).resources.length>0&&h(ye)})}var re=t(_e,2),U=n(re);let le;var Se=n(U,!0);o(U),o(re),o(pe),o(fe),o(J),I((h,b,k,T)=>{x(B,e(f).name),x(Ie,` ${h??""}`),x(me,` ${b??""}`),x(ne,` ${k??""}`),le=te(U,1,"status-badge svelte-ffh941",null,le,T),x(Se,e(f).isActive?"Active":"Inactive")},[()=>Tr(e(f).jobTypeId),()=>Be(e(f).customerId),()=>wr(e(f).recurrencePattern),()=>({active:e(f).isActive})],G),p(j,J)});var _=t(c,2);{var v=j=>{var f=ft(),J=t(n(f),4);Y(J,{variant:"primary",$$events:{click:qe},children:(z,M)=>{H();var B=L("Create Recurring Job");p(z,B)},$$slots:{default:!0}}),o(f),p(j,f)};P(_,j=>{e(D).length===0&&j(v)})}o(u),p(s,u)};P(Le,s=>{e(q)?s(Or):s(Cr,!1)})}var Ye=t(Le,2);const Ar=G(()=>e(F)?"Edit Recurring Job":"Create Recurring Job");Qr(Ye,{get title(){return e(Ar)},get show(){return e(Z)},set show(s){m(Z,s)},children:(s,u)=>{var c=Pt(),_=n(c),v=t(n(_),2),j=t(n(v),2);w(j);let f;var J=t(j,2);{var z=a=>{var i=mt(),d=n(i,!0);o(i),I(()=>x(d,e(l).name)),p(a,i)};P(J,a=>{e(S)&&e(l).name&&a(z)})}o(v);var M=t(v,2),B=n(M),W=t(n(B),2);I(()=>{e(r),dr(()=>{e(S),e(l),e(ve)})});let se;var K=n(W);K.value=K.__value="";var je=t(K);ce(je,1,()=>e(ve),de,(a,i)=>{var d=_t(),$={},O=n(d,!0);o(d),I(()=>{$!==($=e(i).id)&&(d.value=(d.__value=e(i).id)??""),x(O,e(i).name)}),p(a,d)}),o(W);var fe=t(W,2);{var pe=a=>{var i=yt(),d=n(i,!0);o(i),I(()=>x(d,e(l).jobTypeId)),p(a,i)};P(fe,a=>{e(S)&&e(l).jobTypeId&&a(pe)})}o(B);var Q=t(B,2),Ie=n(Q);const oe=G(()=>e(S)&&!!e(l).customerId);rt(Ie,{get customerId(){return e(r).customerId},get customerSearch(){return e(r).customerSearch},get hasError(){return e(oe)},get errorMessage(){return e(l).customerId},$$events:{selectcustomer:Pr}}),o(Q),o(M);var me=t(M,2),V=t(n(me),2);Gr(V),o(me),o(_);var ne=t(_,2),X=t(n(ne),2),ee=t(n(X),2);w(ee);let ie;var ke=t(ee,2);{var _e=a=>{var i=ht(),d=n(i,!0);o(i),I(()=>x(d,e(l).street)),p(a,i)};P(ke,a=>{e(S)&&e(l).street&&a(_e)})}o(X);var ye=t(X,2),re=n(ye),U=t(n(re),2);w(U);let le;var Se=t(U,2);{var h=a=>{var i=gt(),d=n(i,!0);o(i),I(()=>x(d,e(l).city)),p(a,i)};P(Se,a=>{e(S)&&e(l).city&&a(h)})}o(re);var b=t(re,2),k=t(n(b),2);w(k),o(b);var T=t(b,2),Ze=t(n(T),2);w(Ze),o(T),o(ye),o(ne);var Pe=t(ne,2),Ke=t(n(Pe),2);ce(Ke,5,()=>Ue().filter(a=>a.isActive),de,(a,i)=>{var d=bt(),$=n(d);w($);var O,R=t($,3);o(d),I(()=>{O!==(O=e(i).id)&&($.value=($.__value=e(i).id)??""),x(R,` ${e(i).fullName??""}`)}),ur(hr,[],$,()=>(e(i).id,e(r).assignedStaffIds),E=>y(r,e(r).assignedStaffIds=E)),p(a,d)}),o(Ke),o(Pe);var Te=t(Pe,2),Qe=t(n(Te),2);ce(Qe,5,()=>e(xe),de,(a,i)=>{var d=$t(),$=n(d);w($);var O,R=t($,3);o(d),I(()=>{O!==(O=e(i).id)&&($.value=($.__value=e(i).id)??""),x(R,` ${e(i).name??""} (${e(i).type??""})`)}),ur(gr,[],$,()=>(e(i).id,e(r).resourceIds),E=>y(r,e(r).resourceIds=E)),p(a,d)}),o(Qe),o(Te);var we=t(Te,2),De=t(n(we),2),Oe=n(De),Ce=t(n(Oe),2);I(()=>{e(r),dr(()=>{})});var Ae=n(Ce);Ae.value=Ae.__value="daily";var Je=t(Ae);Je.value=Je.__value="weekly";var Re=t(Je);Re.value=Re.__value="monthly";var Ve=t(Re);Ve.value=Ve.__value="yearly",o(Ce),o(Oe);var Xe=t(Oe,2),he=t(n(Xe),2);w(he);let er;var Ee=t(he,2),Rr=n(Ee,!0);o(Ee);var Er=t(Ee,2);{var Nr=a=>{var i=xt(),d=n(i,!0);o(i),I(()=>x(d,e(l).interval)),p(a,i)};P(Er,a=>{e(S)&&e(l).interval&&a(Nr)})}o(Xe),o(De);var rr=t(De,2);{var Mr=a=>{var i=kt(),d=t(n(i),2);ce(d,4,()=>["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],de,(R,E,ge)=>{var Me=jt(),be=n(Me);w(be);var cr=t(be,2),Fr=n(cr,!0);o(cr),o(Me),I($e=>{Hr(be,$e),x(Fr,E)},[()=>{var $e;return($e=e(r).recurrencePattern.daysOfWeek)==null?void 0:$e.includes(ge)}],G),vr("change",be,()=>Dr(ge)),p(R,Me)}),o(d);var $=t(d,2);{var O=R=>{var E=It(),ge=n(E,!0);o(E),I(()=>x(ge,e(l).daysOfWeek)),p(R,E)};P($,R=>{e(S)&&e(l).daysOfWeek&&R(O)})}o(i),p(a,i)};P(rr,a=>{e(r).recurrencePattern.type==="weekly"&&a(Mr)})}var tr=t(rr,2);{var Wr=a=>{var i=St(),d=t(n(i),2);w(d),o(i),A(d,()=>e(r).recurrencePattern.dayOfMonth,$=>y(r,e(r).recurrencePattern.dayOfMonth=$)),p(a,i)};P(tr,a=>{e(r).recurrencePattern.type==="monthly"&&a(Wr)})}var ar=t(tr,2),Ne=n(ar),sr=t(n(Ne),2);w(sr),o(Ne);var or=t(Ne,2),nr=t(n(or),2);w(nr),o(or),o(ar),o(we);var ir=t(we,2),lr=n(ir);Y(lr,{type:"button",variant:"secondary",$$events:{click:()=>m(Z,!1)},children:(a,i)=>{H();var d=L("Cancel");p(a,d)},$$slots:{default:!0}});var Ur=t(lr,2);Y(Ur,{type:"submit",variant:"primary",get disabled(){return e(q)},children:(a,i)=>{H();var d=L();I(()=>x(d,e(q)?"Saving...":e(F)?"Update Template":"Create Template")),p(a,d)},$$slots:{default:!0}}),o(ir),o(c),I((a,i,d,$,O)=>{f=te(j,1,"",null,f,a),se=te(W,1,"",null,se,i),ie=te(ee,1,"",null,ie,d),le=te(U,1,"",null,le,$),er=te(he,1,"",null,er,O),x(Rr,e(r).recurrencePattern.type==="daily"?"day(s)":e(r).recurrencePattern.type==="weekly"?"week(s)":e(r).recurrencePattern.type==="monthly"?"month(s)":"year(s)")},[()=>({error:e(S)&&e(l).name}),()=>({error:e(S)&&e(l).jobTypeId}),()=>({error:e(S)&&e(l).street}),()=>({error:e(S)&&e(l).city}),()=>({error:e(S)&&e(l).interval})],G),A(j,()=>e(r).name,a=>y(r,e(r).name=a)),fr(W,()=>e(r).jobTypeId,a=>y(r,e(r).jobTypeId=a)),A(V,()=>e(r).description,a=>y(r,e(r).description=a)),A(ee,()=>e(r).jobAddress.street,a=>y(r,e(r).jobAddress.street=a)),A(U,()=>e(r).jobAddress.city,a=>y(r,e(r).jobAddress.city=a)),A(k,()=>e(r).jobAddress.state,a=>y(r,e(r).jobAddress.state=a)),A(Ze,()=>e(r).jobAddress.zipCode,a=>y(r,e(r).jobAddress.zipCode=a)),fr(Ce,()=>e(r).recurrencePattern.type,a=>y(r,e(r).recurrencePattern.type=a)),A(he,()=>e(r).recurrencePattern.interval,a=>y(r,e(r).recurrencePattern.interval=a)),A(sr,()=>e(r).recurrencePattern.endDate,a=>y(r,e(r).recurrencePattern.endDate=a)),A(nr,()=>e(r).recurrencePattern.maxOccurrences,a=>y(r,e(r).recurrencePattern.maxOccurrences=a)),vr("submit",c,Lr(jr)),p(s,c)},$$slots:{default:!0},$$legacy:!0});var Jr=t(Ye,2);Vr(Jr,{title:"Delete Recurring Job Template",message:"Are you sure you want to delete this recurring job template? This action cannot be undone.",get showConfirm(){return e(ae)},set showConfirm(s){m(ae,s)},$$events:{confirm:kr,cancel:()=>m(ae,!1)},$$legacy:!0}),p(mr,Ge),Br(),yr()}export{ea as component};
