import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as gt,u as ht,v as zt,g as jt,ae as yt,i as s,k as e,m as y,s as r,j as v,o as b,r as a,f as $e,n as Ie,t as X,q as $t,l as be}from"../chunks/p3DoyA09.js";import{h as bt,s as Q,e as xt,r as Dt}from"../chunks/BUelSUke.js";import{i as C}from"../chunks/DwdToawP.js";import{e as xe,i as Tt}from"../chunks/DEqeA9IH.js";import{t as u,a as l,b as S,e as Ge}from"../chunks/B67foYpL.js";import{r as De,d as wt}from"../chunks/DdRd56Yq.js";import{s as Qt}from"../chunks/D7jLSc-x.js";import{b as Te,a as Ct}from"../chunks/WI3NPOEW.js";import{b as St}from"../chunks/DSjDIsro.js";import{i as At}from"../chunks/D3pqaimu.js";import{o as kt}from"../chunks/C_WNR8j8.js";import{B}from"../chunks/6Zk3JFqZ.js";import{P as Pt}from"../chunks/CC9utfo3.js";import{L as He}from"../chunks/C8F602cz.js";import{T as Et}from"../chunks/BIVs0YJ0.js";import{a as U}from"../chunks/Ce-0qAhV.js";import{g as Me}from"../chunks/CSyJhG7e.js";import{a as Lt,b as Nt,c as qt,u as Ft,d as Vt,e as Bt}from"../chunks/SnLwHqso.js";import{f as Ue}from"../chunks/Chsk6cZE.js";import{S as ae}from"../chunks/BYzA1sSu.js";var It=u('<div class="loading-container svelte-plrzj4"><!> <p class="svelte-plrzj4">Loading quotes...</p></div>'),Gt=u("<option> </option>"),Ht=u('<div class="empty-state svelte-plrzj4"><h3 class="svelte-plrzj4">No quotes found</h3> <p class="svelte-plrzj4"><!></p> <!></div>'),Mt=u('<div class="table-row svelte-plrzj4" role="button" tabindex="0"><div class="table-cell svelte-plrzj4"><span class="quote-number svelte-plrzj4"> </span></div> <div class="table-cell svelte-plrzj4"><span class="customer-name svelte-plrzj4"> </span></div> <div class="table-cell svelte-plrzj4"><span class="date svelte-plrzj4"> </span></div> <div class="table-cell svelte-plrzj4"><span> </span></div> <div class="table-cell svelte-plrzj4"><span class="amount svelte-plrzj4"> </span></div> <div class="table-cell svelte-plrzj4"><span class="status-badge svelte-plrzj4"> </span></div> <div class="table-cell svelte-plrzj4"><!></div></div>'),Ut=u('<div class="quotes-table svelte-plrzj4"><div class="table-header svelte-plrzj4"><div class="header-cell svelte-plrzj4">Quote #</div> <div class="header-cell svelte-plrzj4">Customer</div> <div class="header-cell svelte-plrzj4">Issue Date</div> <div class="header-cell svelte-plrzj4">Expiry Date</div> <div class="header-cell svelte-plrzj4">Amount</div> <div class="header-cell svelte-plrzj4">Status</div> <div class="header-cell svelte-plrzj4">Actions</div></div> <!></div>'),Jt=u('<div class="stats svelte-plrzj4"><!> <!> <!> <!> <!> <!></div> <div class="controls svelte-plrzj4"><div class="search-section svelte-plrzj4"><input type="text" placeholder="Search quotes..." class="search-input svelte-plrzj4"></div> <div class="filter-section svelte-plrzj4"><label for="status-filter" class="svelte-plrzj4">Status:</label> <select id="status-filter" class="svelte-plrzj4"><option>All</option><!></select></div></div> <!>',1),Kt=u('<div class="loading-container svelte-plrzj4"><!> <p class="svelte-plrzj4">Loading templates...</p></div>'),Ot=u('<div class="empty-state svelte-plrzj4"><h3 class="svelte-plrzj4">No templates found</h3> <p class="svelte-plrzj4">Create your first quote template to get started.</p> <!></div>'),Rt=u('<span class="default-badge svelte-plrzj4">Default</span>'),Wt=u('<p class="template-description svelte-plrzj4"> </p>'),Xt=u('<div class="template-card svelte-plrzj4"><div class="template-header svelte-plrzj4"><h3 class="svelte-plrzj4"> </h3> <!></div> <!> <div class="template-actions svelte-plrzj4"><!> <!></div></div>'),Yt=u('<div class="templates-grid svelte-plrzj4"></div>'),Zt=u('<div class="templates-section"><div class="section-header svelte-plrzj4"><h2 class="svelte-plrzj4">Quote Templates</h2> <!></div> <!></div>'),ea=u('<div class="template-editor svelte-plrzj4"><div class="editor-header svelte-plrzj4"><h2 class="svelte-plrzj4"> </h2> <div class="editor-actions svelte-plrzj4"><!> <!></div></div> <div class="editor-form svelte-plrzj4"><div class="form-group"><label for="template-name">Template Name *</label> <input id="template-name" type="text" placeholder="Enter template name" required></div> <div class="form-group"><label for="template-description">Description</label> <textarea id="template-description" placeholder="Enter template description (optional)" rows="3"></textarea></div> <div class="form-group"><label><input type="checkbox"> Set as default template</label></div></div></div>'),ta=u('<div class="designer-container svelte-plrzj4"><!></div>'),aa=u('<div class="container"><!> <!> <main class="svelte-plrzj4"><!></main></div>');function Da(Je,Ke){gt(Ke,!1);const ne=y();let re=y("quotes");const Oe=[{id:"quotes",label:"Quotes"},{id:"designer",label:"Quote Designer"}];function Re(t){v(re,t.detail.tabId)}let k=y([]),de=y([]),ce=y(!0),J=y(""),I=y("All"),pe=y([]),ue=y(!1),Y=y(null),le=y(!1),ie=y(!1),c=y({name:"",description:"",isDefault:!1,sections:[]});kt(async()=>{await We(),await me()});async function We(){v(ce,!0);try{const[t,d]=await Promise.all([Lt(),Nt()]);v(k,t),v(de,d)}catch(t){console.error("Error loading quotes:",t),U({message:"Failed to load quotes",type:"error"})}finally{v(ce,!1)}}async function me(){v(ue,!0);try{v(pe,await qt())}catch(t){console.error("Error loading templates:",t),U({message:"Failed to load templates",type:"error"})}finally{v(ue,!1)}}function we(){Me("/quotes/create")}function Qe(t){Me(`/quotes/${t.id}`)}function Xe(t){return t.color}function Ce(t){return new Date(t).toLocaleDateString()}function Ye(){return e(k).filter(t=>t.status.name==="Draft")}function Ze(){return e(k).filter(t=>t.status.name==="Sent")}function et(){return e(k).filter(t=>t.status.name==="Accepted")}function tt(){const t=new Date;return e(k).filter(d=>d.status.name!=="Accepted"&&d.status.name!=="Converted"&&new Date(d.expiryDate)<t)}function at(){return e(k).filter(t=>t.status.name==="Accepted").reduce((t,d)=>t+d.totalAmount,0)}function Se(){v(Y,null),v(le,!0),Ae()}function st(t){v(Y,t),v(le,!0),rt(t)}function Ae(){v(c,{name:"",description:"",isDefault:!1,sections:[]})}function rt(t){v(c,{name:t.name,description:t.description||"",isDefault:t.isDefault,sections:t.sections||[]})}function ke(){v(le,!1),v(Y,null),Ae()}async function lt(){if(!e(c).name.trim()){U({message:"Template name is required",type:"error"});return}v(ie,!0);try{const t={name:e(c).name,description:e(c).description,isDefault:e(c).isDefault,sections:e(c).sections};e(Y)?(await Ft(e(Y).id,t),U({message:"Template updated successfully",type:"success"})):(await Vt(t),U({message:"Template created successfully",type:"success"})),await me(),ke()}catch(t){console.error("Error saving template:",t),U({message:"Failed to save template",type:"error"})}finally{v(ie,!1)}}async function it(t){if(confirm(`Are you sure you want to delete the template "${t.name}"?`))try{await Bt(t.id),U({message:"Template deleted successfully",type:"success"}),await me()}catch(d){console.error("Error deleting template:",d),U({message:"Failed to delete template",type:"error"})}}ht(()=>(e(k),e(J),e(I)),()=>{v(ne,e(k).filter(t=>{var P;const d=t.quoteNumber.toLowerCase().includes(e(J).toLowerCase())||((P=t.customerName)==null?void 0:P.toLowerCase().includes(e(J).toLowerCase())),K=e(I)==="All"||t.status.name===e(I);return d&&K}))}),zt(),At();var fe=aa();bt(t=>{yt.title="Quotes"});var Pe=s(fe);Pt(Pe,{title:"Quotes",$$slots:{actions:(t,d)=>{B(t,{variant:"primary",type:"button",$$events:{click:we},children:(K,P)=>{b();var Z=S("Create Quote");l(K,Z)},$$slots:{default:!0}})}}});var Ee=r(Pe,2);Et(Ee,{tabs:Oe,get activeTab(){return e(re)},$$events:{change:Re}});var Le=r(Ee,2),ot=s(Le);{var vt=t=>{var d=Ge(),K=$e(d);{var P=G=>{var H=It(),O=s(H);He(O,{}),b(2),a(H),l(G,H)},Z=G=>{var H=Jt(),O=$e(H),E=s(O);const L=Ie(()=>Ue(at()));ae(E,{title:"Total Quote Value",get value(){return e(L)}});var ee=r(E,2);ae(ee,{title:"Total Quotes",get value(){return e(k).length}});var oe=r(ee,2);ae(oe,{title:"Accepted",get value(){return et().length}});var ve=r(oe,2);ae(ve,{title:"Sent",get value(){return Ze().length}});var N=r(ve,2);ae(N,{title:"Expired",get value(){return tt().length},valueClass:"overdue"});var q=r(N,2);ae(q,{title:"Drafts",get value(){return Ye().length}}),a(O);var x=r(O,2),F=s(x),te=s(F);De(te),a(F);var R=r(F,2),M=r(s(R),2);X(()=>{e(I),$t(()=>{e(de)})});var _=s(M);_.value=_.__value="All";var g=r(_);xe(g,1,()=>e(de),Tt,(o,n)=>{var p=Gt(),j={},i=s(p,!0);a(p),X(()=>{j!==(j=e(n).name)&&(p.value=(p.__value=e(n).name)??""),Q(i,e(n).name)}),l(o,p)}),a(M),a(R),a(x);var h=r(x,2);{var z=o=>{var n=Ht(),p=r(s(n),2),j=s(p);{var i=m=>{var T=S("Try adjusting your search or filters.");l(m,T)},D=m=>{var T=S("Get started by creating your first quote.");l(m,T)};C(j,m=>{e(J)||e(I)!=="All"?m(i):m(D,!1)})}a(p);var $=r(p,2);{var W=m=>{B(m,{variant:"primary",$$events:{click:we},children:(T,f)=>{b();var w=S("Create Quote");l(T,w)},$$slots:{default:!0}})};C($,m=>{!e(J)&&e(I)==="All"&&m(W)})}a(n),l(o,n)},A=o=>{var n=Ut(),p=r(s(n),2);xe(p,1,()=>e(ne),j=>j.id,(j,i)=>{var D=Mt(),$=s(D),W=s($),m=s(W,!0);a(W),a($);var T=r($,2),f=s(T),w=s(f,!0);a(f),a(T);var V=r(T,2),Ne=s(V),dt=s(Ne,!0);a(Ne),a(V);var _e=r(V,2),ge=s(_e);let qe;var ct=s(ge,!0);a(ge),a(_e);var he=r(_e,2),Fe=s(he),pt=s(Fe,!0);a(Fe),a(he);var ze=r(he,2),je=s(ze),ut=s(je,!0);a(je),a(ze);var Ve=r(ze,2),mt=s(Ve);B(mt,{variant:"secondary",size:"small",$$events:{click:se=>{se.stopPropagation(),Qe(e(i))}},children:(se,Be)=>{b();var ye=S("View");l(se,ye)},$$slots:{default:!0}}),a(Ve),a(D),X((se,Be,ye,ft,_t)=>{Q(m,e(i).quoteNumber),Q(w,e(i).customerName||"Unknown Customer"),Q(dt,se),qe=Qt(ge,1,"date svelte-plrzj4",null,qe,Be),Q(ct,ye),Q(pt,ft),wt(je,`background-color: ${_t??""}`),Q(ut,e(i).status.name)},[()=>Ce(e(i).issueDate),()=>({expired:new Date(e(i).expiryDate)<new Date&&e(i).status.name!=="Accepted"}),()=>Ce(e(i).expiryDate),()=>Ue(e(i).totalAmount),()=>Xe(e(i).status)],Ie),xt("click",D,()=>Qe(e(i))),l(j,D)}),a(n),l(o,n)};C(h,o=>{e(ne).length===0?o(z):o(A,!1)})}Te(te,()=>e(J),o=>v(J,o)),St(M,()=>e(I),o=>v(I,o)),l(G,H)};C(K,G=>{e(ce)?G(P):G(Z,!1)})}l(t,d)},nt=(t,d)=>{{var K=P=>{var Z=Ge(),G=$e(Z);{var H=E=>{var L=Kt(),ee=s(L);He(ee,{}),b(2),a(L),l(E,L)},O=E=>{var L=ta(),ee=s(L);{var oe=N=>{var q=Zt(),x=s(q),F=r(s(x),2);B(F,{variant:"primary",$$events:{click:Se},children:(_,g)=>{b();var h=S("Create New Template");l(_,h)},$$slots:{default:!0}}),a(x);var te=r(x,2);{var R=_=>{var g=Ot(),h=r(s(g),4);B(h,{variant:"primary",$$events:{click:Se},children:(z,A)=>{b();var o=S("Create Template");l(z,o)},$$slots:{default:!0}}),a(g),l(_,g)},M=_=>{var g=Yt();xe(g,5,()=>e(pe),h=>h.id,(h,z)=>{var A=Xt(),o=s(A),n=s(o),p=s(n,!0);a(n);var j=r(n,2);{var i=f=>{var w=Rt();l(f,w)};C(j,f=>{e(z).isDefault&&f(i)})}a(o);var D=r(o,2);{var $=f=>{var w=Wt(),V=s(w,!0);a(w),X(()=>Q(V,e(z).description)),l(f,w)};C(D,f=>{e(z).description&&f($)})}var W=r(D,2),m=s(W);B(m,{variant:"secondary",size:"small",$$events:{click:()=>st(e(z))},children:(f,w)=>{b();var V=S("Edit");l(f,V)},$$slots:{default:!0}});var T=r(m,2);B(T,{variant:"tertiary",size:"small",$$events:{click:()=>it(e(z))},children:(f,w)=>{b();var V=S("Delete");l(f,V)},$$slots:{default:!0}}),a(W),a(A),X(()=>Q(p,e(z).name)),l(h,A)}),a(g),l(_,g)};C(te,_=>{e(pe).length===0?_(R):_(M,!1)})}a(q),l(N,q)},ve=N=>{var q=ea(),x=s(q),F=s(x),te=s(F,!0);a(F);var R=r(F,2),M=s(R);B(M,{variant:"tertiary",$$events:{click:ke},children:(i,D)=>{b();var $=S("Cancel");l(i,$)},$$slots:{default:!0}});var _=r(M,2);B(_,{variant:"primary",get disabled(){return e(ie)},$$events:{click:lt},children:(i,D)=>{b();var $=S();X(()=>Q($,e(ie)?"Saving...":"Save Template")),l(i,$)},$$slots:{default:!0}}),a(R),a(x);var g=r(x,2),h=s(g),z=r(s(h),2);De(z),a(h);var A=r(h,2),o=r(s(A),2);Dt(o),a(A);var n=r(A,2),p=s(n),j=s(p);De(j),b(),a(p),a(n),a(g),a(q),X(()=>Q(te,e(Y)?"Edit Template":"Create New Template")),Te(z,()=>e(c).name,i=>be(c,e(c).name=i)),Te(o,()=>e(c).description,i=>be(c,e(c).description=i)),Ct(j,()=>e(c).isDefault,i=>be(c,e(c).isDefault=i)),l(N,q)};C(ee,N=>{e(le)?N(ve,!1):N(oe)})}a(L),l(E,L)};C(G,E=>{e(ue)?E(H):E(O,!1)})}l(P,Z)};C(t,P=>{e(re)==="designer"&&P(K)},d)}};C(ot,t=>{e(re)==="quotes"?t(vt):t(nt,!1)})}a(Le),a(fe),l(Je,fe),jt()}export{Da as component};
