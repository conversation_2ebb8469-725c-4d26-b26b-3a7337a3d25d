import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{s as e,i as s,o as n,r as t,k as i,m as te,j as V,t as Fe,e as He,n as Qe,g as Ue,f as Xe,ae as Je}from"../chunks/p3DoyA09.js";import{e as Re,s as ge,h as Ze}from"../chunks/BUelSUke.js";import{t as p,b as c,a}from"../chunks/B67foYpL.js";import{i as C}from"../chunks/DwdToawP.js";import{P as re}from"../chunks/CC9utfo3.js";import{V as Pe}from"../chunks/Bg6zarrZ.js";import{p as Ge}from"../chunks/Bfc47y5P.js";import{B as d}from"../chunks/6Zk3JFqZ.js";import{S as _e}from"../chunks/BYzA1sSu.js";import{G as es}from"../chunks/DEPinlMt.js";import{M as Ae}from"../chunks/BWn8tY11.js";import{T as Be}from"../chunks/BIVs0YJ0.js";import{d as ss}from"../chunks/DdRd56Yq.js";import{L as ne}from"../chunks/C8F602cz.js";import{i as Oe}from"../chunks/D3pqaimu.js";import{T as ts}from"../chunks/RoTT3oWY.js";import{a as ee}from"../chunks/Ce-0qAhV.js";var as=p('<div class="component-docs-container"><h1 class="svelte-139t231">Button Components</h1> <p class="description svelte-139t231">Examples and usage of the Button component with different variants, sizes, and states.</p> <section class="svelte-139t231"><h2 class="svelte-139t231">Variants</h2> <div class="button-row svelte-139t231"><!> <!> <!></div></section> <section class="svelte-139t231"><h2 class="svelte-139t231">Sizes</h2> <div class="button-row svelte-139t231"><!> <!> <!></div></section> <section class="svelte-139t231"><h2 class="svelte-139t231">With Icons</h2> <div class="button-row svelte-139t231"><!> <!> <!></div></section> <section class="svelte-139t231"><h2 class="svelte-139t231">Disabled State</h2> <div class="button-row svelte-139t231"><!> <!> <!></div></section> <section class="svelte-139t231"><h2 class="svelte-139t231">Form Submission</h2> <form><div class="form-row svelte-139t231"><input type="text" placeholder="Enter some text..." class="svelte-139t231"> <!></div></form></section></div>');function ls(X){const b=()=>`
    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
      <line x1="12" y1="5" x2="12" y2="19"></line>
      <line x1="5" y1="12" x2="19" y2="12"></line>
    </svg>
  `;function l(){console.log("Button clicked!")}var E=as(),M=e(s(E),4),P=e(s(M),2),z=s(P);d(z,{$$events:{click:l},children:(r,y)=>{n();var u=c("Primary Button");a(r,u)},$$slots:{default:!0}});var _=e(z,2);d(_,{variant:"secondary",$$events:{click:l},children:(r,y)=>{n();var u=c("Secondary Button");a(r,u)},$$slots:{default:!0}});var R=e(_,2);d(R,{variant:"tertiary",$$events:{click:l},children:(r,y)=>{n();var u=c("Tertiary Button");a(r,u)},$$slots:{default:!0}}),t(P),t(M);var k=e(M,2),W=e(s(k),2),j=s(W);d(j,{size:"small",$$events:{click:l},children:(r,y)=>{n();var u=c("Small Button");a(r,u)},$$slots:{default:!0}});var G=e(j,2);d(G,{size:"medium",$$events:{click:l},children:(r,y)=>{n();var u=c("Medium Button");a(r,u)},$$slots:{default:!0}});var I=e(G,2);d(I,{$$events:{click:l},children:(r,y)=>{n();var u=c("Default Size");a(r,u)},$$slots:{default:!0}}),t(W),t(k);var L=e(k,2),O=e(s(L),2),Y=s(O);d(Y,{icon:b,$$events:{click:l},children:(r,y)=>{n();var u=c("Add Item");a(r,u)},$$slots:{default:!0}});var D=e(Y,2);d(D,{variant:"secondary",icon:b,$$events:{click:l},children:(r,y)=>{n();var u=c("Add Item");a(r,u)},$$slots:{default:!0}});var q=e(D,2);d(q,{variant:"tertiary",icon:b,$$events:{click:l},children:(r,y)=>{n();var u=c("Add Item");a(r,u)},$$slots:{default:!0}}),t(O),t(L);var F=e(L,2),$=e(s(F),2),A=s($);d(A,{disabled:!0,children:(r,y)=>{n();var u=c("Disabled Primary");a(r,u)},$$slots:{default:!0}});var Q=e(A,2);d(Q,{variant:"secondary",disabled:!0,children:(r,y)=>{n();var u=c("Disabled Secondary");a(r,u)},$$slots:{default:!0}});var N=e(Q,2);d(N,{variant:"tertiary",disabled:!0,children:(r,y)=>{n();var u=c("Disabled Tertiary");a(r,u)},$$slots:{default:!0}}),t($),t(F);var K=e(F,2),S=e(s(K),2),Z=s(S),h=e(s(Z),2);d(h,{type:"submit",children:(r,y)=>{n();var u=c("Submit Form");a(r,u)},$$slots:{default:!0}}),t(Z),t(S),t(K),t(E),Re("submit",S,Ge(()=>alert("Form submitted!"))),a(X,E)}var is=p('<div class="component-docs-container"><h1>Stat Card Components</h1> <p class="description">A reusable component for displaying statistics with a title and value.</p> <h3>Basic Usage</h3> <div class="example svelte-bnfyfi"><div class="stats-grid svelte-bnfyfi"><!> <!> <!></div></div> <h3>With Special Styling</h3> <div class="example svelte-bnfyfi"><div class="stats-grid svelte-bnfyfi"><!> <!> <!></div></div> <h3>Props</h3> <div class="props-table"><div class="prop-row"><div class="prop-name">title</div> <div class="prop-type">string</div> <div class="prop-description">The title/label for the statistic</div></div> <div class="prop-row"><div class="prop-name">value</div> <div class="prop-type">string | number</div> <div class="prop-description">The value to display</div></div> <div class="prop-row"><div class="prop-name">valueClass</div> <div class="prop-type">string | undefined</div> <div class="prop-description">Optional CSS class for special styling (e.g., "overdue")</div></div></div></div>');function os(X){var b=is(),l=e(s(b),6),E=s(l),M=s(E);_e(M,{title:"Total Users",value:1234});var P=e(M,2);_e(P,{title:"Revenue",value:"$45,678"});var z=e(P,2);_e(z,{title:"Orders",value:89}),t(E),t(l);var _=e(l,4),R=s(_),k=s(R);_e(k,{title:"Overdue Items",value:5,valueClass:"overdue"});var W=e(k,2);_e(W,{title:"Active Users",value:2456});var j=e(W,2);_e(j,{title:"Pending",value:12}),t(R),t(_),n(4),t(b),a(X,b)}var ns=p('<div class="component-docs-container"><h1>Grid Components</h1> <p class="description">Examples and usage of the Grid component with different content types and configurations.</p> <!></div>');function rs(X){const b=[{key:"id",text:"ID",sortable:!0},{key:"name",text:"Name",sortable:!0},{key:"age",text:"Age",sortable:!0}],l=[{id:1,name:"Alice",age:30},{id:2,name:"Bob",age:25},{id:3,name:"Charlie",age:35}];let E={key:"",direction:""},M=[{displayName:"Name",queryKey:"name",currentQuery:""}],P=te(10),z=te(1),_=l.length;function R(L){console.log("Sort by:",L)}function k(L){console.log("Search queries:",L)}function W(L){V(P,L),V(z,1),console.log("Items per page:",i(P))}function j(L){V(z,L),console.log("Current page:",i(z))}var G=ns(),I=e(s(G),4);es(I,{headers:b,dataRows:l,currentSort:E,onHeaderClick:R,searchFields:M,itemsPerPageOptions:[5,10,20],get itemsPerPage(){return i(P)},get currentPage(){return i(z)},totalItems:_,onSearch:k,onItemsPerPageChange:W,onPageChange:j}),t(G),a(X,G)}var vs=p('<p class="svelte-1nkl4r8">This is a basic modal with default styling and behavior.</p> <p class="svelte-1nkl4r8">You can close it by clicking the X button, clicking outside, or using the close button in the footer.</p>',1),cs=p('<div class="custom-content svelte-1nkl4r8"><h3 class="svelte-1nkl4r8">Custom Content Example</h3> <p class="svelte-1nkl4r8">This modal contains custom content with different styling.</p> <ul class="svelte-1nkl4r8"><li class="svelte-1nkl4r8">Custom HTML content</li> <li class="svelte-1nkl4r8">Lists and formatted text</li> <li class="svelte-1nkl4r8">Multiple paragraphs</li></ul> <p class="svelte-1nkl4r8">The footer also has custom actions.</p></div>'),ds=p('<div slot="footer" class="custom-footer svelte-1nkl4r8"><!> <!></div>'),ps=p('<form class="modal-form svelte-1nkl4r8"><div class="form-group svelte-1nkl4r8"><label for="name" class="svelte-1nkl4r8">Name:</label> <input type="text" id="name" required class="svelte-1nkl4r8"></div> <div class="form-group svelte-1nkl4r8"><label for="email" class="svelte-1nkl4r8">Email:</label> <input type="email" id="email" required class="svelte-1nkl4r8"></div> <div class="form-group svelte-1nkl4r8"><label for="message" class="svelte-1nkl4r8">Message:</label> <textarea id="message" rows="4" required class="svelte-1nkl4r8"></textarea></div></form>'),us=p('<div slot="footer" class="form-footer svelte-1nkl4r8"><!> <!></div>'),hs=p('<div class="component-docs-container"><h1 class="svelte-1nkl4r8">Modal Components</h1> <p class="description svelte-1nkl4r8">Examples and usage of the Modal component with different content types and configurations.</p> <section class="svelte-1nkl4r8"><h2 class="svelte-1nkl4r8">Basic Modal</h2> <p class="svelte-1nkl4r8">Simple modal with default content and close functionality.</p> <!> <!></section> <section class="svelte-1nkl4r8"><h2 class="svelte-1nkl4r8">Custom Content Modal</h2> <p class="svelte-1nkl4r8">Modal with custom content and footer actions.</p> <!> <!></section> <section class="svelte-1nkl4r8"><h2 class="svelte-1nkl4r8">Form Modal</h2> <p class="svelte-1nkl4r8">Modal containing a form with validation and submission.</p> <!> <!></section></div>');function ms(X){let b=te(!1),l=te(!1),E=te(!1);function M(){V(b,!0)}function P(){V(l,!0)}function z(){V(E,!0)}function _(){alert("Form submitted!"),V(E,!1)}var R=hs(),k=e(s(R),4),W=e(s(k),4);d(W,{$$events:{click:M},children:(q,F)=>{n();var $=c("Open Basic Modal");a(q,$)},$$slots:{default:!0}});var j=e(W,2);Ae(j,{title:"Basic Modal",get show(){return i(b)},set show(q){V(b,q)},children:(q,F)=>{var $=vs();n(2),a(q,$)},$$slots:{default:!0},$$legacy:!0}),t(k);var G=e(k,2),I=e(s(G),4);d(I,{variant:"secondary",$$events:{click:P},children:(q,F)=>{n();var $=c("Open Custom Modal");a(q,$)},$$slots:{default:!0}});var L=e(I,2);Ae(L,{title:"Custom Content",get show(){return i(l)},set show(q){V(l,q)},children:(q,F)=>{var $=cs();a(q,$)},$$slots:{default:!0,footer:(q,F)=>{var $=ds(),A=s($);d(A,{variant:"tertiary",$$events:{click:()=>V(l,!1)},children:(N,K)=>{n();var S=c("Cancel");a(N,S)},$$slots:{default:!0}});var Q=e(A,2);d(Q,{$$events:{click:()=>{alert("Action performed!"),V(l,!1)}},children:(N,K)=>{n();var S=c("Perform Action");a(N,S)},$$slots:{default:!0}}),t($),a(q,$)}},$$legacy:!0}),t(G);var O=e(G,2),Y=e(s(O),4);d(Y,{variant:"tertiary",$$events:{click:z},children:(q,F)=>{n();var $=c("Open Form Modal");a(q,$)},$$slots:{default:!0}});var D=e(Y,2);Ae(D,{title:"Contact Form",get show(){return i(E)},set show(q){V(E,q)},children:(q,F)=>{var $=ps();Re("submit",$,Ge(_)),a(q,$)},$$slots:{default:!0,footer:(q,F)=>{var $=us(),A=s($);d(A,{variant:"tertiary",$$events:{click:()=>V(E,!1)},children:(N,K)=>{n();var S=c("Cancel");a(N,S)},$$slots:{default:!0}});var Q=e(A,2);d(Q,{type:"submit",$$events:{click:_},children:(N,K)=>{n();var S=c("Send Message");a(N,S)},$$slots:{default:!0}}),t($),a(q,$)}},$$legacy:!0}),t(O),t(R),a(X,R)}var fs=p('<div class="content-panel svelte-tlqsbh"><h3 class="svelte-tlqsbh">Tab 1 Content</h3> <p class="svelte-tlqsbh">This is the content for the first tab. It contains some sample text to demonstrate how tab content works.</p></div>'),bs=p('<div class="content-panel svelte-tlqsbh"><h3 class="svelte-tlqsbh">Tab 2 Content</h3> <p class="svelte-tlqsbh">This is the content for the second tab. Each tab can have completely different content and layout.</p> <ul class="svelte-tlqsbh"><li class="svelte-tlqsbh">List item 1</li> <li class="svelte-tlqsbh">List item 2</li> <li class="svelte-tlqsbh">List item 3</li></ul></div>'),gs=p('<div class="content-panel svelte-tlqsbh"><h3 class="svelte-tlqsbh">Tab 3 Content</h3> <p class="svelte-tlqsbh">This is the content for the third tab. You can include any type of content here.</p> <button class="demo-button svelte-tlqsbh">Sample Button</button></div>'),$s=p('<div class="content-panel svelte-tlqsbh"><h3 class="svelte-tlqsbh">Home</h3> <p class="svelte-tlqsbh">Welcome to the home page. Notice that the "Contact" tab is disabled and cannot be clicked.</p></div>'),_s=p('<div class="content-panel svelte-tlqsbh"><h3 class="svelte-tlqsbh">About</h3> <p class="svelte-tlqsbh">Learn more about our company and mission. The disabled tab demonstrates how to prevent access to certain sections.</p></div>'),xs=p('<div class="content-panel svelte-tlqsbh"><h3 class="svelte-tlqsbh">Services</h3> <p class="svelte-tlqsbh">Explore our range of services and offerings.</p></div>'),ys=p('<div class="content-panel rich-content svelte-tlqsbh"><h3 class="svelte-tlqsbh">Product Overview</h3> <p class="svelte-tlqsbh">Our product is designed to solve complex problems with simple, elegant solutions.</p> <div class="feature-grid svelte-tlqsbh"><div class="feature-item svelte-tlqsbh"><h4 class="svelte-tlqsbh">Fast</h4> <p class="svelte-tlqsbh">Lightning-fast performance</p></div> <div class="feature-item svelte-tlqsbh"><h4 class="svelte-tlqsbh">Secure</h4> <p class="svelte-tlqsbh">Enterprise-grade security</p></div> <div class="feature-item svelte-tlqsbh"><h4 class="svelte-tlqsbh">Scalable</h4> <p class="svelte-tlqsbh">Grows with your business</p></div></div></div>'),qs=p('<div class="content-panel rich-content svelte-tlqsbh"><h3 class="svelte-tlqsbh">Key Features</h3> <ul class="feature-list svelte-tlqsbh"><li class="svelte-tlqsbh">✅ Real-time collaboration</li> <li class="svelte-tlqsbh">✅ Advanced analytics</li> <li class="svelte-tlqsbh">✅ Custom integrations</li> <li class="svelte-tlqsbh">✅ 24/7 support</li> <li class="svelte-tlqsbh">✅ Mobile-first design</li></ul></div>'),ks=p('<div class="content-panel rich-content svelte-tlqsbh"><h3 class="svelte-tlqsbh">Pricing Plans</h3> <div class="pricing-grid svelte-tlqsbh"><div class="pricing-card svelte-tlqsbh"><h4 class="svelte-tlqsbh">Starter</h4> <div class="price svelte-tlqsbh">$9/month</div> <p class="svelte-tlqsbh">Perfect for small teams</p></div> <div class="pricing-card svelte-tlqsbh"><h4 class="svelte-tlqsbh">Professional</h4> <div class="price svelte-tlqsbh">$29/month</div> <p class="svelte-tlqsbh">For growing businesses</p></div> <div class="pricing-card svelte-tlqsbh"><h4 class="svelte-tlqsbh">Enterprise</h4> <div class="price svelte-tlqsbh">Custom</div> <p class="svelte-tlqsbh">For large organizations</p></div></div></div>'),ws=p('<div class="content-panel rich-content svelte-tlqsbh"><h3 class="svelte-tlqsbh">Support & Resources</h3> <p class="svelte-tlqsbh">Get help when you need it with our comprehensive support options.</p> <div class="support-options svelte-tlqsbh"><div class="support-item svelte-tlqsbh"><h4 class="svelte-tlqsbh">📚 Documentation</h4> <p class="svelte-tlqsbh">Comprehensive guides and tutorials</p></div> <div class="support-item svelte-tlqsbh"><h4 class="svelte-tlqsbh">💬 Live Chat</h4> <p class="svelte-tlqsbh">Real-time support during business hours</p></div> <div class="support-item svelte-tlqsbh"><h4 class="svelte-tlqsbh">📧 Email Support</h4> <p class="svelte-tlqsbh">Get help via email within 24 hours</p></div></div></div>'),Ps=p('<div class="component-docs-container"><h1>Tabs Components</h1> <p class="description">Examples and usage of the Tabs component with different configurations and states.</p> <section class="svelte-tlqsbh"><h2 class="svelte-tlqsbh">Basic Tabs</h2> <p class="svelte-tlqsbh">Simple tab navigation with basic functionality.</p> <!> <div class="tab-content svelte-tlqsbh"><!></div></section> <section class="svelte-tlqsbh"><h2 class="svelte-tlqsbh">Tabs with Disabled State</h2> <p class="svelte-tlqsbh">Tabs can be disabled to prevent user interaction.</p> <!> <div class="tab-content svelte-tlqsbh"><!></div></section> <section class="svelte-tlqsbh"><h2 class="svelte-tlqsbh">Content-Rich Tabs</h2> <p class="svelte-tlqsbh">Tabs with more complex content and styling.</p> <!> <div class="tab-content svelte-tlqsbh"><!></div></section></div>');function zs(X){const b=[{id:"tab1",label:"Tab 1"},{id:"tab2",label:"Tab 2"},{id:"tab3",label:"Tab 3"}];let l=te("tab1");const E=[{id:"home",label:"Home"},{id:"about",label:"About"},{id:"contact",label:"Contact",disabled:!0},{id:"services",label:"Services"}];let M=te("home");const P=[{id:"overview",label:"Overview"},{id:"features",label:"Features"},{id:"pricing",label:"Pricing"},{id:"support",label:"Support"}];let z=te("overview");function _(h){console.log("Tab changed to:",h.detail.tabId)}var R=Ps(),k=e(s(R),4),W=e(s(k),4);Be(W,{tabs:b,get activeTab(){return i(l)},set activeTab(h){V(l,h)},$$events:{change:_},$$legacy:!0});var j=e(W,2),G=s(j);{var I=h=>{var r=fs();a(h,r)},L=(h,r)=>{{var y=m=>{var H=bs();a(m,H)},u=(m,H)=>{{var v=g=>{var x=gs();a(g,x)};C(m,g=>{i(l)==="tab3"&&g(v)},H)}};C(h,m=>{i(l)==="tab2"?m(y):m(u,!1)},r)}};C(G,h=>{i(l)==="tab1"?h(I):h(L,!1)})}t(j),t(k);var O=e(k,2),Y=e(s(O),4);Be(Y,{tabs:E,get activeTab(){return i(M)},set activeTab(h){V(M,h)},$$events:{change:_},$$legacy:!0});var D=e(Y,2),q=s(D);{var F=h=>{var r=$s();a(h,r)},$=(h,r)=>{{var y=m=>{var H=_s();a(m,H)},u=(m,H)=>{{var v=g=>{var x=xs();a(g,x)};C(m,g=>{i(M)==="services"&&g(v)},H)}};C(h,m=>{i(M)==="about"?m(y):m(u,!1)},r)}};C(q,h=>{i(M)==="home"?h(F):h($,!1)})}t(D),t(O);var A=e(O,2),Q=e(s(A),4);Be(Q,{tabs:P,get activeTab(){return i(z)},set activeTab(h){V(z,h)},$$events:{change:_},$$legacy:!0});var N=e(Q,2),K=s(N);{var S=h=>{var r=ys();a(h,r)},Z=(h,r)=>{{var y=m=>{var H=qs();a(m,H)},u=(m,H)=>{{var v=x=>{var w=ks();a(x,w)},g=(x,w)=>{{var T=o=>{var U=ws();a(o,U)};C(x,o=>{i(z)==="support"&&o(T)},w)}};C(m,x=>{i(z)==="pricing"?x(v):x(g,!1)},H)}};C(h,m=>{i(z)==="features"?m(y):m(u,!1)},r)}};C(K,h=>{i(z)==="overview"?h(S):h(Z,!1)})}t(N),t(A),t(R),a(X,R)}var Ss=p('<div class="loading-demo-container svelte-8nlz55"><!> <div class="progress-info svelte-8nlz55"><div class="progress-bar svelte-8nlz55"><div class="progress-fill svelte-8nlz55"></div></div> <span class="progress-text svelte-8nlz55"> </span></div></div>'),Ts=p('<div class="component-docs-container"><h1>Loading Spinner Components</h1> <p class="description">Examples and usage of the LoadingSpinner component with different sizes and messages.</p> <section class="svelte-8nlz55"><h2>Different Sizes</h2> <p class="svelte-8nlz55">Loading spinners come in three sizes: small, medium, and large.</p> <div class="spinner-row svelte-8nlz55"><div class="spinner-demo svelte-8nlz55"><h4>Small</h4> <!></div> <div class="spinner-demo svelte-8nlz55"><h4>Medium (Default)</h4> <!></div> <div class="spinner-demo svelte-8nlz55"><h4>Large</h4> <!></div></div></section> <section class="svelte-8nlz55"><h2>Custom Messages</h2> <p class="svelte-8nlz55">Customize the loading message to provide context to users.</p> <div class="spinner-row svelte-8nlz55"><div class="spinner-demo svelte-8nlz55"><!></div> <div class="spinner-demo svelte-8nlz55"><!></div> <div class="spinner-demo svelte-8nlz55"><!></div></div></section> <section class="svelte-8nlz55"><h2>No Message</h2> <p class="svelte-8nlz55">Loading spinner without any text message.</p> <div class="spinner-row svelte-8nlz55"><div class="spinner-demo svelte-8nlz55"><!></div></div></section> <section class="svelte-8nlz55"><h2>Interactive Demo</h2> <p class="svelte-8nlz55">Click the button to see a simulated loading process.</p> <div class="interactive-demo svelte-8nlz55"><!></div></section> <section class="svelte-8nlz55"><h2>In Context Examples</h2> <p class="svelte-8nlz55">Examples of how loading spinners might appear in real application contexts.</p> <div class="context-examples svelte-8nlz55"><div class="context-card svelte-8nlz55"><h4 class="svelte-8nlz55">Card Loading State</h4> <div class="card-content svelte-8nlz55"><!></div></div> <div class="context-card svelte-8nlz55"><h4 class="svelte-8nlz55">Form Submission</h4> <div class="form-example svelte-8nlz55"><input type="text" placeholder="Enter your name" disabled class="svelte-8nlz55"> <input type="email" placeholder="Enter your email" disabled class="svelte-8nlz55"> <div class="form-loading svelte-8nlz55"><!></div></div></div> <div class="context-card svelte-8nlz55"><h4 class="svelte-8nlz55">Data Table Loading</h4> <div class="table-loading svelte-8nlz55"><!></div></div></div></section></div>');function Cs(X){let b=te(!1),l=te(0);function E(){V(b,!0),V(l,0);const oe=setInterval(()=>{V(l,i(l)+10),i(l)>=100&&(clearInterval(oe),setTimeout(()=>{V(b,!1),V(l,0)},1e3))},200)}var M=Ts(),P=e(s(M),4),z=e(s(P),4),_=s(z),R=e(s(_),2);ne(R,{size:"small",message:"Loading..."}),t(_);var k=e(_,2),W=e(s(k),2);ne(W,{size:"medium",message:"Loading..."}),t(k);var j=e(k,2),G=e(s(j),2);ne(G,{size:"large",message:"Loading..."}),t(j),t(z),t(P);var I=e(P,2),L=e(s(I),4),O=s(L),Y=s(O);ne(Y,{message:"Saving your changes..."}),t(O);var D=e(O,2),q=s(D);ne(q,{message:"Uploading files..."}),t(D);var F=e(D,2),$=s(F);ne($,{message:"Processing data..."}),t(F),t(L),t(I);var A=e(I,2),Q=e(s(A),4),N=s(Q),K=s(N);ne(K,{message:""}),t(N),t(Q),t(A);var S=e(A,2),Z=e(s(S),4),h=s(Z);{var r=oe=>{d(oe,{$$events:{click:E},children:(ce,ue)=>{n();var ae=c("Start Loading Demo");a(ce,ae)},$$slots:{default:!0}})},y=oe=>{var ce=Ss(),ue=s(ce);ne(ue,{message:"Processing your request..."});var ae=e(ue,2),de=s(ae),xe=s(de);t(de);var pe=e(de,2),ye=s(pe);t(pe),t(ae),t(ce),Fe(()=>{ss(xe,`width: ${i(l)??""}%`),ge(ye,`${i(l)??""}% Complete`)}),a(oe,ce)};C(h,oe=>{i(b)?oe(y,!1):oe(r)})}t(Z),t(S);var u=e(S,2),m=e(s(u),4),H=s(m),v=e(s(H),2),g=s(v);ne(g,{size:"small",message:"Loading content..."}),t(v),t(H);var x=e(H,2),w=e(s(x),2),T=e(s(w),4),o=s(T);ne(o,{size:"small",message:"Submitting form..."}),t(T),t(w),t(x);var U=e(x,2),B=e(s(U),2),ve=s(B);ne(ve,{message:"Loading table data..."}),t(B),t(U),t(m),t(u),t(M),a(X,M)}var Ms=p('<div slot="actions"><!></div>'),Es=p('<div slot="actions"><!> <!> <!></div>'),Is=p('<div slot="actions" class="mixed-actions svelte-nkbz4t"><select class="action-select svelte-nkbz4t"><option>Last 7 days</option><option>Last 30 days</option><option>Last 90 days</option></select> <!> <!></div>'),Ls=p('<div slot="actions"><!></div>'),As=p('<div slot="actions"><!> <!></div>'),Bs=p('<div slot="actions"><!> <!></div>'),Ds=p('<div slot="actions"><!> <!></div>'),Fs=p('<div class="component-docs-container"><h1>Page Header Components</h1> <p class="description">Examples and usage of the PageHeader component with different configurations and action buttons.</p> <section class="svelte-nkbz4t"><h2 class="svelte-nkbz4t">Basic Page Header</h2> <p class="svelte-nkbz4t">Simple page header with just a title.</p> <div class="example-container svelte-nkbz4t"><!></div></section> <section class="svelte-nkbz4t"><h2 class="svelte-nkbz4t">Page Header with Single Action</h2> <p class="svelte-nkbz4t">Page header with a single action button in the actions slot.</p> <div class="example-container svelte-nkbz4t"><!></div></section> <section class="svelte-nkbz4t"><h2 class="svelte-nkbz4t">Page Header with Multiple Actions</h2> <p class="svelte-nkbz4t">Page header with multiple action buttons showing different variants.</p> <div class="example-container svelte-nkbz4t"><!></div></section> <section class="svelte-nkbz4t"><h2 class="svelte-nkbz4t">Page Header with Mixed Actions</h2> <p class="svelte-nkbz4t">Page header with a mix of buttons and other action elements.</p> <div class="example-container svelte-nkbz4t"><!></div></section> <section class="svelte-nkbz4t"><h2 class="svelte-nkbz4t">Different Title Lengths</h2> <p class="svelte-nkbz4t">Examples showing how the component handles different title lengths.</p> <div class="title-examples svelte-nkbz4t"><div class="example-container svelte-nkbz4t"><!></div> <div class="example-container svelte-nkbz4t"><!></div> <div class="example-container svelte-nkbz4t"><!></div></div></section> <section class="svelte-nkbz4t"><h2 class="svelte-nkbz4t">Real-world Examples</h2> <p class="svelte-nkbz4t">Examples of how page headers might appear in different application contexts.</p> <div class="context-examples svelte-nkbz4t"><div class="context-example svelte-nkbz4t"><h4 class="svelte-nkbz4t">E-commerce Admin</h4> <!></div> <div class="context-example svelte-nkbz4t"><h4 class="svelte-nkbz4t">Content Management</h4> <!></div> <div class="context-example svelte-nkbz4t"><h4 class="svelte-nkbz4t">Team Management</h4> <!></div></div></section></div>');function Hs(X){function b(){alert("Action clicked!")}var l=Fs(),E=e(s(l),4),M=e(s(E),4),P=s(M);re(P,{title:"Dashboard"}),t(M),t(E);var z=e(E,2),_=e(s(z),4),R=s(_);re(R,{title:"User Management",$$slots:{actions:(m,H)=>{var v=Ms(),g=s(v);d(g,{$$events:{click:b},children:(x,w)=>{n();var T=c("Add User");a(x,T)},$$slots:{default:!0}}),t(v),a(m,v)}}}),t(_),t(z);var k=e(z,2),W=e(s(k),4),j=s(W);re(j,{title:"Project Settings",$$slots:{actions:(m,H)=>{var v=Es(),g=s(v);d(g,{variant:"tertiary",$$events:{click:b},children:(T,o)=>{n();var U=c("Cancel");a(T,U)},$$slots:{default:!0}});var x=e(g,2);d(x,{variant:"secondary",$$events:{click:b},children:(T,o)=>{n();var U=c("Save Draft");a(T,U)},$$slots:{default:!0}});var w=e(x,2);d(w,{$$events:{click:b},children:(T,o)=>{n();var U=c("Publish");a(T,U)},$$slots:{default:!0}}),t(v),a(m,v)}}}),t(W),t(k);var G=e(k,2),I=e(s(G),4),L=s(I);re(L,{title:"Analytics Dashboard",$$slots:{actions:(m,H)=>{var v=Is(),g=e(s(v),2);d(g,{variant:"secondary",$$events:{click:b},children:(w,T)=>{n();var o=c("Export");a(w,o)},$$slots:{default:!0}});var x=e(g,2);d(x,{$$events:{click:b},children:(w,T)=>{n();var o=c("Refresh");a(w,o)},$$slots:{default:!0}}),t(v),a(m,v)}}}),t(I),t(G);var O=e(G,2),Y=e(s(O),4),D=s(Y),q=s(D);re(q,{title:"Home"}),t(D);var F=e(D,2),$=s(F);re($,{title:"Customer Relationship Management"}),t(F);var A=e(F,2),Q=s(A);re(Q,{title:"Advanced Analytics and Reporting Dashboard with Real-time Data",$$slots:{actions:(m,H)=>{var v=Ls(),g=s(v);d(g,{size:"small",$$events:{click:b},children:(x,w)=>{n();var T=c("Action");a(x,T)},$$slots:{default:!0}}),t(v),a(m,v)}}}),t(A),t(Y),t(O);var N=e(O,2),K=e(s(N),4),S=s(K),Z=e(s(S),2);re(Z,{title:"Products",$$slots:{actions:(m,H)=>{var v=As(),g=s(v);d(g,{variant:"tertiary",$$events:{click:b},children:(w,T)=>{n();var o=c("Import");a(w,o)},$$slots:{default:!0}});var x=e(g,2);d(x,{$$events:{click:b},children:(w,T)=>{n();var o=c("Add Product");a(w,o)},$$slots:{default:!0}}),t(v),a(m,v)}}}),t(S);var h=e(S,2),r=e(s(h),2);re(r,{title:"Blog Posts",$$slots:{actions:(m,H)=>{var v=Bs(),g=s(v);d(g,{variant:"secondary",$$events:{click:b},children:(w,T)=>{n();var o=c("Drafts (3)");a(w,o)},$$slots:{default:!0}});var x=e(g,2);d(x,{$$events:{click:b},children:(w,T)=>{n();var o=c("New Post");a(w,o)},$$slots:{default:!0}}),t(v),a(m,v)}}}),t(h);var y=e(h,2),u=e(s(y),2);re(u,{title:"Team Members",$$slots:{actions:(m,H)=>{var v=Ds(),g=s(v);d(g,{variant:"tertiary",$$events:{click:b},children:(w,T)=>{n();var o=c("Invite Link");a(w,o)},$$slots:{default:!0}});var x=e(g,2);d(x,{$$events:{click:b},children:(w,T)=>{n();var o=c("Invite Member");a(w,o)},$$slots:{default:!0}}),t(v),a(m,v)}}}),t(y),t(K),t(N),t(l),a(X,l)}var Us=p(`<p class="svelte-100uiqv">Welcome to our homepage! Here you'll find the latest updates and featured content.</p>`),Rs=p('<p class="svelte-100uiqv">Learn more about our company, mission, and the team behind our success.</p>'),Gs=p('<p class="svelte-100uiqv">Discover our comprehensive range of services designed to meet your needs.</p>'),Os=p('<p class="svelte-100uiqv">Browse through our portfolio of successful projects and case studies.</p>'),Ns=p('<p class="svelte-100uiqv">Get in touch with us through our contact form or find our office locations.</p>'),Vs=p('<p class="svelte-100uiqv">Dashboard overview with key metrics and system status.</p> <div class="stats-grid svelte-100uiqv"><div class="stat-item svelte-100uiqv"><div class="stat-value svelte-100uiqv">1,234</div> <div class="stat-label svelte-100uiqv">Total Users</div></div> <div class="stat-item svelte-100uiqv"><div class="stat-value svelte-100uiqv">567</div> <div class="stat-label svelte-100uiqv">Active Sessions</div></div></div>',1),Ws=p('<p class="svelte-100uiqv">Manage user accounts, permissions, and access levels.</p>'),js=p('<p class="svelte-100uiqv">Create, edit, and organize website content and media.</p>'),Ks=p('<p class="svelte-100uiqv">View detailed analytics and performance reports.</p>'),Ys=p('<p class="svelte-100uiqv">Configure system-wide settings and preferences.</p>'),Qs=p('<p class="svelte-100uiqv">Manage security settings, authentication, and access controls.</p>'),Xs=p('<p class="svelte-100uiqv">Schedule backups and restore system data when needed.</p>'),Js=p('<div class="component-docs-container"><h1>Vertical Menu Components</h1> <p class="description">Examples and usage of the VerticalMenu component for navigation and selection interfaces.</p> <section class="svelte-100uiqv"><h2 class="svelte-100uiqv">Basic Vertical Menu</h2> <p class="svelte-100uiqv">Simple vertical menu with basic navigation items.</p> <div class="menu-demo svelte-100uiqv"><div class="menu-container svelte-100uiqv"><!></div> <div class="content-display svelte-100uiqv"><h3 class="svelte-100uiqv"> </h3> <p class="svelte-100uiqv"> </p></div></div></section> <section class="svelte-100uiqv"><h2 class="svelte-100uiqv">Website Navigation Menu</h2> <p class="svelte-100uiqv">Vertical menu used for website navigation with multiple pages.</p> <div class="menu-demo svelte-100uiqv"><div class="menu-container svelte-100uiqv"><!></div> <div class="content-display svelte-100uiqv"><h3 class="svelte-100uiqv"> </h3> <!></div></div></section> <section class="svelte-100uiqv"><h2 class="svelte-100uiqv">Admin Dashboard Menu</h2> <p class="svelte-100uiqv">Comprehensive admin menu with multiple management sections.</p> <div class="menu-demo svelte-100uiqv"><div class="menu-container svelte-100uiqv"><!></div> <div class="content-display svelte-100uiqv"><h3 class="svelte-100uiqv"> </h3> <!></div></div></section> <section class="svelte-100uiqv"><h2 class="svelte-100uiqv">Category Filter Menu</h2> <p class="svelte-100uiqv">Vertical menu used for filtering content by categories.</p> <div class="menu-demo svelte-100uiqv"><div class="menu-container svelte-100uiqv"><!></div> <div class="content-display svelte-100uiqv"><h3 class="svelte-100uiqv"> </h3> <p class="svelte-100uiqv"> </p> <div class="product-grid svelte-100uiqv"><div class="product-item svelte-100uiqv">Product 1</div> <div class="product-item svelte-100uiqv">Product 2</div> <div class="product-item svelte-100uiqv">Product 3</div> <div class="product-item svelte-100uiqv">Product 4</div></div></div></div></section></div>');function Zs(X,b){He(b,!1);const l=[{id:"dashboard",label:"Dashboard"},{id:"users",label:"Users"},{id:"settings",label:"Settings"},{id:"reports",label:"Reports"}];let E=te("dashboard");const M=[{id:"home",label:"Home"},{id:"about",label:"About Us"},{id:"services",label:"Services"},{id:"portfolio",label:"Portfolio"},{id:"contact",label:"Contact"}];let P=te("home");const z=[{id:"overview",label:"Overview"},{id:"user-management",label:"User Management"},{id:"content-management",label:"Content Management"},{id:"analytics",label:"Analytics"},{id:"system-settings",label:"System Settings"},{id:"security",label:"Security"},{id:"backup",label:"Backup & Restore"}];let _=te("overview");const R=[{id:"electronics",label:"Electronics"},{id:"clothing",label:"Clothing & Fashion"},{id:"home-garden",label:"Home & Garden"},{id:"sports",label:"Sports & Outdoors"},{id:"books",label:"Books & Media"},{id:"toys",label:"Toys & Games"}];let k=te("electronics");function W(f){V(E,f),console.log("Basic menu selected:",f)}function j(f){V(P,f),console.log("Navigation menu selected:",f)}function G(f){V(_,f),console.log("Admin menu selected:",f)}function I(f){V(k,f),console.log("Category menu selected:",f)}Oe();var L=Js(),O=e(s(L),4),Y=e(s(O),4),D=s(Y),q=s(D);Pe(q,{items:l,get currentItem(){return i(E)},onSelect:W}),t(D);var F=e(D,2),$=s(F),A=s($);t($);var Q=e($,2),N=s(Q);t(Q),t(F),t(Y),t(O);var K=e(O,2),S=e(s(K),4),Z=s(S),h=s(Z);Pe(h,{items:M,get currentItem(){return i(P)},onSelect:j}),t(Z);var r=e(Z,2),y=s(r),u=s(y,!0);t(y);var m=e(y,2);{var H=f=>{var J=Us();a(f,J)},v=(f,J)=>{{var qe=se=>{var me=Rs();a(se,me)},ke=(se,me)=>{{var ze=le=>{var fe=Gs();a(le,fe)},Se=(le,fe)=>{{var Te=ie=>{var be=Os();a(ie,be)},Ce=(ie,be)=>{{var Me=we=>{var he=Ns();a(we,he)};C(ie,we=>{i(P)==="contact"&&we(Me)},be)}};C(le,ie=>{i(P)==="portfolio"?ie(Te):ie(Ce,!1)},fe)}};C(se,le=>{i(P)==="services"?le(ze):le(Se,!1)},me)}};C(f,se=>{i(P)==="about"?se(qe):se(ke,!1)},J)}};C(m,f=>{i(P)==="home"?f(H):f(v,!1)})}t(r),t(S),t(K);var g=e(K,2),x=e(s(g),4),w=s(x),T=s(w);Pe(T,{items:z,get currentItem(){return i(_)},onSelect:G}),t(w);var o=e(w,2),U=s(o),B=s(U,!0);t(U);var ve=e(U,2);{var oe=f=>{var J=Vs();n(2),a(f,J)},ce=(f,J)=>{{var qe=se=>{var me=Ws();a(se,me)},ke=(se,me)=>{{var ze=le=>{var fe=js();a(le,fe)},Se=(le,fe)=>{{var Te=ie=>{var be=Ks();a(ie,be)},Ce=(ie,be)=>{{var Me=he=>{var Ee=Ys();a(he,Ee)},we=(he,Ee)=>{{var We=$e=>{var Ie=Qs();a($e,Ie)},je=($e,Ie)=>{{var Ke=Le=>{var Ye=Xs();a(Le,Ye)};C($e,Le=>{i(_)==="backup"&&Le(Ke)},Ie)}};C(he,$e=>{i(_)==="security"?$e(We):$e(je,!1)},Ee)}};C(ie,he=>{i(_)==="system-settings"?he(Me):he(we,!1)},be)}};C(le,ie=>{i(_)==="analytics"?ie(Te):ie(Ce,!1)},fe)}};C(se,le=>{i(_)==="content-management"?le(ze):le(Se,!1)},me)}};C(f,se=>{i(_)==="user-management"?se(qe):se(ke,!1)},J)}};C(ve,f=>{i(_)==="overview"?f(oe):f(ce,!1)})}t(o),t(x),t(g);var ue=e(g,2),ae=e(s(ue),4),de=s(ae),xe=s(de);Pe(xe,{items:R,get currentItem(){return i(k)},onSelect:I}),t(de);var pe=e(de,2),ye=s(pe),Ne=s(ye,!0);t(ye);var De=e(ye,2),Ve=s(De);t(De),n(2),t(pe),t(ae),t(ue),t(L),Fe((f,J,qe,ke,se)=>{ge(A,`Selected: ${i(E)??""}`),ge(N,`Content for the ${f??""} section would appear here.`),ge(u,J),ge(B,qe),ge(Ne,ke),ge(Ve,`Showing products in the ${se??""} category.`)},[()=>{var f;return(f=l.find(J=>J.id===i(E)))==null?void 0:f.label},()=>{var f;return(f=M.find(J=>J.id===i(P)))==null?void 0:f.label},()=>{var f;return(f=z.find(J=>J.id===i(_)))==null?void 0:f.label},()=>{var f;return(f=R.find(J=>J.id===i(k)))==null?void 0:f.label},()=>{var f;return(f=R.find(J=>J.id===i(k)))==null?void 0:f.label}],Qe),a(X,L),Ue()}var et=p('<div class="component-docs-container"><h1 class="svelte-1h7xlg7">Toasts Components</h1> <p class="description svelte-1h7xlg7">Examples and usage of the Toasts component for displaying notifications and feedback messages.</p> <section class="svelte-1h7xlg7"><h2 class="svelte-1h7xlg7">Basic Toast Types</h2> <p class="svelte-1h7xlg7">Different types of toast notifications for various scenarios.</p> <div class="button-grid svelte-1h7xlg7"><!> <!> <!> <!></div></section> <section class="svelte-1h7xlg7"><h2 class="svelte-1h7xlg7">Custom Duration</h2> <p class="svelte-1h7xlg7">Toasts with custom display durations.</p> <div class="button-grid svelte-1h7xlg7"><!> <!></div></section> <section class="svelte-1h7xlg7"><h2 class="svelte-1h7xlg7">Multiple Toasts</h2> <p class="svelte-1h7xlg7">Demonstrate how multiple toasts stack and display.</p> <div class="button-grid svelte-1h7xlg7"><!></div></section> <section class="svelte-1h7xlg7"><h2 class="svelte-1h7xlg7">Real-world Examples</h2> <p class="svelte-1h7xlg7">Examples of how toasts might be used in real application scenarios.</p> <div class="scenario-examples svelte-1h7xlg7"><div class="scenario-card svelte-1h7xlg7"><h4 class="svelte-1h7xlg7">Form Submission</h4> <p class="svelte-1h7xlg7">Simulate a form submission with progress and success feedback.</p> <!></div> <div class="scenario-card svelte-1h7xlg7"><h4 class="svelte-1h7xlg7">Error Handling</h4> <p class="svelte-1h7xlg7">Simulate a network error with appropriate error messaging.</p> <!></div> <div class="scenario-card svelte-1h7xlg7"><h4 class="svelte-1h7xlg7">User Actions</h4> <p class="svelte-1h7xlg7">Common user action feedback messages.</p> <div class="action-buttons svelte-1h7xlg7"><!> <!> <!></div></div></div></section> <section class="svelte-1h7xlg7"><h2 class="svelte-1h7xlg7">Toast Guidelines</h2> <div class="guidelines svelte-1h7xlg7"><div class="guideline-item svelte-1h7xlg7"><h4 class="svelte-1h7xlg7">✅ Success Toasts</h4> <p class="svelte-1h7xlg7">Use for successful operations, confirmations, and positive feedback.</p></div> <div class="guideline-item svelte-1h7xlg7"><h4 class="svelte-1h7xlg7">❌ Error Toasts</h4> <p class="svelte-1h7xlg7">Use for errors, failures, and critical issues that need user attention.</p></div> <div class="guideline-item svelte-1h7xlg7"><h4 class="svelte-1h7xlg7">⚠️ Warning Toasts</h4> <p class="svelte-1h7xlg7">Use for warnings, cautions, and situations requiring user awareness.</p></div> <div class="guideline-item svelte-1h7xlg7"><h4 class="svelte-1h7xlg7">ℹ️ Info Toasts</h4> <p class="svelte-1h7xlg7">Use for general information, tips, and neutral notifications.</p></div></div></section></div> <!>',1);function st(X,b){He(b,!1);function l(){ee({type:"success",message:"Operation completed successfully!"})}function E(){ee({type:"error",message:"An error occurred while processing your request."})}function M(){ee({type:"warning",message:"Please review your input before proceeding."})}function P(){ee({type:"info",message:"Here is some helpful information for you."})}function z(){ee({type:"info",message:"This is a longer toast message that demonstrates how the component handles multiple lines of text and wrapping content gracefully.",duration:5e3})}function _(){ee({type:"success",message:"This toast will disappear in 10 seconds.",duration:1e4})}function R(){ee({type:"info",message:"First toast message"}),setTimeout(()=>{ee({type:"success",message:"Second toast message"})},500),setTimeout(()=>{ee({type:"warning",message:"Third toast message"})},1e3)}function k(){ee({type:"info",message:"Submitting form..."}),setTimeout(()=>{ee({type:"success",message:"Form submitted successfully!"})},2e3)}function W(){ee({type:"info",message:"Processing request..."}),setTimeout(()=>{ee({type:"error",message:"Network error: Unable to connect to server."})},2e3)}Oe();var j=et(),G=Xe(j),I=e(s(G),4),L=e(s(I),4),O=s(L);d(O,{variant:"secondary",$$events:{click:l},children:(o,U)=>{n();var B=c("Show Success Toast");a(o,B)},$$slots:{default:!0}});var Y=e(O,2);d(Y,{variant:"secondary",$$events:{click:E},children:(o,U)=>{n();var B=c("Show Error Toast");a(o,B)},$$slots:{default:!0}});var D=e(Y,2);d(D,{variant:"secondary",$$events:{click:M},children:(o,U)=>{n();var B=c("Show Warning Toast");a(o,B)},$$slots:{default:!0}});var q=e(D,2);d(q,{variant:"secondary",$$events:{click:P},children:(o,U)=>{n();var B=c("Show Info Toast");a(o,B)},$$slots:{default:!0}}),t(L),t(I);var F=e(I,2),$=e(s(F),4),A=s($);d(A,{variant:"tertiary",$$events:{click:z},children:(o,U)=>{n();var B=c("Long Message (5s)");a(o,B)},$$slots:{default:!0}});var Q=e(A,2);d(Q,{variant:"tertiary",$$events:{click:_},children:(o,U)=>{n();var B=c("Custom Duration (10s)");a(o,B)},$$slots:{default:!0}}),t($),t(F);var N=e(F,2),K=e(s(N),4),S=s(K);d(S,{$$events:{click:R},children:(o,U)=>{n();var B=c("Show Multiple Toasts");a(o,B)},$$slots:{default:!0}}),t(K),t(N);var Z=e(N,2),h=e(s(Z),4),r=s(h),y=e(s(r),4);d(y,{variant:"secondary",$$events:{click:k},children:(o,U)=>{n();var B=c("Submit Form");a(o,B)},$$slots:{default:!0}}),t(r);var u=e(r,2),m=e(s(u),4);d(m,{variant:"secondary",$$events:{click:W},children:(o,U)=>{n();var B=c("Trigger Error");a(o,B)},$$slots:{default:!0}}),t(u);var H=e(u,2),v=e(s(H),4),g=s(v);d(g,{size:"small",$$events:{click:()=>ee({type:"success",message:"Item saved to favorites!"})},children:(o,U)=>{n();var B=c("Save to Favorites");a(o,B)},$$slots:{default:!0}});var x=e(g,2);d(x,{size:"small",$$events:{click:()=>ee({type:"info",message:"Item copied to clipboard!"})},children:(o,U)=>{n();var B=c("Copy Link");a(o,B)},$$slots:{default:!0}});var w=e(x,2);d(w,{size:"small",$$events:{click:()=>ee({type:"warning",message:"Item removed from cart."})},children:(o,U)=>{n();var B=c("Remove Item");a(o,B)},$$slots:{default:!0}}),t(v),t(H),t(h),t(Z),n(2),t(G);var T=e(G,2);ts(T,{}),a(X,j),Ue()}var tt=p('<div class="welcome-section svelte-1vbrsb5"><h2 class="svelte-1vbrsb5">Welcome to Components</h2> <p class="svelte-1vbrsb5">Select a component from the menu to view examples and documentation.</p></div>'),at=p('<div class="container"><!> <main><div class="vertical-sidebar-layout"><!> <div class="vertical-sidebar-content"><!></div></div></main></div>');function qt(X){const b=[{id:"buttons",label:"Buttons"},{id:"statcards",label:"Stat Cards"},{id:"grid",label:"Grid"},{id:"modal",label:"Modal"},{id:"tabs",label:"Tabs"},{id:"loading-spinner",label:"Loading Spinner"},{id:"page-header",label:"Page Header"},{id:"vertical-menu",label:"Vertical Menu"},{id:"toasts",label:"Toasts"}];let l=te("");function E(I){V(l,I)}var M=at();Ze(I=>{Je.title="Components"});var P=s(M);re(P,{title:"Components"});var z=e(P,2),_=s(z),R=s(_);Pe(R,{items:b,get currentItem(){return i(l)},onSelect:E});var k=e(R,2),W=s(k);{var j=I=>{var L=tt();a(I,L)},G=(I,L)=>{{var O=D=>{ls(D)},Y=(D,q)=>{{var F=A=>{os(A)},$=(A,Q)=>{{var N=S=>{rs(S)},K=(S,Z)=>{{var h=y=>{ms(y)},r=(y,u)=>{{var m=v=>{zs(v)},H=(v,g)=>{{var x=T=>{Cs(T)},w=(T,o)=>{{var U=ve=>{Hs(ve)},B=(ve,oe)=>{{var ce=ae=>{Zs(ae,{})},ue=(ae,de)=>{{var xe=pe=>{st(pe,{})};C(ae,pe=>{i(l)==="toasts"&&pe(xe)},de)}};C(ve,ae=>{i(l)==="vertical-menu"?ae(ce):ae(ue,!1)},oe)}};C(T,ve=>{i(l)==="page-header"?ve(U):ve(B,!1)},o)}};C(v,T=>{i(l)==="loading-spinner"?T(x):T(w,!1)},g)}};C(y,v=>{i(l)==="tabs"?v(m):v(H,!1)},u)}};C(S,y=>{i(l)==="modal"?y(h):y(r,!1)},Z)}};C(A,S=>{i(l)==="grid"?S(N):S(K,!1)},Q)}};C(D,A=>{i(l)==="statcards"?A(F):A($,!1)},q)}};C(I,D=>{i(l)==="buttons"?D(O):D(Y,!1)},L)}};C(W,I=>{i(l)?I(G,!1):I(j)})}t(k),t(_),t(z),t(M),a(X,M)}export{qt as component};
