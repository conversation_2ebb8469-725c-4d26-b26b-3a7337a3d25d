import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{f as B,ae as P,s as o,i as h,j as s,m,k as u,r as f,o as y}from"../chunks/p3DoyA09.js";import{h as S,e as l}from"../chunks/BUelSUke.js";import{t as r,a as n}from"../chunks/B67foYpL.js";import{M as p}from"../chunks/BWn8tY11.js";var E=r("<p>This is a basic modal with smooth animations!</p> <p>The backdrop fades in and the modal scales in with a slight delay for a polished effect.</p>",1),F=r('<div class="rich-content svelte-x5n398"><h3 class="svelte-x5n398">Rich Content</h3> <p>This modal contains more complex content to demonstrate how the animations work with different content types.</p> <div class="feature-list svelte-x5n398"><h4 class="svelte-x5n398">Animation Features:</h4> <ul class="svelte-x5n398"><li class="svelte-x5n398">✨ Backdrop fades in smoothly (300ms)</li> <li class="svelte-x5n398">🎭 Modal scales from 70% to 100% with elastic easing</li> <li class="svelte-x5n398">⏱️ 150ms delay on modal entrance for layered effect</li> <li class="svelte-x5n398">🎯 Transform origin set to center for balanced scaling</li> <li class="svelte-x5n398">🔧 Hardware acceleration with will-change properties</li></ul></div> <div class="sample-form svelte-x5n398"><h4 class="svelte-x5n398">Sample Form:</h4> <input type="text" placeholder="Enter some text" class="svelte-x5n398"> <textarea placeholder="Enter a longer message..." class="svelte-x5n398"></textarea></div></div>'),R=r("<p>Are you sure you want to perform this action?</p> <p>This action cannot be undone.</p>",1),j=r('<div slot="footer" class="confirm-footer svelte-x5n398"><button class="cancel-btn svelte-x5n398">Cancel</button> <button class="confirm-btn svelte-x5n398">Confirm</button></div>'),H=r('<div class="container svelte-x5n398"><h1 class="svelte-x5n398">Modal Animation Test</h1> <p>Test the smooth modal animations with different modal types:</p> <div class="button-grid svelte-x5n398"><button class="test-button svelte-x5n398">Show Basic Modal</button> <button class="test-button svelte-x5n398">Show Content Modal</button> <button class="test-button svelte-x5n398">Show Confirm Modal</button></div></div> <!> <!> <!>',1);function K(k){let i=m(!1),c=m(!1),a=m(!1);var x=H();S(t=>{P.title="Modal Animation Test"});var d=B(x),b=o(h(d),4),w=h(b),$=o(w,2),C=o($,2);f(b),f(d);var g=o(d,2);p(g,{title:"Basic Modal",get show(){return u(i)},set show(t){s(i,t)},children:(t,v)=>{var e=E();y(2),n(t,e)},$$slots:{default:!0},$$legacy:!0});var _=o(g,2);p(_,{title:"Content-Rich Modal",get show(){return u(c)},set show(t){s(c,t)},children:(t,v)=>{var e=F();n(t,e)},$$slots:{default:!0},$$legacy:!0});var T=o(_,2);p(T,{title:"Confirm Action",get show(){return u(a)},set show(t){s(a,t)},children:(t,v)=>{var e=R();y(2),n(t,e)},$$slots:{default:!0,footer:(t,v)=>{var e=j(),M=h(e),A=o(M,2);f(e),l("click",M,()=>s(a,!1)),l("click",A,()=>s(a,!1)),n(t,e)}},$$legacy:!0}),l("click",w,()=>s(i,!0)),l("click",$,()=>s(c,!0)),l("click",C,()=>s(a,!0)),n(k,x)}export{K as component};
