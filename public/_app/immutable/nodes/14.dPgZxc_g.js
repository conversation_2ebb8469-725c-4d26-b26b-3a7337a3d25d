import{r as La}from"../chunks/DGCjcLwA.js";import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{x as Ot,z as Fa,y as ka,w as Ma,ah as za,e as Zt,i as g,r as p,s as c,t as ee,n as $e,g as Yt,u as hr,v as sa,j as O,m as ce,k as n,l as Z,q as wt,o as Ne,f as mr,ae as Ua}from"../chunks/p3DoyA09.js";import{s as G,e as Re,r as Ba,h as Ha}from"../chunks/BUelSUke.js";import{i as De}from"../chunks/DwdToawP.js";import{e as Ze,i as Dt}from"../chunks/DEqeA9IH.js";import{t as B,a as A,b as Ie}from"../chunks/B67foYpL.js";import{d as Ga,r as fe,s as Wa}from"../chunks/DdRd56Yq.js";import{b as Ja}from"../chunks/nu3w91fk.js";import{b as he,c as Jr}from"../chunks/WI3NPOEW.js";import{b as yr}from"../chunks/DSjDIsro.js";import{i as Vt}from"../chunks/D3pqaimu.js";import{p as la,s as da,b as Za,a as Fe,m as Ya}from"../chunks/qYb16FSw.js";import{c as Sr,o as ca}from"../chunks/C_WNR8j8.js";import{c as Va,M as ua}from"../chunks/BWn8tY11.js";import{B as Me}from"../chunks/6Zk3JFqZ.js";import{P as qa}from"../chunks/CC9utfo3.js";import{L as Ka}from"../chunks/C8F602cz.js";import{a as Ve}from"../chunks/Ce-0qAhV.js";import{p as Xa}from"../chunks/Bfc47y5P.js";import{C as Qa}from"../chunks/CxRlB3U5.js";import{api as en}from"../chunks/C5jwvbV4.js";import{j as Ce,b as Zr,s as tn,c as rn,d as an,e as nn,f as on,g as sn,h as ln}from"../chunks/CE5QyBL8.js";import{a as dn,c as cn}from"../chunks/Atsggda0.js";import{s as un,b as vn}from"../chunks/BGnz0MpO.js";import{j as fn,s as pn}from"../chunks/D9cKHHet.js";import{s as mn}from"../chunks/D7jLSc-x.js";function va(e,r,t){Ot(()=>{var a=Fa(()=>r(e,t==null?void 0:t())||{});if(t&&(a!=null&&a.update)){var o=!1,i={};ka(()=>{var l=t();Ma(l),o&&za(i,l)&&(i=l,a.update(l))}),o=!0}if(a!=null&&a.destroy)return()=>a.destroy()})}const gn=()=>{if(!localStorage.getItem("user"))throw La(302,"/login");return{}},Ro=Object.freeze(Object.defineProperty({__proto__:null,load:gn},Symbol.toStringTag,{value:"Module"}));function Yr(e,r){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);r&&(a=a.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),t.push.apply(t,a)}return t}function zt(e){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?Yr(Object(t),!0).forEach(function(a){ut(e,a,t[a])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Yr(Object(t)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))})}return e}function Tt(e){"@babel/helpers - typeof";return Tt=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(r){return typeof r}:function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},Tt(e)}function ut(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}function hn(e,r){if(e==null)return{};var t={},a=Object.keys(e),o,i;for(i=0;i<a.length;i++)o=a[i],!(r.indexOf(o)>=0)&&(t[o]=e[o]);return t}function yn(e,r){if(e==null)return{};var t=hn(e,r),a,o;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(o=0;o<i.length;o++)a=i[o],!(r.indexOf(a)>=0)&&Object.prototype.propertyIsEnumerable.call(e,a)&&(t[a]=e[a])}return t}function bn(e,r){return Dn(e)||wn(e,r)||xr(e,r)||In()}function Oe(e){return _n(e)||En(e)||xr(e)||Tn()}function _n(e){if(Array.isArray(e))return br(e)}function Dn(e){if(Array.isArray(e))return e}function En(e){if(typeof Symbol<"u"&&e[Symbol.iterator]!=null||e["@@iterator"]!=null)return Array.from(e)}function wn(e,r){var t=e==null?null:typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(t!=null){var a=[],o=!0,i=!1,l,v;try{for(t=t.call(e);!(o=(l=t.next()).done)&&(a.push(l.value),!(r&&a.length===r));o=!0);}catch(f){i=!0,v=f}finally{try{!o&&t.return!=null&&t.return()}finally{if(i)throw v}}return a}}function xr(e,r){if(e){if(typeof e=="string")return br(e,r);var t=Object.prototype.toString.call(e).slice(8,-1);if(t==="Object"&&e.constructor&&(t=e.constructor.name),t==="Map"||t==="Set")return Array.from(e);if(t==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t))return br(e,r)}}function br(e,r){(r==null||r>e.length)&&(r=e.length);for(var t=0,a=new Array(r);t<r;t++)a[t]=e[t];return a}function Tn(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function In(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ct(e,r){var t=typeof Symbol<"u"&&e[Symbol.iterator]||e["@@iterator"];if(!t){if(Array.isArray(e)||(t=xr(e))||r){t&&(e=t);var a=0,o=function(){};return{s:o,n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(f){throw f},f:o}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var i=!0,l=!1,v;return{s:function(){t=t.call(e)},n:function(){var f=t.next();return i=f.done,f},e:function(f){l=!0,v=f},f:function(){try{!i&&t.return!=null&&t.return()}finally{if(l)throw v}}}}var On="finalize",Sn="consider";function vt(e,r,t){e.dispatchEvent(new CustomEvent(On,{detail:{items:r,info:t}}))}function Xe(e,r,t){e.dispatchEvent(new CustomEvent(Sn,{detail:{items:r,info:t}}))}var qt="draggedEntered",$t="draggedLeft",Kt="draggedOverIndex",Ar="draggedLeftDocument",Ut={LEFT_FOR_ANOTHER:"leftForAnother",OUTSIDE_OF_ANY:"outsideOfAny"};function xn(e,r,t){e.dispatchEvent(new CustomEvent(qt,{detail:{indexObj:r,draggedEl:t}}))}function An(e,r,t){e.dispatchEvent(new CustomEvent($t,{detail:{draggedEl:r,type:Ut.LEFT_FOR_ANOTHER,theOtherDz:t}}))}function Cn(e,r){e.dispatchEvent(new CustomEvent($t,{detail:{draggedEl:r,type:Ut.OUTSIDE_OF_ANY}}))}function $n(e,r,t){e.dispatchEvent(new CustomEvent(Kt,{detail:{indexObj:r,draggedEl:t}}))}function Pn(e){window.dispatchEvent(new CustomEvent(Ar,{detail:{draggedEl:e}}))}var pe={DRAG_STARTED:"dragStarted",DRAGGED_ENTERED:qt,DRAGGED_ENTERED_ANOTHER:"dragEnteredAnother",DRAGGED_OVER_INDEX:Kt,DRAGGED_LEFT:$t,DRAGGED_LEFT_ALL:"draggedLeftAll",DROPPED_INTO_ZONE:"droppedIntoZone",DROPPED_INTO_ANOTHER:"droppedIntoAnother",DROPPED_OUTSIDE_OF_ANY:"droppedOutsideOfAny",DRAG_STOPPED:"dragStopped"},be={POINTER:"pointer",KEYBOARD:"keyboard"},Xt="isDndShadowItem",Qt="data-is-dnd-shadow-item-internal",Nn="data-is-dnd-shadow-item-hint",Rn="id:dnd-shadow-placeholder-0000",jn="dnd-action-dragged-el",ae="id",_r=0;function fa(){_r++}function pa(){if(_r===0)throw new Error("Bug! trying to decrement when there are no dropzones");_r--}var Cr=typeof window>"u";function Dr(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0,t,a=r?Mn(e):e.getBoundingClientRect(),o=getComputedStyle(e),i=o.transform;if(i){var l,v,f,E;if(i.startsWith("matrix3d("))t=i.slice(9,-1).split(/, /),l=+t[0],v=+t[5],f=+t[12],E=+t[13];else if(i.startsWith("matrix("))t=i.slice(7,-1).split(/, /),l=+t[0],v=+t[3],f=+t[4],E=+t[5];else return a;var d=o.transformOrigin,T=a.x-f-(1-l)*parseFloat(d),u=a.y-E-(1-v)*parseFloat(d.slice(d.indexOf(" ")+1)),y=l?a.width/l:e.offsetWidth,x=v?a.height/v:e.offsetHeight;return{x:T,y:u,width:y,height:x,top:u,right:T+y,bottom:u+x,left:T}}else return a}function ma(e){var r=Dr(e);return{top:r.top+window.scrollY,bottom:r.bottom+window.scrollY,left:r.left+window.scrollX,right:r.right+window.scrollX}}function ga(e){var r=e.getBoundingClientRect();return{top:r.top+window.scrollY,bottom:r.bottom+window.scrollY,left:r.left+window.scrollX,right:r.right+window.scrollX}}function ha(e){return{x:(e.left+e.right)/2,y:(e.top+e.bottom)/2}}function Ln(e,r){return Math.sqrt(Math.pow(e.x-r.x,2)+Math.pow(e.y-r.y,2))}function er(e,r){return e.y<=r.bottom&&e.y>=r.top&&e.x>=r.left&&e.x<=r.right}function St(e){return ha(ga(e))}function Vr(e,r){var t=St(e),a=ma(r);return er(t,a)}function Fn(e,r){var t=St(e),a=St(r);return Ln(t,a)}function kn(e){var r=ga(e);return r.right<0||r.left>document.documentElement.scrollWidth||r.bottom<0||r.top>document.documentElement.scrollHeight}function Mn(e){for(var r=e.getBoundingClientRect(),t={top:r.top,bottom:r.bottom,left:r.left,right:r.right},a=e.parentElement;a&&a!==document.body;){var o=a.getBoundingClientRect(),i=window.getComputedStyle(a).overflowY,l=window.getComputedStyle(a).overflowX,v=i==="scroll"||i==="auto",f=l==="scroll"||l==="auto";v&&(t.top=Math.max(t.top,o.top),t.bottom=Math.min(t.bottom,o.bottom)),f&&(t.left=Math.max(t.left,o.left),t.right=Math.min(t.right,o.right)),a=a.parentElement}return t.top=Math.max(t.top,0),t.bottom=Math.min(t.bottom,window.innerHeight),t.left=Math.max(t.left,0),t.right=Math.min(t.right,window.innerWidth),{top:t.top,bottom:t.bottom,left:t.left,right:t.right,width:Math.max(0,t.right-t.left),height:Math.max(0,t.bottom-t.top)}}var lt;function $r(){lt=new Map}$r();function zn(e){var r=Array.from(e.children).findIndex(function(t){return t.getAttribute(Qt)});if(r>=0)return lt.has(e)||lt.set(e,new Map),lt.get(e).set(r,ma(e.children[r])),r}function Un(e,r){if(!Vr(e,r))return null;var t=r.children;if(t.length===0)return{index:0,isProximityBased:!0};for(var a=zn(r),o=0;o<t.length;o++)if(Vr(e,t[o])){var i=lt.has(r)&&lt.get(r).get(o);return i&&!er(St(e),i)?{index:a,isProximityBased:!1}:{index:o,isProximityBased:!1}}for(var l=Number.MAX_VALUE,v=void 0,f=0;f<t.length;f++){var E=Fn(e,t[f]);E<l&&(l=E,v=f)}return{index:v,isProximityBased:!0}}function Lt(e){return JSON.stringify(e,null,2)}function Bt(e){if(!e)throw new Error("cannot get depth of a falsy node");return ya(e,0)}function ya(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;return e.parentElement?ya(e.parentElement,r+1):r-1}function Bn(e,r){if(Object.keys(e).length!==Object.keys(r).length)return!1;for(var t in e)if(!{}.hasOwnProperty.call(r,t)||r[t]!==e[t])return!1;return!0}function Hn(e,r){if(e.length!==r.length)return!1;for(var t=0;t<e.length;t++)if(e[t]!==r[t])return!1;return!0}var Gn=200,qr=10,Er;function Wn(e,r){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Gn,a=arguments.length>3?arguments[3]:void 0,o,i,l=!1,v,f=Array.from(r).sort(function(d,T){return Bt(T)-Bt(d)});function E(){var d=St(e),T=a.multiScrollIfNeeded();if(!T&&v&&Math.abs(v.x-d.x)<qr&&Math.abs(v.y-d.y)<qr){Er=window.setTimeout(E,t);return}if(kn(e)){Pn(e);return}v=d;var u=!1,y=Ct(f),x;try{for(y.s();!(x=y.n()).done;){var s=x.value;T&&$r();var C=Un(e,s);if(C!==null){var R=C.index;u=!0,s!==o?(o&&An(o,e,s),xn(s,C,e),o=s):R!==i&&($n(s,C,e),i=R);break}}}catch(w){y.e(w)}finally{y.f()}!u&&l&&o?(Cn(o,e),o=void 0,i=void 0,l=!1):l=!0,Er=window.setTimeout(E,t)}E()}function Jn(){clearTimeout(Er),$r()}var Et=30;function Zn(){var e;function r(){e={directionObj:void 0,stepPx:0}}r();function t(i){var l=e,v=l.directionObj,f=l.stepPx;v&&(i.scrollBy(v.x*f,v.y*f),window.requestAnimationFrame(function(){return t(i)}))}function a(i){return Et-i}function o(i,l){if(!l)return!1;var v=Yn(i,l),f=!!e.directionObj;if(v===null)return f&&r(),!1;var E=!1,d=!1;return l.scrollHeight>l.clientHeight&&(v.bottom<Et?(E=!0,e.directionObj={x:0,y:1},e.stepPx=a(v.bottom)):v.top<Et&&(E=!0,e.directionObj={x:0,y:-1},e.stepPx=a(v.top)),!f&&E)||l.scrollWidth>l.clientWidth&&(v.right<Et?(d=!0,e.directionObj={x:1,y:0},e.stepPx=a(v.right)):v.left<Et&&(d=!0,e.directionObj={x:-1,y:0},e.stepPx=a(v.left)),!f&&d)?(t(l),!0):(r(),!1)}return{scrollIfNeeded:o,resetScrolling:r}}function Yn(e,r){var t=r===document.scrollingElement?{top:0,bottom:window.innerHeight,left:0,right:window.innerWidth}:r.getBoundingClientRect();return er(e,t)?{top:e.y-t.top,bottom:t.bottom-e.y,left:e.x-t.left,right:t.right-e.x}:null}function Vn(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:[],r=arguments.length>1?arguments[1]:void 0,t=Kn(e),a=Array.from(t).sort(function(f,E){return Bt(E)-Bt(f)}),o=Zn(),i=o.scrollIfNeeded,l=o.resetScrolling;function v(){var f=r();if(!f||!a)return!1;for(var E=a.filter(function(u){return er(f,u.getBoundingClientRect())||u===document.scrollingElement}),d=0;d<E.length;d++){var T=i(f,E[d]);if(T)return!0}return!1}return{multiScrollIfNeeded:t.size>0?v:function(){return!1},destroy:function(){return l()}}}function qn(e){if(!e)return[];for(var r=[],t=e;t;){var a=window.getComputedStyle(t),o=a.overflow;o.split(" ").some(function(i){return i.includes("auto")||i.includes("scroll")})&&r.push(t),t=t.parentElement}return r}function Kn(e){var r=new Set,t=Ct(e),a;try{for(t.s();!(a=t.n()).done;){var o=a.value;qn(o).forEach(function(i){return r.add(i)})}}catch(i){t.e(i)}finally{t.f()}return(document.scrollingElement.scrollHeight>document.scrollingElement.clientHeight||document.scrollingElement.scrollWidth>document.scrollingElement.clientHeight)&&r.add(document.scrollingElement),r}function Xn(e){var r=e.cloneNode(!0),t=[],a=e.tagName==="SELECT",o=a?[e]:Oe(e.querySelectorAll("select")),i=Ct(o),l;try{for(i.s();!(l=i.n()).done;){var v=l.value;t.push(v.value)}}catch(F){i.e(F)}finally{i.f()}if(o.length>0)for(var f=a?[r]:Oe(r.querySelectorAll("select")),E=0;E<f.length;E++){var d=f[E],T=t[E],u=d.querySelector('option[value="'.concat(T,'"'));u&&u.setAttribute("selected",!0)}var y=e.tagName==="CANVAS",x=y?[e]:Oe(e.querySelectorAll("canvas"));if(x.length>0)for(var s=y?[r]:Oe(r.querySelectorAll("canvas")),C=0;C<s.length;C++){var R=x[C],w=s[C];w.width=R.width,w.height=R.height,R.width>0&&R.height>0&&w.getContext("2d").drawImage(R,0,0)}return r}var xt=Object.freeze({USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT:"USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT"}),Qn=ut({},xt.USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT,!1);function ba(e){if(!xt[e])throw new Error("Can't get non existing feature flag ".concat(e,"! Supported flags: ").concat(Object.keys(xt)));return Qn[e]}var ei=.2;function tt(e){return"".concat(e," ").concat(ei,"s ease")}function ti(e,r){var t=e.getBoundingClientRect(),a=Xn(e);_a(e,a),a.id=jn,a.style.position="fixed";var o=t.top,i=t.left;if(a.style.top="".concat(o,"px"),a.style.left="".concat(i,"px"),r){var l=ha(t);o-=l.y-r.y,i-=l.x-r.x,window.setTimeout(function(){a.style.top="".concat(o,"px"),a.style.left="".concat(i,"px")},0)}return a.style.margin="0",a.style.boxSizing="border-box",a.style.height="".concat(t.height,"px"),a.style.width="".concat(t.width,"px"),a.style.transition="".concat(tt("top"),", ").concat(tt("left"),", ").concat(tt("background-color"),", ").concat(tt("opacity"),", ").concat(tt("color")," "),window.setTimeout(function(){return a.style.transition+=", ".concat(tt("width"),", ").concat(tt("height"))},0),a.style.zIndex="9999",a.style.cursor="grabbing",a}function ri(e){e.style.cursor="grab"}function ai(e,r,t,a){_a(r,e);var o=r.getBoundingClientRect(),i=e.getBoundingClientRect(),l=o.width-i.width,v=o.height-i.height;if(l||v){var f={left:(t-i.left)/i.width,top:(a-i.top)/i.height};ba(xt.USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT)||(e.style.height="".concat(o.height,"px"),e.style.width="".concat(o.width,"px")),e.style.left="".concat(parseFloat(e.style.left)-f.left*l,"px"),e.style.top="".concat(parseFloat(e.style.top)-f.top*v,"px")}}function _a(e,r){var t=window.getComputedStyle(e);Array.from(t).filter(function(a){return a.startsWith("background")||a.startsWith("padding")||a.startsWith("font")||a.startsWith("text")||a.startsWith("align")||a.startsWith("justify")||a.startsWith("display")||a.startsWith("flex")||a.startsWith("border")||a==="opacity"||a==="color"||a==="list-style-type"||ba(xt.USE_COMPUTED_STYLE_INSTEAD_OF_BOUNDING_RECT)&&(a==="width"||a==="height")}).forEach(function(a){return r.style.setProperty(a,t.getPropertyValue(a),t.getPropertyPriority(a))})}function ni(e,r){e.draggable=!1,e.ondragstart=function(){return!1},r?(e.style.userSelect="",e.style.WebkitUserSelect="",e.style.cursor=""):(e.style.userSelect="none",e.style.WebkitUserSelect="none",e.style.cursor="grab")}function Da(e){e.style.display="none",e.style.position="fixed",e.style.zIndex="-5"}function ii(e){e.style.visibility="hidden",e.setAttribute(Qt,"true")}function oi(e){e.style.visibility="",e.removeAttribute(Qt)}function kt(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:function(){},t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:function(){return[]};e.forEach(function(a){var o=r(a);Object.keys(o).forEach(function(i){a.style[i]=o[i]}),t(a).forEach(function(i){return a.classList.add(i)})})}function Ht(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:function(){},t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:function(){return[]};e.forEach(function(a){var o=r(a);Object.keys(o).forEach(function(i){a.style[i]=""}),t(a).forEach(function(i){return a.classList.contains(i)&&a.classList.remove(i)})})}function si(e){var r=e.style.minHeight;e.style.minHeight=window.getComputedStyle(e).getPropertyValue("height");var t=e.style.minWidth;return e.style.minWidth=window.getComputedStyle(e).getPropertyValue("width"),function(){e.style.minHeight=r,e.style.minWidth=t}}var li="--any--",di=100,ci=20,Kr=3,Xr={outline:"rgba(255, 255, 102, 0.7) solid 2px"},Qr="data-is-dnd-original-dragged-item",Ae,ue,ye,tr,H,rr,Ke,oe,Ye,_e,Je=!1,Pr=!1,Nr,Pt=!1,Mt=[],It,je=new Map,q=new Map,gr=new WeakMap;function ui(e,r){je.has(r)||je.set(r,new Set),je.get(r).has(e)||(je.get(r).add(e),fa())}function ea(e,r){je.get(r).delete(e),pa(),je.get(r).size===0&&je.delete(r)}function vi(){var e=je.get(tr),r=Ct(e),t;try{for(r.s();!(t=r.n()).done;){var a=t.value;a.addEventListener(qt,Ea),a.addEventListener($t,wa),a.addEventListener(Kt,Ta)}}catch(l){r.e(l)}finally{r.f()}window.addEventListener(Ar,ft);var o=Math.max.apply(Math,Oe(Array.from(e.keys()).map(function(l){return q.get(l).dropAnimationDurationMs}))),i=o===0?ci:Math.max(o,di);It=Vn(e,function(){return _e}),Wn(ue,e,i*1.07,It)}function fi(){var e=je.get(tr),r=Ct(e),t;try{for(r.s();!(t=r.n()).done;){var a=t.value;a.removeEventListener(qt,Ea),a.removeEventListener($t,wa),a.removeEventListener(Kt,Ta)}}catch(o){r.e(o)}finally{r.f()}window.removeEventListener(Ar,ft),It&&(It.destroy(),It=void 0),Jn()}function ar(e){return e.findIndex(function(r){return!!r[Xt]})}function pi(e){var r;return zt(zt({},e),{},(r={},ut(r,Xt,!0),ut(r,ae,Rn),r))}function Ea(e){var r=q.get(e.currentTarget),t=r.items,a=r.dropFromOthersDisabled;if(!(a&&e.currentTarget!==H)){if(Pt=!1,t=t.filter(function(d){return d[ae]!==Ke[ae]}),H!==e.currentTarget){var o=q.get(H).items,i=o.filter(function(d){return!d[Xt]});Xe(H,i,{trigger:pe.DRAGGED_ENTERED_ANOTHER,id:ye[ae],source:be.POINTER})}var l=e.detail.indexObj,v=l.index,f=l.isProximityBased,E=f&&v===e.currentTarget.children.length-1?v+1:v;oe=e.currentTarget,t.splice(E,0,Ke),Xe(e.currentTarget,t,{trigger:pe.DRAGGED_ENTERED,id:ye[ae],source:be.POINTER})}}function wa(e){if(Je){var r=q.get(e.currentTarget),t=r.items,a=r.dropFromOthersDisabled;if(!(a&&e.currentTarget!==H&&e.currentTarget!==oe)){var o=Oe(t),i=ar(o);i!==-1&&o.splice(i,1);var l=oe;oe=void 0;var v=e.detail,f=v.type,E=v.theOtherDz;if(f===Ut.OUTSIDE_OF_ANY||f===Ut.LEFT_FOR_ANOTHER&&E!==H&&q.get(E).dropFromOthersDisabled){Pt=!0,oe=H;var d=l===H?o:Oe(q.get(H).items);d.splice(rr,0,Ke),Xe(H,d,{trigger:pe.DRAGGED_LEFT_ALL,id:ye[ae],source:be.POINTER})}Xe(e.currentTarget,o,{trigger:pe.DRAGGED_LEFT,id:ye[ae],source:be.POINTER})}}}function Ta(e){var r=q.get(e.currentTarget),t=r.items,a=r.dropFromOthersDisabled;if(!(a&&e.currentTarget!==H)){var o=Oe(t);Pt=!1;var i=e.detail.indexObj.index,l=ar(o);l!==-1&&o.splice(l,1),o.splice(i,0,Ke),Xe(e.currentTarget,o,{trigger:pe.DRAGGED_OVER_INDEX,id:ye[ae],source:be.POINTER})}}function Gt(e){e.preventDefault();var r=e.touches?e.touches[0]:e;_e={x:r.clientX,y:r.clientY},ue.style.transform="translate3d(".concat(_e.x-Ye.x,"px, ").concat(_e.y-Ye.y,"px, 0)")}function ft(){Pr=!0,window.removeEventListener("mousemove",Gt),window.removeEventListener("touchmove",Gt),window.removeEventListener("mouseup",ft),window.removeEventListener("touchend",ft),fi(),ri(ue),oe||(oe=H);var e=q.get(oe),r=e.items,t=e.type;Ht(je.get(t),function(i){return q.get(i).dropTargetStyle},function(i){return q.get(i).dropTargetClasses});var a=ar(r);a===-1&&oe===H&&(a=rr),r=r.map(function(i){return i[Xt]?ye:i});function o(){Nr(),vt(oe,r,{trigger:Pt?pe.DROPPED_OUTSIDE_OF_ANY:pe.DROPPED_INTO_ZONE,id:ye[ae],source:be.POINTER}),oe!==H&&vt(H,q.get(H).items,{trigger:pe.DROPPED_INTO_ANOTHER,id:ye[ae],source:be.POINTER});var i=Array.from(oe.children).find(function(l){return l.getAttribute(Qt)});i&&oi(i),hi()}q.get(oe).dropAnimationDisabled?o():mi(a,o)}function mi(e,r){var t=e>-1?Dr(oe.children[e],!1):Dr(oe,!1),a={x:t.left-parseFloat(ue.style.left),y:t.top-parseFloat(ue.style.top)},o=q.get(oe),i=o.dropAnimationDurationMs,l="transform ".concat(i,"ms ease");ue.style.transition=ue.style.transition?ue.style.transition+","+l:l,ue.style.transform="translate3d(".concat(a.x,"px, ").concat(a.y,"px, 0)"),window.setTimeout(r,i)}function gi(e,r){Mt.push({dz:e,destroy:r}),window.requestAnimationFrame(function(){Da(e),document.body.appendChild(e)})}function hi(){ue.remove(),Ae.remove(),Mt.length&&(Mt.forEach(function(e){var r=e.dz,t=e.destroy;t(),r.remove()}),Mt=[]),ue=void 0,Ae=void 0,ye=void 0,tr=void 0,H=void 0,rr=void 0,Ke=void 0,oe=void 0,Ye=void 0,_e=void 0,Je=!1,Pr=!1,Nr=void 0,Pt=!1}function yi(e,r){var t=!1,a={items:void 0,type:void 0,flipDurationMs:0,dragDisabled:!1,morphDisabled:!1,dropFromOthersDisabled:!1,dropTargetStyle:Xr,dropTargetClasses:[],transformDraggedElement:function(){},centreDraggedOnCursor:!1,dropAnimationDisabled:!1},o=new Map;function i(){window.addEventListener("mousemove",f,{passive:!1}),window.addEventListener("touchmove",f,{passive:!1,capture:!1}),window.addEventListener("mouseup",v,{passive:!1}),window.addEventListener("touchend",v,{passive:!1})}function l(){window.removeEventListener("mousemove",f),window.removeEventListener("touchmove",f),window.removeEventListener("mouseup",v),window.removeEventListener("touchend",v)}function v(u){if(l(),Ae=void 0,Ye=void 0,_e=void 0,u.type==="touchend"){var y=new Event("click",{bubbles:!0,cancelable:!0});u.target.dispatchEvent(y)}}function f(u){u.preventDefault();var y=u.touches?u.touches[0]:u;_e={x:y.clientX,y:y.clientY},(Math.abs(_e.x-Ye.x)>=Kr||Math.abs(_e.y-Ye.y)>=Kr)&&(l(),d())}function E(u){if(!(u.target!==u.currentTarget&&(u.target.value!==void 0||u.target.isContentEditable))&&!u.button&&!Je){u.preventDefault(),u.stopPropagation();var y=u.touches?u.touches[0]:u;Ye={x:y.clientX,y:y.clientY},_e=zt({},Ye),Ae=u.currentTarget,i()}}function d(){Je=!0;var u=o.get(Ae);rr=u,H=Ae.parentElement;var y=H.closest("dialog")||H.closest("[popover]")||H.getRootNode(),x=y.body||y,s=a.items,C=a.type,R=a.centreDraggedOnCursor,w=Oe(s);ye=w[u],tr=C,Ke=pi(ye),ue=ti(Ae,R&&_e),x.appendChild(ue);function F(){Ae.parentElement?window.requestAnimationFrame(F):(Ae.setAttribute(Qr,!0),x.appendChild(Ae),vi(),Da(Ae),Ke[ae]=ye[ae],ue.focus())}window.requestAnimationFrame(F),kt(Array.from(je.get(a.type)).filter(function(k){return k===H||!q.get(k).dropFromOthersDisabled}),function(k){return q.get(k).dropTargetStyle},function(k){return q.get(k).dropTargetClasses}),w.splice(u,1,Ke),Nr=si(H),Xe(H,w,{trigger:pe.DRAG_STARTED,id:ye[ae],source:be.POINTER}),window.addEventListener("mousemove",Gt,{passive:!1}),window.addEventListener("touchmove",Gt,{passive:!1,capture:!1}),window.addEventListener("mouseup",ft,{passive:!1}),window.addEventListener("touchend",ft,{passive:!1})}function T(u){var y=u.items,x=y===void 0?void 0:y,s=u.flipDurationMs,C=s===void 0?0:s,R=u.type,w=R===void 0?li:R,F=u.dragDisabled,k=F===void 0?!1:F,Y=u.morphDisabled,te=Y===void 0?!1:Y,K=u.dropFromOthersDisabled,$=K===void 0?!1:K,M=u.dropTargetStyle,V=M===void 0?Xr:M,ne=u.dropTargetClasses,S=ne===void 0?[]:ne,W=u.transformDraggedElement,j=W===void 0?function(){}:W,se=u.centreDraggedOnCursor,b=se===void 0?!1:se,m=u.dropAnimationDisabled,N=m===void 0?!1:m;a.dropAnimationDurationMs=C,a.type&&w!==a.type&&ea(e,a.type),a.type=w,a.items=Oe(x),a.dragDisabled=k,a.morphDisabled=te,a.transformDraggedElement=j,a.centreDraggedOnCursor=b,a.dropAnimationDisabled=N,t&&Je&&!Pr&&(!Bn(V,a.dropTargetStyle)||!Hn(S,a.dropTargetClasses))&&(Ht([e],function(){return a.dropTargetStyle},function(){return S}),kt([e],function(){return V},function(){return S})),a.dropTargetStyle=V,a.dropTargetClasses=Oe(S);function L(D,U){return q.get(D)?q.get(D)[U]:a[U]}t&&Je&&a.dropFromOthersDisabled!==$&&($?Ht([e],function(D){return L(D,"dropTargetStyle")},function(D){return L(D,"dropTargetClasses")}):kt([e],function(D){return L(D,"dropTargetStyle")},function(D){return L(D,"dropTargetClasses")})),a.dropFromOthersDisabled=$,q.set(e,a),ui(e,w);for(var Q=Je?ar(a.items):-1,_=0;_<e.children.length;_++){var h=e.children[_];if(ni(h,k),_===Q){te||ai(ue,h,_e.x,_e.y),a.transformDraggedElement(ue,ye,_),ii(h);continue}h.removeEventListener("mousedown",gr.get(h)),h.removeEventListener("touchstart",gr.get(h)),k||(h.addEventListener("mousedown",E),h.addEventListener("touchstart",E),gr.set(h,E)),o.set(h,_),t||(t=!0)}}return T(r),{update:function(y){T(y)},destroy:function(){function y(){ea(e,q.get(e).type),q.delete(e)}Je&&!e.closest("[".concat(Qr,"]"))?gi(e,y):y()}}}var Ft,wr={DND_ZONE_ACTIVE:"dnd-zone-active",DND_ZONE_DRAG_DISABLED:"dnd-zone-drag-disabled"},Ia=(Ft={},ut(Ft,wr.DND_ZONE_ACTIVE,"Tab to one the items and press space-bar or enter to start dragging it"),ut(Ft,wr.DND_ZONE_DRAG_DISABLED,"This is a disabled drag and drop list"),Ft),bi="dnd-action-aria-alert",X;function Tr(){X||(X=document.createElement("div"),function(){X.id=bi,X.style.position="fixed",X.style.bottom="0",X.style.left="0",X.style.zIndex="-5",X.style.opacity="0",X.style.height="0",X.style.width="0",X.setAttribute("role","alert")}(),document.body.prepend(X),Object.entries(Ia).forEach(function(e){var r=bn(e,2),t=r[0],a=r[1];return document.body.prepend(Ei(t,a))}))}function _i(){return Cr?null:(document.readyState==="complete"?Tr():window.addEventListener("DOMContentLoaded",Tr),zt({},wr))}function Di(){Cr||!X||(Object.keys(Ia).forEach(function(e){var r;return(r=document.getElementById(e))===null||r===void 0?void 0:r.remove()}),X.remove(),X=void 0)}function Ei(e,r){var t=document.createElement("div");return t.id=e,t.innerHTML="<p>".concat(r,"</p>"),t.style.display="none",t.style.position="fixed",t.style.zIndex="-5",t}function dt(e){if(!Cr){X||Tr(),X.innerHTML="";var r=document.createTextNode(e);X.appendChild(r),X.style.display="none",X.style.display="inline"}}var wi="--any--",ta={outline:"rgba(255, 255, 102, 0.7) solid 2px"},Te=!1,Ir,ve,at="",rt,ke,qe="",Wt=new WeakSet,ra=new WeakMap,aa=new WeakMap,Or=new Map,ie=new Map,Pe=new Map,Jt;function Ti(e,r){Pe.size===0&&(Jt=_i(),window.addEventListener("keydown",Oa),window.addEventListener("click",Sa)),Pe.has(r)||Pe.set(r,new Set),Pe.get(r).has(e)||(Pe.get(r).add(e),fa())}function na(e,r){ve===e&&At(),Pe.get(r).delete(e),pa(),Pe.get(r).size===0&&Pe.delete(r),Pe.size===0&&(window.removeEventListener("keydown",Oa),window.removeEventListener("click",Sa),Jt=void 0,Di())}function Oa(e){if(Te)switch(e.key){case"Escape":{At();break}}}function Sa(){Te&&(Wt.has(document.activeElement)||At())}function Ii(e){if(Te){var r=e.currentTarget;if(r!==ve){at=r.getAttribute("aria-label")||"";var t=ie.get(ve),a=t.items,o=a.find(function(T){return T[ae]===ke}),i=a.indexOf(o),l=a.splice(i,1)[0],v=ie.get(r),f=v.items,E=v.autoAriaDisabled;r.getBoundingClientRect().top<ve.getBoundingClientRect().top||r.getBoundingClientRect().left<ve.getBoundingClientRect().left?(f.push(l),E||dt("Moved item ".concat(qe," to the end of the list ").concat(at))):(f.unshift(l),E||dt("Moved item ".concat(qe," to the beginning of the list ").concat(at)));var d=ve;vt(d,a,{trigger:pe.DROPPED_INTO_ANOTHER,id:ke,source:be.KEYBOARD}),vt(r,f,{trigger:pe.DROPPED_INTO_ZONE,id:ke,source:be.KEYBOARD}),ve=r}}}function xa(){Or.forEach(function(e,r){var t=e.update;return t(ie.get(r))})}function At(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;ie.get(ve).autoAriaDisabled||dt("Stopped dragging item ".concat(qe)),Wt.has(document.activeElement)&&document.activeElement.blur(),e&&Xe(ve,ie.get(ve).items,{trigger:pe.DRAG_STOPPED,id:ke,source:be.KEYBOARD}),Ht(Pe.get(Ir),function(r){return ie.get(r).dropTargetStyle},function(r){return ie.get(r).dropTargetClasses}),rt=null,ke=null,qe="",Ir=null,ve=null,at="",Te=!1,xa()}function Oi(e,r){var t={items:void 0,type:void 0,dragDisabled:!1,zoneTabIndex:0,zoneItemTabIndex:0,dropFromOthersDisabled:!1,dropTargetStyle:ta,dropTargetClasses:[],autoAriaDisabled:!1};function a(d,T,u){d.length<=1||d.splice(u,1,d.splice(T,1,d[u])[0])}function o(d){switch(d.key){case"Enter":case" ":{if((d.target.disabled!==void 0||d.target.href||d.target.isContentEditable)&&!Wt.has(d.target))return;d.preventDefault(),d.stopPropagation(),Te?At():i(d);break}case"ArrowDown":case"ArrowRight":{if(!Te)return;d.preventDefault(),d.stopPropagation();var T=ie.get(e),u=T.items,y=Array.from(e.children),x=y.indexOf(d.currentTarget);x<y.length-1&&(t.autoAriaDisabled||dt("Moved item ".concat(qe," to position ").concat(x+2," in the list ").concat(at)),a(u,x,x+1),vt(e,u,{trigger:pe.DROPPED_INTO_ZONE,id:ke,source:be.KEYBOARD}));break}case"ArrowUp":case"ArrowLeft":{if(!Te)return;d.preventDefault(),d.stopPropagation();var s=ie.get(e),C=s.items,R=Array.from(e.children),w=R.indexOf(d.currentTarget);w>0&&(t.autoAriaDisabled||dt("Moved item ".concat(qe," to position ").concat(w," in the list ").concat(at)),a(C,w,w-1),vt(e,C,{trigger:pe.DROPPED_INTO_ZONE,id:ke,source:be.KEYBOARD}));break}}}function i(d){v(d.currentTarget),ve=e,Ir=t.type,Te=!0;var T=Array.from(Pe.get(t.type)).filter(function(y){return y===ve||!ie.get(y).dropFromOthersDisabled});if(kt(T,function(y){return ie.get(y).dropTargetStyle},function(y){return ie.get(y).dropTargetClasses}),!t.autoAriaDisabled){var u="Started dragging item ".concat(qe,". Use the arrow keys to move it within its list ").concat(at);T.length>1&&(u+=", or tab to another list in order to move the item into it"),dt(u)}Xe(e,ie.get(e).items,{trigger:pe.DRAG_STARTED,id:ke,source:be.KEYBOARD}),xa()}function l(d){Te&&d.currentTarget!==rt&&(d.stopPropagation(),At(!1),i(d))}function v(d){var T=ie.get(e),u=T.items,y=Array.from(e.children),x=y.indexOf(d);rt=d,rt.tabIndex=t.zoneItemTabIndex,ke=u[x][ae],qe=y[x].getAttribute("aria-label")||""}function f(d){var T=d.items,u=T===void 0?[]:T,y=d.type,x=y===void 0?wi:y,s=d.dragDisabled,C=s===void 0?!1:s,R=d.zoneTabIndex,w=R===void 0?0:R,F=d.zoneItemTabIndex,k=F===void 0?0:F,Y=d.dropFromOthersDisabled,te=Y===void 0?!1:Y,K=d.dropTargetStyle,$=K===void 0?ta:K,M=d.dropTargetClasses,V=M===void 0?[]:M,ne=d.autoAriaDisabled,S=ne===void 0?!1:ne;t.items=Oe(u),t.dragDisabled=C,t.dropFromOthersDisabled=te,t.zoneTabIndex=w,t.zoneItemTabIndex=k,t.dropTargetStyle=$,t.dropTargetClasses=V,t.autoAriaDisabled=S,t.type&&x!==t.type&&na(e,t.type),t.type=x,Ti(e,x),S||(e.setAttribute("aria-disabled",C),e.setAttribute("role","list"),e.setAttribute("aria-describedby",C?Jt.DND_ZONE_DRAG_DISABLED:Jt.DND_ZONE_ACTIVE)),ie.set(e,t),Te?e.tabIndex=e===ve||rt.contains(e)||t.dropFromOthersDisabled||ve&&t.type!==ie.get(ve).type?-1:0:e.tabIndex=t.zoneTabIndex,e.addEventListener("focus",Ii);for(var W=function(b){var m=e.children[b];Wt.add(m),m.tabIndex=Te?-1:t.zoneItemTabIndex,S||m.setAttribute("role","listitem"),m.removeEventListener("keydown",ra.get(m)),m.removeEventListener("click",aa.get(m)),C||(m.addEventListener("keydown",o),ra.set(m,o),m.addEventListener("click",l),aa.set(m,l)),Te&&t.items[b][ae]===ke&&(rt=m,rt.tabIndex=t.zoneItemTabIndex,m.focus())},j=0;j<e.children.length;j++)W(j)}f(r);var E={update:function(T){f(T)},destroy:function(){na(e,t.type),ie.delete(e),Or.delete(e)}};return Or.set(e,E),E}var Si=["items","flipDurationMs","type","dragDisabled","morphDisabled","dropFromOthersDisabled","zoneTabIndex","zoneItemTabIndex","dropTargetStyle","dropTargetClasses","transformDraggedElement","autoAriaDisabled","centreDraggedOnCursor","dropAnimationDisabled"];function ct(e,r){if(xi(e))return{update:function(){},destroy:function(){}};ia(r);var t=yi(e,r),a=Oi(e,r);return{update:function(i){ia(i),t.update(i),a.update(i)},destroy:function(){t.destroy(),a.destroy()}}}function xi(e){return!!e.closest("[".concat(Nn,'="true"]'))}function ia(e){var r=e.items;e.flipDurationMs,e.type,e.dragDisabled,e.morphDisabled,e.dropFromOthersDisabled;var t=e.zoneTabIndex,a=e.zoneItemTabIndex;e.dropTargetStyle;var o=e.dropTargetClasses;e.transformDraggedElement,e.autoAriaDisabled,e.centreDraggedOnCursor,e.dropAnimationDisabled;var i=yn(e,Si);if(Object.keys(i).length>0&&console.warn("dndzone will ignore unknown options",i),!r)throw new Error("no 'items' key provided to dndzone");var l=r.find(function(v){return!{}.hasOwnProperty.call(v,ae)});if(l)throw new Error("missing '".concat(ae,"' property for item ").concat(Lt(l)));if(o&&!Array.isArray(o))throw new Error("dropTargetClasses should be an array but instead it is a ".concat(Tt(o),", ").concat(Lt(o)));if(t&&!oa(t))throw new Error("zoneTabIndex should be a number but instead it is a ".concat(Tt(t),", ").concat(Lt(t)));if(a&&!oa(a))throw new Error("zoneItemTabIndex should be a number but instead it is a ".concat(Tt(a),", ").concat(Lt(a)))}function oa(e){return!isNaN(e)&&function(r){return(r|0)===r}(parseFloat(e))}function Ai(e,{from:r,to:t},a={}){var{delay:o=0,duration:i=te=>Math.sqrt(te)*120,easing:l=Va}=a,v=getComputedStyle(e),f=v.transform==="none"?"":v.transform,[E,d]=v.transformOrigin.split(" ").map(parseFloat);E/=e.clientWidth,d/=e.clientHeight;var T=Ci(e),u=e.clientWidth/t.width/T,y=e.clientHeight/t.height/T,x=r.left+r.width*E,s=r.top+r.height*d,C=t.left+t.width*E,R=t.top+t.height*d,w=(x-C)*u,F=(s-R)*y,k=r.width/t.width,Y=r.height/t.height;return{delay:o,duration:typeof i=="function"?i(Math.sqrt(w*w+F*F)):i,easing:l,css:(te,K)=>{var $=K*w,M=K*F,V=te+K*k,ne=te+K*Y;return`transform: ${f} translate(${$}px, ${M}px) scale(${V}, ${ne});`}}}function Ci(e){if("currentCSSZoom"in e)return e.currentCSSZoom;for(var r=e,t=1;r!==null;)t*=+getComputedStyle(r).zoom,r=r.parentElement;return t}var $i=B('<div class="job-scheduled svelte-leoof"><span class="label svelte-leoof">Scheduled:</span> </div>'),Pi=B('<div class="job-duration svelte-leoof"><span class="label svelte-leoof">Est. Duration:</span> </div>'),Ni=B('<div class="job-card svelte-leoof" role="button" aria-label="Draggable job card" tabindex="0"><div class="job-header svelte-leoof"><h4 class="job-title svelte-leoof"> </h4> <div class="priority-badge svelte-leoof"> </div></div> <div class="job-customer svelte-leoof"><span class="label svelte-leoof">Customer:</span> </div> <!> <!> <div class="job-footer svelte-leoof"><div class="job-type svelte-leoof"> </div> <button class="edit-button svelte-leoof" aria-label="Edit job">Edit</button></div></div>');function Ri(e,r){Zt(r,!1);let t=la(r,"job",8);const a=Sr();function o($){$.stopPropagation(),a("edit",t())}function i(){a("edit",t())}function l($){if(!$)return"";const M=new Date($);return M.toLocaleDateString()+" "+M.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}function v($){switch($){case"Urgent":return"#EF4444";case"High":return"#F59E0B";case"Medium":return"#3B82F6";case"Low":return"#6B7280";default:return"#6B7280"}}Vt();var f=Ni(),E=g(f),d=g(E),T=g(d,!0);p(d);var u=c(d,2),y=g(u,!0);p(u),p(E);var x=c(E,2),s=c(g(x));p(x);var C=c(x,2);{var R=$=>{var M=$i(),V=c(g(M));p(M),ee(ne=>G(V,` ${ne??""}`),[()=>l(t().scheduledDateTime)],$e),A($,M)};De(C,$=>{t().scheduledDateTime&&$(R)})}var w=c(C,2);{var F=$=>{var M=Pi(),V=c(g(M));p(M),ee(ne=>G(V,` ${ne??""}h`),[()=>Math.round(t().estimatedDuration/60*10)/10],$e),A($,M)};De(w,$=>{t().estimatedDuration&&$(F)})}var k=c(w,2),Y=g(k),te=g(Y,!0);p(Y);var K=c(Y,2);p(k),p(f),ee($=>{G(T,t().title),Ga(u,`background-color: ${$??""}`),G(y,t().priority),G(s,` ${t().customerName||"Unknown"}`),G(te,t().jobType||"General")},[()=>v(t().priority)],$e),Re("click",K,o),Re("click",f,i),Re("keydown",f,$=>$.key==="Enter"&&i()),A(e,f),Yt()}var ji=B("<option> </option>"),Li=B('<span class="wage-info svelte-8jlwav"> </span>'),Fi=B('<label><input type="checkbox"> <span class="checkbox-custom"></span> <!></label>'),ki=B('<span class="cost-info svelte-8jlwav"><!></span>'),Mi=B('<label><input type="checkbox"> <span class="checkbox-custom"></span> <!></label>'),zi=B('<div class="custom-field svelte-8jlwav"><input type="text" placeholder="Field name" class="field-key svelte-8jlwav"> <input type="text" placeholder="Field value" class="field-value svelte-8jlwav"> <!></div>'),Ui=B('<div class="breakdown-item svelte-8jlwav"><span class="description"> </span> <span class="amount"> </span></div>'),Bi=B('<details class="cost-details svelte-8jlwav"><summary>Cost Breakdown</summary> <div class="breakdown-items svelte-8jlwav"></div></details>'),Hi=B('<div class="cost-estimate-section svelte-8jlwav"><h3 class="svelte-8jlwav">Cost Estimate</h3> <div class="cost-breakdown svelte-8jlwav"><div class="cost-row svelte-8jlwav"><span>Labor Cost:</span> <span> </span></div> <div class="cost-row svelte-8jlwav"><span>Material Cost:</span> <span> </span></div> <div class="cost-row svelte-8jlwav"><span>Resource Cost:</span> <span> </span></div> <div class="cost-row total svelte-8jlwav"><span>Total Estimated Cost:</span> <span> </span></div></div> <!></div>'),Gi=B('<form class="job-form svelte-8jlwav"><div class="form-grid svelte-8jlwav"><div class="form-group"><label for="title">Job Title *</label> <input id="title" type="text" placeholder="Enter job title" required></div> <div class="form-group"><!></div> <div class="form-group"><label for="jobType">Job Type</label> <select id="jobType"><option>Select job type</option><!></select></div> <div class="form-group"><label for="priority">Priority</label> <select id="priority"><option>Low</option><option>Medium</option><option>High</option><option>Urgent</option></select></div> <div class="form-group"><label for="scheduledDate">Scheduled Date</label> <input id="scheduledDate" type="date"></div> <div class="form-group"><label for="scheduledTime">Scheduled Time</label> <input id="scheduledTime" type="time"></div> <div class="form-group"><label for="estimatedHours">Estimated Hours</label> <input id="estimatedHours" type="number" min="0" step="0.5" placeholder="0"></div> <div class="form-group staff-section svelte-8jlwav"><label>Assigned Staff</label> <div class="staff-checkboxes svelte-8jlwav"></div></div> <div class="form-group resources-section svelte-8jlwav"><label>Resources</label> <div class="resource-checkboxes svelte-8jlwav"></div></div></div> <div class="form-group full-width"><label for="description">Description</label> <textarea id="description" placeholder="Enter job description" rows="4"></textarea></div> <div class="address-section svelte-8jlwav"><h3 class="svelte-8jlwav">Job Address</h3> <div class="form-grid svelte-8jlwav"><div class="form-group"><label for="street">Street Address</label> <input id="street" type="text" placeholder="Enter street address"></div> <div class="form-group"><label for="city">City</label> <input id="city" type="text" placeholder="Enter city"></div> <div class="form-group"><label for="state">State</label> <input id="state" type="text" placeholder="Enter state"></div> <div class="form-group"><label for="zipCode">ZIP Code</label> <input id="zipCode" type="text" placeholder="Enter ZIP code"></div></div></div> <div class="custom-fields-section svelte-8jlwav"><div class="section-header svelte-8jlwav"><h3 class="svelte-8jlwav">Custom Fields</h3> <!></div> <!></div> <!> <div class="form-actions svelte-8jlwav"><!> <!></div></form>');function Wi(e,r){Zt(r,!1);const[t,a]=da(),o=()=>Fe(Zr,"$jobModalOpen",t),i=()=>Fe(tn,"$selectedJob",t),l=()=>Fe(rn,"$jobModalMode",t),v=()=>Fe(cn,"$customers",t),f=()=>Fe(an,"$jobTypes",t),E=()=>Fe(vn,"$activeStaff",t),d=[],T=[];Sr(),ca(async()=>{await Promise.all([dn.loadContacts(),un.loadActiveStaff(),x()])});let u=ce([]),y=ce(null);async function x(){try{O(u,await fn())}catch(S){console.error("Error loading resources:",S)}}let s=ce({title:"",description:"",customerId:"",jobTypeId:"",scheduledDate:"",scheduledTime:"",address:{street:"",city:"",state:"",zipCode:"",country:"US"},priority:"Medium",estimatedHours:0,customFields:[],assignedStaffIds:[],resourceIds:[]}),C=ce(""),R=ce(!1),w=ce({}),F=ce(null);async function k(){const S=f().find(b=>b.id===n(s).jobTypeId);if(!S){O(y,null);return}const W=n(s).assignedStaffIds.map(b=>{var N;const m=E().find(L=>L.id===b);return{staffId:b,staffName:(m==null?void 0:m.fullName)||"Unknown",hourlyRate:((N=m==null?void 0:m.wageInfo)==null?void 0:N.rate)||0,estimatedHours:n(s).estimatedHours||(S.defaultDuration?S.defaultDuration/60:2),role:(m==null?void 0:m.position)||""}}),j=n(s).resourceIds.map(b=>{const m=n(u).find(N=>N.id===b);return{id:Y(),resourceId:b,resourceName:(m==null?void 0:m.name)||"Unknown",resourceType:(m==null?void 0:m.type)||"Equipment",quantity:1,costPerUnit:(m==null?void 0:m.costPerUnit)||(m==null?void 0:m.costPerHour)||0,totalCost:(m==null?void 0:m.costPerUnit)||(m==null?void 0:m.costPerHour)||0}}),se=n(s).customFields.filter(b=>b.key.trim()&&b.value.trim()).map(b=>({id:Y(),key:b.key.trim(),value:b.value.trim(),type:"text",options:void 0}));try{O(y,pn(S,W,j,se))}catch(b){console.error("Error calculating cost estimate:",b),O(y,null)}}function Y(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}function te(S){Z(s,n(s).customerId=S.id),O(F,S),n(w).customerId&&(O(w,{...n(w)}),delete n(w).customerId)}async function K(){var S,W,j,se;O(R,!0),O(w,{});try{if(n(s).title.trim()||Z(w,n(w).title="Job title is required"),n(s).customerId||Z(w,n(w).customerId="Customer is required"),Object.keys(n(w)).length>0)return;let b=n(F);if(!b&&n(s).customerId)try{const h=await en.get(`/Customers/${n(s).customerId}`);b={id:h.id,fullName:h.name||h.fullName||"Unknown Customer",companyName:h.companyName||"",emails:h.emails||[{id:"1",email:h.email||"",type:"Work",isPrimary:!0}],phones:h.phones||[{id:"1",phone:h.phone||"",type:"Work",isPrimary:!0}],addresses:h.addresses||[],status:h.status||"Customer",notes:h.notes||[],checklists:h.checklists||[],communicationTimeline:h.communicationTimeline||[],createdAt:h.createdAt||new Date().toISOString(),updatedAt:h.updatedAt||new Date().toISOString()},O(F,b)}catch(h){console.error("Error fetching customer:",h)}if(!b){Ve({message:"Selected customer not found",type:"error"});return}let m;n(s).scheduledDate&&(m=n(s).scheduledTime?`${n(s).scheduledDate}T${n(s).scheduledTime}:00`:`${n(s).scheduledDate}T09:00:00`);const N=n(s).customFields.filter(h=>h.key.trim()&&h.value.trim()).map(h=>({id:Y(),key:h.key.trim(),value:h.value.trim(),type:"text",options:void 0})),L=n(s).assignedStaffIds.map(h=>{var U;const D=E().find(Ee=>Ee.id===h);return{staffId:h,staffName:(D==null?void 0:D.fullName)||"Unknown",hourlyRate:((U=D==null?void 0:D.wageInfo)==null?void 0:U.rate)||0,estimatedHours:n(s).estimatedHours||2,role:(D==null?void 0:D.position)||""}}),Q=n(s).resourceIds.map(h=>{const D=n(u).find(U=>U.id===h);return{id:Y(),resourceId:h,resourceName:(D==null?void 0:D.name)||"Unknown",resourceType:(D==null?void 0:D.type)||"Equipment",quantity:1,costPerUnit:(D==null?void 0:D.costPerUnit)||(D==null?void 0:D.costPerHour)||0,totalCost:(D==null?void 0:D.costPerUnit)||(D==null?void 0:D.costPerHour)||0}}),_={title:n(s).title.trim(),customerId:n(s).customerId,customerName:b.fullName,jobTypeId:n(s).jobTypeId,description:n(s).description.trim(),scheduledDateTime:m,jobAddress:n(s).address,priority:n(s).priority,estimatedDuration:n(s).estimatedHours*60,customFields:N,tags:((S=i())==null?void 0:S.tags)||[],status:((W=i())==null?void 0:W.status)||{id:"1",name:"Backlog",color:"#6B7280",order:1,isCompleted:!1},assignedStaff:L,resources:Q,estimatedCost:n(y)||{laborCost:0,materialCost:0,resourceCost:0,totalCost:0,breakdown:[]},attachments:((j=i())==null?void 0:j.attachments)||[],notes:((se=i())==null?void 0:se.notes)||""};l()==="edit"&&i()?(await Ce.updateJob(i().id,_),Ve({message:"Job updated successfully",type:"success"})):(await Ce.addJob(_),Ve({message:"Job created successfully",type:"success"})),Ce.closeJobModal()}catch(b){console.error("Error saving job:",b),Ve({message:"Failed to save job",type:"error"})}}function $(){Z(s,n(s).customFields=[...n(s).customFields,{key:"",value:""}])}function M(S){Z(s,n(s).customFields=n(s).customFields.filter((W,j)=>j!==S))}function V(){Ce.closeJobModal()}hr(()=>(o(),i(),l(),v()),()=>{var S;if(o()&&i()&&l()==="edit"){O(s,{title:i().title,description:i().description,customerId:i().customerId,jobTypeId:i().jobTypeId,scheduledDate:i().scheduledDateTime?i().scheduledDateTime.split("T")[0]:"",scheduledTime:i().scheduledDateTime?(S=i().scheduledDateTime.split("T")[1])==null?void 0:S.substring(0,5):"",address:{...i().jobAddress},priority:i().priority,estimatedHours:Math.round((i().estimatedDuration||0)/60*100)/100,customFields:(i().customFields||[]).map(j=>({key:j.key,value:j.value})),assignedStaffIds:(i().assignedStaff||[]).map(j=>j.staffId),resourceIds:(i().resources||[]).map(j=>j.resourceId)});const W=v().find(j=>j.id===i().customerId);O(C,W?W.companyName||W.fullName:""),O(F,W||null),O(R,!1),O(w,{})}else o()&&l()==="create"&&(O(s,{title:"",description:"",customerId:"",jobTypeId:"",scheduledDate:"",scheduledTime:"",address:{street:"",city:"",state:"",zipCode:"",country:"US"},priority:"Medium",estimatedHours:0,customFields:[],assignedStaffIds:[],resourceIds:[]}),O(C,""),O(F,null),O(R,!1),O(w,{}))}),hr(()=>n(s),()=>{n(s).jobTypeId&&(n(s).assignedStaffIds.length>0||n(s).resourceIds.length>0)?k():O(y,null)}),sa(),Vt();const ne=$e(()=>l()==="edit"?"Edit Job":"Create New Job");ua(e,{get title(){return n(ne)},get show(){return Ya(),o()},set show(S){Za(Zr,S)},$$events:{close:V},children:(S,W)=>{var j=Gi(),se=g(j),b=g(se),m=c(g(b),2);fe(m),p(b);var N=c(b,2),L=g(N);const Q=$e(()=>n(R)&&!!n(w).customerId),_=$e(()=>n(R)&&n(w).customerId?n(w).customerId:"");Qa(L,{get hasError(){return n(Q)},get errorMessage(){return n(_)},get customerId(){return n(s).customerId},set customerId(I){Z(s,n(s).customerId=I)},get customerSearch(){return n(C)},set customerSearch(I){O(C,I)},$$events:{selectcustomer:I=>te(I.detail)},$$legacy:!0}),p(N);var h=c(N,2),D=c(g(h),2);ee(()=>{n(s),wt(()=>{f()})});var U=g(D);U.value=U.__value="";var Ee=c(U);Ze(Ee,1,f,Dt,(I,P)=>{var z=ji(),J={},me=g(z,!0);p(z),ee(()=>{J!==(J=n(P).id)&&(z.value=(z.__value=n(P).id)??""),G(me,n(P).name)}),A(I,z)}),p(D),p(h);var re=c(h,2),Le=c(g(re),2);ee(()=>{n(s),wt(()=>{})});var Se=g(Le);Se.value=Se.__value="Low";var He=c(Se);He.value=He.__value="Medium";var Qe=c(He);Qe.value=Qe.__value="High";var nt=c(Qe);nt.value=nt.__value="Urgent",p(Le),p(re);var pt=c(re,2),mt=c(g(pt),2);fe(mt),p(pt);var gt=c(pt,2),Ge=c(g(gt),2);fe(Ge),p(gt);var it=c(gt,2),Nt=c(g(it),2);fe(Nt),p(it);var ht=c(it,2),le=c(g(ht),2);Ze(le,5,E,Dt,(I,P)=>{var z=Fi(),J=g(z);fe(J);var me,xe=c(J,3),Ue=c(xe);{var we=ge=>{var de=Li(),ot=g(de);p(de),ee(()=>G(ot,`($${n(P).wageInfo.rate??""}/hr)`)),A(ge,de)};De(Ue,ge=>{var de;(de=n(P).wageInfo)!=null&&de.rate&&ge(we)})}p(z),ee(()=>{me!==(me=n(P).id)&&(J.value=(J.__value=n(P).id)??""),G(xe,` ${n(P).fullName??""} - ${n(P).position??""} `)}),Jr(d,[],J,()=>(n(P).id,n(s).assignedStaffIds),ge=>Z(s,n(s).assignedStaffIds=ge)),A(I,z)}),p(le),p(ht);var ze=c(ht,2),et=c(g(ze),2);Ze(et,5,()=>n(u),Dt,(I,P)=>{var z=Mi(),J=g(z);fe(J);var me,xe=c(J,3),Ue=c(xe);{var we=ge=>{var de=ki(),ot=g(de);{var cr=Be=>{var st=Ie();ee(()=>G(st,`($${n(P).costPerHour??""}/hr)`)),A(Be,st)},Rt=(Be,st)=>{{var ur=bt=>{var We=Ie();ee(()=>G(We,`($${n(P).costPerUnit??""}/unit)`)),A(bt,We)};De(Be,bt=>{n(P).costPerUnit&&bt(ur)},st)}};De(ot,Be=>{n(P).costPerHour?Be(cr):Be(Rt,!1)})}p(de),A(ge,de)};De(Ue,ge=>{(n(P).costPerHour||n(P).costPerUnit)&&ge(we)})}p(z),ee(()=>{me!==(me=n(P).id)&&(J.value=(J.__value=n(P).id)??""),G(xe,` ${n(P).name??""} (${n(P).type??""}) `)}),Jr(T,[],J,()=>(n(P).id,n(s).resourceIds),ge=>Z(s,n(s).resourceIds=ge)),A(I,z)}),p(et),p(ze),p(se);var yt=c(se,2),Rr=c(g(yt),2);Ba(Rr),p(yt);var nr=c(yt,2),jr=c(g(nr),2),ir=g(jr),Lr=c(g(ir),2);fe(Lr),p(ir);var or=c(ir,2),Fr=c(g(or),2);fe(Fr),p(or);var sr=c(or,2),kr=c(g(sr),2);fe(kr),p(sr);var Mr=c(sr,2),zr=c(g(Mr),2);fe(zr),p(Mr),p(jr),p(nr);var lr=c(nr,2),dr=g(lr),Aa=c(g(dr),2);Me(Aa,{type:"button",variant:"secondary",size:"small",$$events:{click:$},children:(I,P)=>{Ne();var z=Ie("Add Field");A(I,z)},$$slots:{default:!0}}),p(dr);var Ca=c(dr,2);Ze(Ca,1,()=>n(s).customFields,Dt,(I,P,z)=>{var J=zi(),me=g(J);fe(me);var xe=c(me,2);fe(xe);var Ue=c(xe,2);Me(Ue,{type:"button",variant:"tertiary",size:"small",$$events:{click:()=>M(z)},children:(we,ge)=>{Ne();var de=Ie("Remove");A(we,de)},$$slots:{default:!0}}),p(J),he(me,()=>n(P).key,we=>(n(P).key=we,wt(()=>n(s).customFields))),he(xe,()=>n(P).value,we=>(n(P).value=we,wt(()=>n(s).customFields))),A(I,J)}),p(lr);var Ur=c(lr,2);{var $a=I=>{var P=Hi(),z=c(g(P),2),J=g(z),me=c(g(J),2),xe=g(me);p(me),p(J);var Ue=c(J,2),we=c(g(Ue),2),ge=g(we);p(we),p(Ue);var de=c(Ue,2),ot=c(g(de),2),cr=g(ot);p(ot),p(de);var Rt=c(de,2),Be=c(g(Rt),2),st=g(Be);p(Be),p(Rt),p(z);var ur=c(z,2);{var bt=We=>{var _t=Bi(),jt=c(g(_t),2);Ze(jt,5,()=>n(y).breakdown,Dt,(vr,Gr)=>{var fr=Ui(),pr=g(fr),Na=g(pr,!0);p(pr);var Wr=c(pr,2),Ra=g(Wr);p(Wr),p(fr),ee(ja=>{G(Na,n(Gr).description),G(Ra,`$${ja??""}`)},[()=>n(Gr).totalCost.toFixed(2)],$e),A(vr,fr)}),p(jt),p(_t),A(We,_t)};De(ur,We=>{n(y).breakdown.length>0&&We(bt)})}p(P),ee((We,_t,jt,vr)=>{G(xe,`$${We??""}`),G(ge,`$${_t??""}`),G(cr,`$${jt??""}`),G(st,`$${vr??""}`)},[()=>n(y).laborCost.toFixed(2),()=>n(y).materialCost.toFixed(2),()=>n(y).resourceCost.toFixed(2),()=>n(y).totalCost.toFixed(2)],$e),A(I,P)};De(Ur,I=>{n(y)&&I($a)})}var Br=c(Ur,2),Hr=g(Br);Me(Hr,{type:"button",variant:"secondary",$$events:{click:V},children:(I,P)=>{Ne();var z=Ie("Cancel");A(I,z)},$$slots:{default:!0}});var Pa=c(Hr,2);Me(Pa,{type:"submit",variant:"primary",children:(I,P)=>{Ne();var z=Ie();ee(()=>G(z,l()==="edit"?"Update Job":"Create Job")),A(I,z)},$$slots:{default:!0}}),p(Br),p(j),he(m,()=>n(s).title,I=>Z(s,n(s).title=I)),yr(D,()=>n(s).jobTypeId,I=>Z(s,n(s).jobTypeId=I)),yr(Le,()=>n(s).priority,I=>Z(s,n(s).priority=I)),he(mt,()=>n(s).scheduledDate,I=>Z(s,n(s).scheduledDate=I)),he(Ge,()=>n(s).scheduledTime,I=>Z(s,n(s).scheduledTime=I)),he(Nt,()=>n(s).estimatedHours,I=>Z(s,n(s).estimatedHours=I)),he(Rr,()=>n(s).description,I=>Z(s,n(s).description=I)),he(Lr,()=>n(s).address.street,I=>Z(s,n(s).address.street=I)),he(Fr,()=>n(s).address.city,I=>Z(s,n(s).address.city=I)),he(kr,()=>n(s).address.state,I=>Z(s,n(s).address.state=I)),he(zr,()=>n(s).address.zipCode,I=>Z(s,n(s).address.zipCode=I)),Re("submit",j,Xa(K)),A(S,j)},$$slots:{default:!0},$$legacy:!0}),Yt(),a()}var Ji=B('<div class="error-message"> </div>'),Zi=B('<div class="stage-item svelte-onmdre"><div class="drag-handle svelte-onmdre"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="9" cy="6" r="2"></circle><circle cx="9" cy="12" r="2"></circle><circle cx="9" cy="18" r="2"></circle><circle cx="15" cy="6" r="2"></circle><circle cx="15" cy="12" r="2"></circle><circle cx="15" cy="18" r="2"></circle></svg></div> <div class="stage-name svelte-onmdre"> </div> <button class="delete-button svelte-onmdre"><svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></button></div>'),Yi=B('<div class="empty-stages svelte-onmdre">No stages defined. Add at least one stage.</div>'),Vi=B('<div class="pipeline-dialog svelte-onmdre"><div class="form-group"><label for="pipeline-name">Pipeline Name</label> <input type="text" id="pipeline-name" placeholder="Enter pipeline name"> <!></div> <div class="stages-section svelte-onmdre"><h3 class="svelte-onmdre">Pipeline Stages</h3> <p class="help-text svelte-onmdre">Drag to reorder stages. Jobs will flow through these stages from left to right.</p> <div class="add-stage svelte-onmdre"><input type="text" placeholder="New stage name" class="svelte-onmdre"> <!></div> <div class="stages-list svelte-onmdre"><!> <!></div></div> <div class="dialog-actions svelte-onmdre"><!> <!></div></div>');function qi(e,r){var L,Q;Zt(r,!1);let t=la(r,"pipeline",8,null);const a=[{id:"s1",name:"Fresh"},{id:"s2",name:"Contacted"},{id:"s3",name:"Won"},{id:"s4",name:"Lost"}];let o=ce(((L=t())==null?void 0:L.name)||""),i=ce((Q=t())!=null&&Q.stages?[...t().stages]:[...a]),l=ce(""),v=ce({name:""});const f=Sr();function E(_){f("save",_)}function d(){f("cancel")}function T(){var h;if(Z(v,n(v).name=""),!n(o).trim()){Z(v,n(v).name="Pipeline name is required");return}if(n(i).length===0)return;const _={id:((h=t())==null?void 0:h.id)||"",name:n(o).trim(),stages:n(i)};E(_)}function u(){d()}function y(){if(!n(l).trim())return;const _={id:`s${Date.now()}`,name:n(l).trim()};O(i,[...n(i),_]),O(l,"")}function x(_){O(i,n(i).filter(h=>h.id!==_))}function s(_){const{detail:h}=_;O(i,[...h.items])}function C(_){const{detail:h}=_;O(i,[...h.items])}function R(_){_.key==="Enter"&&y()}Vt();var w=Vi(),F=g(w),k=c(g(F),2);fe(k);let Y;var te=c(k,2);{var K=_=>{var h=Ji(),D=g(h,!0);p(h),ee(()=>G(D,n(v).name)),A(_,h)};De(te,_=>{n(v).name&&_(K)})}p(F);var $=c(F,2),M=c(g($),4),V=g(M);fe(V);var ne=c(V,2);Me(ne,{variant:"secondary",size:"small",type:"button",$$events:{click:y},children:(_,h)=>{Ne();var D=Ie("Add Stage");A(_,D)},$$slots:{default:!0}}),p(M);var S=c(M,2),W=g(S);Ze(W,1,()=>n(i),_=>_.id,(_,h)=>{var D=Zi(),U=c(g(D),2),Ee=g(U,!0);p(U);var re=c(U,2);p(D),ee(()=>{G(Ee,n(h).name),Wa(re,"aria-label",`Remove ${n(h).name} stage`)}),Re("click",re,()=>x(n(h).id)),A(_,D)});var j=c(W,2);{var se=_=>{var h=Yi();A(_,h)};De(j,_=>{n(i).length===0&&_(se)})}p(S),va(S,(_,h)=>ct==null?void 0:ct(_,h),()=>({items:n(i),type:"pipeline-stages",flipDurationMs:200,morphDisabled:!0,dropTargetStyle:{outline:"2px dashed var(--primary)",outlineOffset:"-4px",backgroundColor:"var(--secondary-fade)"}})),Ot(()=>Re("consider",S,s)),Ot(()=>Re("finalize",S,C)),p($);var b=c($,2),m=g(b);Me(m,{variant:"tertiary",type:"button",$$events:{click:u},children:(_,h)=>{Ne();var D=Ie("Cancel");A(_,D)},$$slots:{default:!0}});var N=c(m,2);Me(N,{variant:"primary",type:"button",$$events:{click:T},children:(_,h)=>{Ne();var D=Ie("Save Pipeline");A(_,D)},$$slots:{default:!0}}),p(b),p(w),ee(_=>Y=mn(k,1,"",null,Y,_),[()=>({error:n(v).name})],$e),he(k,()=>n(o),_=>O(o,_)),he(V,()=>n(l),_=>O(l,_)),Re("keypress",V,R),A(e,w),Yt()}var Ki=B('<button class="clear-search svelte-m1cad8" type="button" aria-label="Clear search"><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></button>'),Xi=B('<div class="search-box svelte-m1cad8"><svg class="search-icon svelte-m1cad8" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.35-4.35"></path></svg> <input type="text" placeholder="Search jobs by name or customer..." class="search-input svelte-m1cad8"> <!></div> <!>',1),Qi=B('<div class="loading-container"><!> <p>Loading jobs...</p></div>'),eo=B("<div><!></div>"),to=B('<div class="empty-column-message svelte-m1cad8">No jobs in this stage</div>'),ro=B('<div class="kanban-column svelte-m1cad8"><div class="kanban-column-header svelte-m1cad8"><h3 class="svelte-m1cad8"> </h3> <span class="job-count svelte-m1cad8"> </span></div> <div class="kanban-column-content svelte-m1cad8"><!> <!></div></div>'),ao=B('<div class="pipeline-controls svelte-m1cad8"><div class="pipeline-selector svelte-m1cad8"><label for="pipeline-select" class="svelte-m1cad8">Pipeline:</label> <select id="pipeline-select" class="svelte-m1cad8"><option>Default Pipeline</option></select></div> <div class="pipeline-actions svelte-m1cad8"><!> <!></div></div> <div class="kanban-board svelte-m1cad8"></div>',1),no=B('<div class="container"><!> <main class="svelte-m1cad8"><!></main></div> <!> <!>',1);function jo(e,r){Zt(r,!1);const[t,a]=da(),o=()=>Fe(nn,"$jobsByStatus",t),i=()=>Fe(ln,"$jobStatuses",t),l=()=>Fe(on,"$jobsLoading",t),v=()=>Fe(sn,"$jobStatusesLoading",t);let f=ce(""),E=ce("default"),d=ce(!1),T=ce(null);ca(async()=>{await Promise.all([Ce.loadJobs(),Ce.loadJobStatuses(),Ce.loadJobTypes()])});let u=ce({});function y(b,m){if(!m.trim())return b;const N=m.toLowerCase();return b.filter(L=>L.title.toLowerCase().includes(N)||L.customerName&&L.customerName.toLowerCase().includes(N))}function x(b,m){O(u,{...n(u)}),Z(u,n(u)[b]=m.detail.items)}async function s(b,m){var Q;const N=m.detail.items;O(u,{...n(u),[b]:N});const L=N.find(_=>_.status.id!==b);if(L)try{await Ce.updateJobStatus(L.id,b),Ve({message:`Job "${L.title}" moved to ${((Q=i().find(_=>_.id===b))==null?void 0:Q.name)||"new status"}`,type:"success"})}catch(_){console.error("Error updating job status:",_),Ve({message:"Failed to move job",type:"error"}),await Ce.loadJobs()}}function C(){Ce.openJobModal("create")}function R(b){Ce.openJobModal("edit",b)}function w(){O(T,null),O(d,!0)}function F(){O(T,{id:"default",name:"Default Pipeline",stages:i().map(b=>({id:b.id,name:b.name}))}),O(d,!0)}function k(){O(d,!1),O(T,null)}async function Y(b){const m=b.detail;try{Ve({message:`Pipeline "${m.name}" saved successfully`,type:"success"}),k()}catch(N){console.error("Error saving pipeline:",N),Ve({message:"Failed to save pipeline",type:"error"})}}hr(()=>(o(),n(f)),()=>{const b={};Object.keys(o()).forEach(m=>{const N=o()[m]||[];b[m]=y(N,n(f))}),O(u,b)}),sa(),Vt();var te=no();Ha(b=>{Ua.title="Jobs"});var K=mr(te),$=g(K);qa($,{title:"Jobs",$$slots:{actions:(b,m)=>{var N=Xi(),L=mr(N),Q=c(g(L),2);fe(Q);var _=c(Q,2);{var h=U=>{var Ee=Ki();Re("click",Ee,()=>O(f,"")),A(U,Ee)};De(_,U=>{n(f)&&U(h)})}p(L);var D=c(L,2);Me(D,{variant:"primary",type:"button",$$events:{click:C},children:(U,Ee)=>{Ne();var re=Ie("Add Job");A(U,re)},$$slots:{default:!0}}),he(Q,()=>n(f),U=>O(f,U)),A(b,N)}}});var M=c($,2),V=g(M);{var ne=b=>{var m=Qi(),N=g(m);Ka(N,{}),Ne(2),p(m),A(b,m)},S=b=>{var m=ao(),N=mr(m),L=g(N),Q=c(g(L),2);ee(()=>{n(E),wt(()=>{})});var _=g(Q);_.value=_.__value="default",p(Q),p(L);var h=c(L,2),D=g(h);Me(D,{variant:"secondary",size:"small",$$events:{click:F},children:(re,Le)=>{Ne();var Se=Ie("Edit Pipeline");A(re,Se)},$$slots:{default:!0}});var U=c(D,2);Me(U,{variant:"secondary",size:"small",$$events:{click:w},children:(re,Le)=>{Ne();var Se=Ie("New Pipeline");A(re,Se)},$$slots:{default:!0}}),p(h),p(N);var Ee=c(N,2);Ze(Ee,5,i,re=>re.id,(re,Le)=>{var Se=ro();const He=$e(()=>n(u)[n(Le).id]||[]);var Qe=g(Se),nt=g(Qe),pt=g(nt,!0);p(nt);var mt=c(nt,2),gt=g(mt,!0);p(mt),p(Qe);var Ge=c(Qe,2),it=g(Ge);Ze(it,9,()=>n(He),le=>le.id,(le,ze)=>{var et=eo(),yt=g(et);Ri(yt,{get job(){return n(ze)},$$events:{edit:()=>R(n(ze))}}),p(et),Ja(et,()=>Ai,()=>({duration:200})),A(le,et)});var Nt=c(it,2);{var ht=le=>{var ze=to();A(le,ze)};De(Nt,le=>{n(He).length===0&&le(ht)})}p(Ge),va(Ge,(le,ze)=>ct==null?void 0:ct(le,ze),()=>({items:n(He),type:"job-cards",flipDurationMs:200,morphDisabled:!1,dropFromOthersDisabled:!1,dragDisabled:!1,dropTargetStyle:{outline:"2px dashed var(--primary)",outlineOffset:"-4px",backgroundColor:"var(--secondary-fade)"}})),Ot(()=>Re("consider",Ge,le=>x(n(Le).id,le))),Ot(()=>Re("finalize",Ge,le=>s(n(Le).id,le))),p(Se),ee(()=>{G(pt,n(Le).name),G(gt,n(He).length)}),A(re,Se)}),p(Ee),yr(Q,()=>n(E),re=>O(E,re)),A(b,m)};De(V,b=>{l()||v()?b(ne):b(S,!1)})}p(M),p(K);var W=c(K,2);Wi(W,{});var j=c(W,2);const se=$e(()=>n(T)?"Edit Pipeline":"Create Pipeline");ua(j,{get show(){return n(d)},get title(){return n(se)},$$events:{close:k},children:(b,m)=>{qi(b,{get pipeline(){return n(T)},$$events:{save:Y,cancel:k}})},$$slots:{default:!0}}),A(e,te),Yt(),a()}export{jo as component,Ro as universal};
