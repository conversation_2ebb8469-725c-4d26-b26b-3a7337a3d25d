import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as $t,u as gt,v as yt,g as ht,t as w,j as N,m as ue,k as e,ae as qt,i as s,r as t,o as k,f as Ne,s as r,n as Ce}from"../chunks/p3DoyA09.js";import{h as xt,s as v,e as bt}from"../chunks/BUelSUke.js";import{i as $}from"../chunks/DwdToawP.js";import{e as Fe,i as Te}from"../chunks/DEqeA9IH.js";import{t as _,a as i,b as C}from"../chunks/B67foYpL.js";import{d as Qt}from"../chunks/DdRd56Yq.js";import{i as Dt,s as kt}from"../chunks/DSjDIsro.js";import{i as It}from"../chunks/D3pqaimu.js";import{s as Pt,a as Ae}from"../chunks/qYb16FSw.js";import{o as St}from"../chunks/C_WNR8j8.js";import{p as Bt}from"../chunks/C3hpKoCs.js";import{g as q}from"../chunks/CSyJhG7e.js";import{P as Et}from"../chunks/CC9utfo3.js";import{B as F}from"../chunks/6Zk3JFqZ.js";import{L as Nt}from"../chunks/C8F602cz.js";import{a as g}from"../chunks/Ce-0qAhV.js";import{j as Ct,k as Ft,l as Tt,m as At}from"../chunks/SnLwHqso.js";import{c as Ut}from"../chunks/Atsggda0.js";import{f as T}from"../chunks/Chsk6cZE.js";var jt=_('<div class="loading-container svelte-1cwus25"><!> <p class="svelte-1cwus25">Loading quote...</p></div>'),Lt=_("<!> <!> <!> <!>",1),Rt=_('<div class="section-content svelte-1cwus25"> </div>'),Ht=_('<div class="additional-info svelte-1cwus25"> </div>'),Mt=_('<div class="items-row svelte-1cwus25"><div class="item-cell svelte-1cwus25"><div class="item-description svelte-1cwus25"><strong> </strong> <!></div></div> <div class="item-cell svelte-1cwus25"> </div> <div class="item-cell svelte-1cwus25"> </div> <div class="item-cell svelte-1cwus25"> </div></div>'),Vt=_('<div class="line-items svelte-1cwus25"><div class="items-table svelte-1cwus25"><div class="items-header svelte-1cwus25"><div class="item-cell svelte-1cwus25">Description</div> <div class="item-cell svelte-1cwus25">Quantity</div> <div class="item-cell svelte-1cwus25">Unit Price</div> <div class="item-cell svelte-1cwus25">Total</div></div> <!></div></div>'),zt=_('<div class="quote-section svelte-1cwus25"><h3 class="svelte-1cwus25"> </h3> <!></div>'),Gt=_('<div class="quote-notes svelte-1cwus25"><h3 class="svelte-1cwus25">Notes</h3> <p class="svelte-1cwus25"> </p></div>'),Jt=_('<div class="quote-terms svelte-1cwus25"><h3 class="svelte-1cwus25">Terms & Conditions</h3> <p class="svelte-1cwus25"> </p></div>'),Kt=_('<!> <main class="svelte-1cwus25"><div class="quote-details svelte-1cwus25"><div class="quote-header svelte-1cwus25"><div class="quote-info svelte-1cwus25"><h2 class="svelte-1cwus25"> </h2> <div class="quote-meta svelte-1cwus25"><div class="meta-item svelte-1cwus25"><span class="label svelte-1cwus25">Customer:</span> <span class="value svelte-1cwus25"> </span></div> <div class="meta-item svelte-1cwus25"><span class="label svelte-1cwus25">Issue Date:</span> <span class="value svelte-1cwus25"> </span></div> <div class="meta-item svelte-1cwus25"><span class="label svelte-1cwus25">Valid Until:</span> <span class="value svelte-1cwus25"> </span></div> <div class="meta-item svelte-1cwus25"><span class="label svelte-1cwus25">Status:</span> <span class="status-badge svelte-1cwus25"> </span></div></div></div> <div class="quote-actions svelte-1cwus25"><div class="status-controls svelte-1cwus25"><label for="status" class="svelte-1cwus25">Update Status:</label> <select id="status" class="svelte-1cwus25"><option>Draft</option><option>Sent</option><option>Accepted</option><option>Rejected</option><option>Expired</option></select></div></div></div> <div class="quote-content svelte-1cwus25"></div> <div class="quote-totals svelte-1cwus25"><div class="totals-section svelte-1cwus25"><div class="totals-row svelte-1cwus25"><span class="label svelte-1cwus25">Subtotal:</span> <span class="value svelte-1cwus25"> </span></div> <div class="totals-row svelte-1cwus25"><span class="label svelte-1cwus25">Tax:</span> <span class="value svelte-1cwus25"> </span></div> <div class="totals-row total svelte-1cwus25"><span class="label svelte-1cwus25">Total:</span> <span class="value svelte-1cwus25"> </span></div></div></div> <!> <!></div></main>',1),Ot=_('<div class="error-container svelte-1cwus25"><h2>Quote Not Found</h2> <p class="svelte-1cwus25">The requested quote could not be found.</p> <!></div>'),Wt=_('<div class="container"><!></div>');function $s(Ue,je){$t(je,!1);const[ne,Le]=Pt(),de=()=>Ae(Bt,"$page",ne),Re=()=>Ae(Ut,"$customers",ne);let j=ue(!0),a=ue(null),pe=ue();St(async()=>{await He()});async function He(){N(j,!0);try{N(a,await Ct(e(pe))),e(a)||(g({message:"Quote not found",type:"error"}),q("/quotes"))}catch(o){console.error("Error loading quote:",o),g({message:"Failed to load quote",type:"error"}),q("/quotes")}finally{N(j,!1)}}async function Me(o){if(e(a))try{const d=await At(e(a).id,{status:{id:Date.now().toString(),name:o,color:Ge(o)}});N(a,d),g({message:"Quote status updated",type:"success"})}catch(d){console.error("Error updating quote status:",d),g({message:"Failed to update quote status",type:"error"})}}async function Ve(){if(e(a))try{const o=await Tt(e(a).id);g({message:"Quote converted to invoice successfully",type:"success"}),q(`/invoices/${o}`)}catch(o){console.error("Error converting quote to invoice:",o),g({message:"Failed to convert quote to invoice",type:"error"})}}async function ze(){if(e(a)&&window.confirm(`Are you sure you want to delete quote ${e(a).quoteNumber}?`))try{await Ft(e(a).id),g({message:"Quote deleted successfully",type:"success"}),q("/quotes")}catch(o){console.error("Error deleting quote:",o),g({message:"Failed to delete quote",type:"error"})}}function Ge(o){switch(o){case"Draft":return"#6B7280";case"Sent":return"#3B82F6";case"Accepted":return"#10B981";case"Rejected":return"#EF4444";case"Expired":return"#F59E0B";default:return"#6B7280"}}function Je(o){const d=Re().find(I=>I.id===o);return d?d.companyName||d.fullName:"Unknown Customer"}function _e(o){return new Date(o).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}gt(()=>de(),()=>{N(pe,de().params.id)}),yt(),It();var L=Wt();xt(o=>{w(()=>qt.title=e(a)?`Quote ${e(a).quoteNumber}`:"Quote Details")});var Ke=s(L);{var Oe=o=>{var d=jt(),I=s(d);Nt(I,{}),k(2),t(d),i(o,d)},We=(o,d)=>{{var I=x=>{var b=Kt(),A=Ne(b);Et(A,{get title(){return`Quote ${e(a).quoteNumber??""}`},$$slots:{actions:(u,l)=>{var n=Lt(),p=Ne(n);F(p,{variant:"tertiary",$$events:{click:()=>q("/quotes")},children:(c,m)=>{k();var f=C("Back to Quotes");i(c,f)},$$slots:{default:!0}});var Q=r(p,2);F(Q,{variant:"secondary",$$events:{click:()=>e(a)&&q(`/quotes/${e(a).id}/edit`)},children:(c,m)=>{k();var f=C("Edit Quote");i(c,f)},$$slots:{default:!0}});var D=r(Q,2);{var se=c=>{F(c,{variant:"primary",$$events:{click:Ve},children:(m,f)=>{k();var S=C("Convert to Invoice");i(m,S)},$$slots:{default:!0}})};$(D,c=>{e(a).status.name==="Accepted"&&c(se)})}var ae=r(D,2);F(ae,{variant:"tertiary",$$events:{click:ze},children:(c,m)=>{k();var f=C("Delete");i(c,f)},$$slots:{default:!0}}),i(u,n)}}});var U=r(A,2),R=s(U),P=s(R),H=s(P),M=s(H),Ye=s(M);t(M);var me=r(M,2),V=s(me),fe=r(s(V),2),Ze=s(fe,!0);t(fe),t(V);var z=r(V,2),we=r(s(z),2),et=s(we,!0);t(we),t(z);var G=r(z,2),$e=r(s(G),2),tt=s($e,!0);t($e),t(G);var ge=r(G,2),J=r(s(ge),2),st=s(J,!0);t(J),t(ge),t(me),t(H);var ye=r(H,2),he=s(ye),y=r(s(he),2);Dt(y,()=>e(a).status.name);var qe,K=s(y);K.value=K.__value="Draft";var O=r(K);O.value=O.__value="Sent";var W=r(O);W.value=W.__value="Accepted";var X=r(W);X.value=X.__value="Rejected";var xe=r(X);xe.value=xe.__value="Expired",t(y),t(he),t(ye),t(P);var Y=r(P,2);Fe(Y,5,()=>e(a).sections,Te,(u,l)=>{var n=zt(),p=s(n),Q=s(p,!0);t(p);var D=r(p,2);{var se=c=>{var m=Rt(),f=s(m,!0);t(m),w(()=>v(f,e(l).content)),i(c,m)},ae=(c,m)=>{{var f=S=>{var re=Vt(),Se=s(re),ct=r(s(Se),2);Fe(ct,1,()=>e(l).lineItems||[],Te,(ut,h)=>{var oe=Mt(),ve=s(oe),Be=s(ve),ie=s(Be),nt=s(ie,!0);t(ie);var dt=r(ie,2);{var pt=B=>{var E=Ht(),wt=s(E,!0);t(E),w(()=>v(wt,e(h).additionalInfo)),i(B,E)};$(dt,B=>{e(h).additionalInfo&&B(pt)})}t(Be),t(ve);var le=r(ve,2),_t=s(le,!0);t(le);var ce=r(le,2),mt=s(ce,!0);t(ce);var Ee=r(ce,2),ft=s(Ee,!0);t(Ee),t(oe),w((B,E)=>{v(nt,e(h).description),v(_t,e(h).quantity),v(mt,B),v(ft,E)},[()=>T(e(h).unitPrice),()=>T(e(h).quantity*e(h).unitPrice)],Ce),i(ut,oe)}),t(Se),t(re),i(S,re)};$(c,S=>{e(l).type==="lineItems"&&S(f)},m)}};$(D,c=>{e(l).type==="text"?c(se):c(ae,!1)})}t(n),w(()=>v(Q,e(l).title)),i(u,n)}),t(Y);var Z=r(Y,2),be=s(Z),ee=s(be),Qe=r(s(ee),2),at=s(Qe,!0);t(Qe),t(ee);var te=r(ee,2),De=r(s(te),2),rt=s(De,!0);t(De),t(te);var ke=r(te,2),Ie=r(s(ke),2),ot=s(Ie,!0);t(Ie),t(ke),t(be),t(Z);var Pe=r(Z,2);{var vt=u=>{var l=Gt(),n=r(s(l),2),p=s(n,!0);t(n),t(l),w(()=>v(p,e(a).notes)),i(u,l)};$(Pe,u=>{e(a).notes&&u(vt)})}var it=r(Pe,2);{var lt=u=>{var l=Jt(),n=r(s(l),2),p=s(n,!0);t(n),t(l),w(()=>v(p,e(a).terms)),i(u,l)};$(it,u=>{e(a).terms&&u(lt)})}t(R),t(U),w((u,l,n,p,Q,D)=>{v(Ye,`Quote #${e(a).quoteNumber??""}`),v(Ze,u),v(et,l),v(tt,n),Qt(J,`background-color: ${e(a).status.color??""}`),v(st,e(a).status.name),qe!==(qe=e(a).status.name)&&(y.value=(y.__value=e(a).status.name)??"",kt(y,e(a).status.name)),v(at,p),v(rt,Q),v(ot,D)},[()=>Je(e(a).customerId),()=>_e(e(a).issueDate),()=>_e(e(a).expiryDate||e(a).issueDate),()=>T(e(a).subtotal),()=>T(e(a).taxAmount),()=>T(e(a).totalAmount)],Ce),bt("change",y,u=>Me(u.target.value)),i(x,b)},Xe=x=>{var b=Ot(),A=r(s(b),4);F(A,{$$events:{click:()=>q("/quotes")},children:(U,R)=>{k();var P=C("Back to Quotes");i(U,P)},$$slots:{default:!0}}),t(b),i(x,b)};$(o,x=>{e(a)?x(I):x(Xe,!1)},d)}};$(Ke,o=>{e(j)?o(Oe):o(We,!1)})}t(L),i(Ue,L),ht(),Le()}export{$s as component};
