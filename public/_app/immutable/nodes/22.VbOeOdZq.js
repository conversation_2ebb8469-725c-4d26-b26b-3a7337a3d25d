import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as nt,m as P,u as it,v as lt,f as he,t as G,i as a,s as r,k as e,g as ct,j as n,l as b,n as re,o as j,r as o,q as we}from"../chunks/p3DoyA09.js";import{e as Le,r as Be,s as ie}from"../chunks/BUelSUke.js";import{i as X}from"../chunks/DwdToawP.js";import{t as A,a as u,b as Y,e as dt}from"../chunks/B67foYpL.js";import{r as I,s as ut}from"../chunks/DdRd56Yq.js";import{s as qe}from"../chunks/D7jLSc-x.js";import{b as T,a as pt}from"../chunks/WI3NPOEW.js";import{b as $e}from"../chunks/DSjDIsro.js";import{p as je}from"../chunks/Bfc47y5P.js";import{i as vt}from"../chunks/D3pqaimu.js";import{o as ft}from"../chunks/C_WNR8j8.js";import{j as mt,o as Ie,p as gt,q as yt}from"../chunks/D9cKHHet.js";import{B as J}from"../chunks/6Zk3JFqZ.js";import{M as ze}from"../chunks/BWn8tY11.js";import{C as bt}from"../chunks/BbevrtJW.js";import{P as _t}from"../chunks/CC9utfo3.js";import{L as xt}from"../chunks/C8F602cz.js";import{G as ht}from"../chunks/DEPinlMt.js";import{a as K}from"../chunks/Ce-0qAhV.js";var wt=A('<div slot="actions"><!></div>'),$t=A("<span> </span>"),kt=A('<div class="flex items-center"><div></div> <span class="ml-2 text-sm"> </span></div>'),Pt=A('<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path></svg> <span class="hidden sm:inline ml-1">Edit</span>',1),At=A('<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg> <span class="hidden sm:inline ml-1">Maint.</span>',1),Mt=A('<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path></svg> <span class="hidden sm:inline ml-1">Delete</span>',1),Ct=A('<div class="flex space-x-1 md:space-x-2"><!> <!> <!></div>'),Rt=A('<span class="text-gray-400">N/A</span>'),Dt=A("<!> <!>",1),St=A('<form class="space-y-6"><div class="grid gap-4 md:grid-cols-2"><div><label for="name" class="block text-sm font-medium text-gray-700 mb-1">Name *</label> <input id="name" type="text" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></div> <div><label for="type" class="block text-sm font-medium text-gray-700 mb-1">Type *</label> <select id="type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option>Equipment</option><option>Vehicle</option><option>Tool</option><option>Material</option></select></div></div> <div><label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label> <textarea id="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea></div> <div class="grid gap-4 md:grid-cols-2"><div><label for="costPerHour" class="block text-sm font-medium text-gray-700 mb-1">Cost Per Hour ($)</label> <input id="costPerHour" type="number" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></div> <div><label for="costPerUnit" class="block text-sm font-medium text-gray-700 mb-1">Cost Per Unit ($)</label> <input id="costPerUnit" type="number" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></div></div> <div class="grid gap-4 md:grid-cols-2"><div><label for="location" class="block text-sm font-medium text-gray-700 mb-1">Location</label> <input id="location" type="text" placeholder="e.g., Main Office, Storage Room" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></div> <div class="flex items-center"><label class="flex items-center gap-2"><input type="checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"> <span class="text-sm font-medium text-gray-700">Available</span></label></div></div> <div class="flex justify-end gap-2"><!> <!></div></form>'),Et=A('<form class="space-y-6"><div class="grid gap-4 md:grid-cols-2"><div><label for="maintenanceDate" class="block text-sm font-medium text-gray-700 mb-1">Date *</label> <input id="maintenanceDate" type="date" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></div> <div><label for="maintenanceType" class="block text-sm font-medium text-gray-700 mb-1">Type *</label> <select id="maintenanceType" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option>Routine</option><option>Repair</option><option>Inspection</option></select></div></div> <div><label for="maintenanceDescription" class="block text-sm font-medium text-gray-700 mb-1">Description *</label> <textarea id="maintenanceDescription" rows="3" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea></div> <div class="grid gap-4 md:grid-cols-2"><div><label for="maintenanceCost" class="block text-sm font-medium text-gray-700 mb-1">Cost ($)</label> <input id="maintenanceCost" type="number" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></div> <div><label for="performedBy" class="block text-sm font-medium text-gray-700 mb-1">Performed By</label> <input id="performedBy" type="text" placeholder="Name or company" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></div></div> <div class="flex justify-end gap-2"><!> <!></div></form>'),Tt=A('<div class="container"><!> <main><div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-6"><div class="flex flex-col sm:flex-row gap-4"><div class="flex-1"><input type="text" placeholder="Search resources..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"></div> <div><select class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"><option>All Types</option><option>Equipment</option><option>Vehicles</option><option>Tools</option><option>Materials</option></select></div></div></div> <!></main></div> <!> <!> <!>',1);function tr(Ne,Oe){nt(Oe,!1);const ue=P();let pe=P([]),Z=P(!1),le=P(!1),ae=P(!1),z=P(null),ce=null,oe=null,S=P(!1),ee=P("All"),se=P("");const ke=[{text:"Name",key:"name",sortable:!0},{text:"Type",key:"type",sortable:!0},{text:"Cost",key:"cost",sortable:!1},{text:"Location",key:"location",sortable:!0},{text:"Available",key:"isAvailable",sortable:!0},{text:"Actions",key:"actions",sortable:!1}];let g=P({key:"name",direction:"ascending"}),l=P({name:"",type:"Equipment",description:"",costPerHour:0,costPerUnit:0,isAvailable:!0,location:""}),f=P({date:new Date().toISOString().split("T")[0],type:"Routine",description:"",cost:0,performedBy:""});ft(async()=>{await de()});async function de(){n(S,!0);try{n(pe,await mt())}catch(t){console.error("Error loading resources:",t),K({type:"error",message:"Error loading resources"})}finally{n(S,!1)}}function Ve(t){ke.find(p=>p.key===t&&!p.sortable)||(e(g).key===t?b(g,e(g).direction=e(g).direction==="ascending"?"descending":"ascending"):(b(g,e(g).key=t),b(g,e(g).direction="ascending")))}function Fe(){Pe(),n(Z,!0),n(z,null)}function Ge(t){n(l,{name:t.name,type:t.type,description:t.description||"",costPerHour:t.costPerHour||0,costPerUnit:t.costPerUnit||0,isAvailable:t.isAvailable,location:t.location||""}),n(z,t),n(Z,!0)}function Pe(){n(l,{name:"",type:"Equipment",description:"",costPerHour:0,costPerUnit:0,isAvailable:!0,location:""})}async function Je(){var t,p;n(S,!0);try{const s={...e(l),maintenanceSchedule:((t=e(z))==null?void 0:t.maintenanceSchedule)||[],createdAt:((p=e(z))==null?void 0:p.createdAt)||new Date().toISOString(),updatedAt:new Date().toISOString()};e(z)?(await Ie(e(z).id,s),K({type:"success",message:"Resource updated successfully"})):(await gt(s),K({type:"success",message:"Resource created successfully"})),await de(),n(Z,!1),Pe()}catch(s){console.error("Error saving resource:",s),K({type:"error",message:"Error saving resource"})}finally{n(S,!1)}}function Ke(t){ce=t,n(le,!0)}async function Qe(){if(ce){n(S,!0);try{await yt(ce.id),await de(),K({type:"success",message:"Resource deleted successfully"})}catch(t){console.error("Error deleting resource:",t),K({type:"error",message:"Error deleting resource"})}finally{n(S,!1),n(le,!1),ce=null}}}function We(t){oe=t,Xe(),n(ae,!0)}function Xe(){n(f,{date:new Date().toISOString().split("T")[0],type:"Routine",description:"",cost:0,performedBy:""})}async function Ye(){if(!oe)return;const t={id:Date.now().toString(),...e(f)},p={...oe,maintenanceSchedule:[...oe.maintenanceSchedule||[],t],updatedAt:new Date().toISOString()};try{await Ie(oe.id,p),await de(),K({type:"success",message:"Maintenance entry added successfully"}),n(ae,!1)}catch(s){console.error("Error adding maintenance entry:",s),K({type:"error",message:"Error adding maintenance entry"})}}function Ze(t){return t.costPerHour&&t.costPerUnit?`$${t.costPerHour}/hr, $${t.costPerUnit}/unit`:t.costPerHour?`$${t.costPerHour}/hour`:t.costPerUnit?`$${t.costPerUnit}/unit`:"No cost set"}function et(t){switch(t){case"Equipment":return"bg-blue-100 text-blue-800";case"Vehicle":return"bg-green-100 text-green-800";case"Tool":return"bg-yellow-100 text-yellow-800";case"Material":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}}it(()=>(e(pe),e(ee),e(se),e(g)),()=>{n(ue,(()=>{let t=e(pe).filter(p=>{const s=e(ee)==="All"||p.type===e(ee),y=p.name.toLowerCase().includes(e(se).toLowerCase())||(p.description||"").toLowerCase().includes(e(se).toLowerCase());return s&&y});return e(g).key&&t.sort((p,s)=>{const y=e(g).key;let i=p[y],c=s[y];if(y==="isAvailable"&&typeof i=="boolean"&&typeof c=="boolean")return i===c?0:e(g).direction==="ascending"?i?-1:1:i?1:-1;if(typeof i=="string"&&typeof c=="string")i=i.toLowerCase(),c=c.toLowerCase();else if(!(typeof i=="number"&&typeof c=="number")){if(i==null)return e(g).direction==="ascending"?1:-1;if(c==null)return e(g).direction==="ascending"?-1:1;i=String(i).toLowerCase(),c=String(c).toLowerCase()}return i<c?e(g).direction==="ascending"?-1:1:i>c?e(g).direction==="ascending"?1:-1:0}),t})())}),lt(),vt();var Ae=Tt(),ve=he(Ae),Me=a(ve);_t(Me,{title:"Resources",$$slots:{actions:(t,p)=>{var s=wt(),y=a(s);J(y,{variant:"primary",$$events:{click:Fe},children:(i,c)=>{j();var M=Y("Add Resource");u(i,M)},$$slots:{default:!0}}),o(s),u(t,s)}}});var Ce=r(Me,2),fe=a(Ce),Re=a(fe),me=a(Re),De=a(me);I(De),o(me);var Se=r(me,2),ge=a(Se);G(()=>{e(ee),we(()=>{})});var ye=a(ge);ye.value=ye.__value="All";var be=r(ye);be.value=be.__value="Equipment";var _e=r(be);_e.value=_e.__value="Vehicle";var xe=r(_e);xe.value=xe.__value="Tool";var Ee=r(xe);Ee.value=Ee.__value="Material",o(ge),o(Se),o(Re),o(fe);var tt=r(fe,2);{var rt=t=>{xt(t,{})},at=t=>{var p=Dt(),s=he(p);ht(s,{headers:ke,get dataRows(){return e(ue)},emptyMessage:"No resources found matching your criteria.",get currentSort(){return e(g)},onHeaderClick:Ve,$$slots:{cell:(c,M)=>{const v=re(()=>M.row),_=re(()=>M.headerKey),H=re(()=>M.value);var U=dt(),N=he(U);{var O=x=>{var h=$t(),L=a(h,!0);o(h),G(B=>{qe(h,1,`px-2 py-0.5 text-xs font-medium rounded-full ${B??""}`),ie(L,e(v).type)},[()=>et(e(v).type)],re),u(x,h)},V=(x,h)=>{{var L=$=>{var C=Y();G(q=>ie(C,q),[()=>Ze(e(v))],re),u($,C)},B=($,C)=>{{var q=R=>{var k=kt(),F=a(k),Q=r(F,2),D=a(Q,!0);o(Q),o(k),G(()=>{qe(F,1,`w-2.5 h-2.5 rounded-full ${e(v).isAvailable?"bg-green-500":"bg-red-500"}`),ut(F,"title",e(v).isAvailable?"Available":"Unavailable"),ie(D,e(v).isAvailable?"Available":"Unavailable")}),u(R,k)},m=(R,k)=>{{var F=D=>{var W=Ct(),d=a(W);J(d,{variant:"secondary",size:"small",title:"Edit Resource",$$events:{click:()=>Ge(e(v))},children:(E,Ue)=>{var ne=Pt();j(2),u(E,ne)},$$slots:{default:!0}});var te=r(d,2);J(te,{variant:"tertiary",size:"small",title:"Manage Maintenance",$$events:{click:()=>We(e(v))},children:(E,Ue)=>{var ne=At();j(2),u(E,ne)},$$slots:{default:!0}});var w=r(te,2);J(w,{variant:"danger",size:"small",title:"Delete Resource",$$events:{click:()=>Ke(e(v))},children:(E,Ue)=>{var ne=Mt();j(2),u(E,ne)},$$slots:{default:!0}}),o(W),u(D,W)},Q=(D,W)=>{{var d=w=>{var E=Y();G(()=>ie(E,e(H))),u(w,E)},te=w=>{var E=Rt();u(w,E)};X(D,w=>{e(v)[e(_)]!==null&&e(v)[e(_)]!==void 0?w(d):w(te,!1)},W)}};X(R,D=>{e(_)==="actions"?D(F):D(Q,!1)},k)}};X($,R=>{e(_)==="isAvailable"?R(q):R(m,!1)},C)}};X(x,$=>{e(_)==="cost"?$(L):$(B,!1)},h)}};X(N,x=>{e(_)==="type"?x(O):x(V,!1)})}u(c,U)}}});var y=r(s,2);{var i=c=>{};X(y,c=>{e(ue).length===0&&!e(S)&&c(i)})}u(t,p)};X(tt,t=>{e(S)?t(rt):t(at,!1)})}o(Ce),o(ve);var Te=r(ve,2);const ot=re(()=>e(z)?"Edit Resource":"Create Resource");ze(Te,{get title(){return e(ot)},get show(){return e(Z)},set show(t){n(Z,t)},children:(t,p)=>{var s=St(),y=a(s),i=a(y),c=r(a(i),2);I(c),o(i);var M=r(i,2),v=r(a(M),2);G(()=>{e(l),we(()=>{})});var _=a(v);_.value=_.__value="Equipment";var H=r(_);H.value=H.__value="Vehicle";var U=r(H);U.value=U.__value="Tool";var N=r(U);N.value=N.__value="Material",o(v),o(M),o(y);var O=r(y,2),V=r(a(O),2);Be(V),o(O);var x=r(O,2),h=a(x),L=r(a(h),2);I(L),o(h);var B=r(h,2),$=r(a(B),2);I($),o(B),o(x);var C=r(x,2),q=a(C),m=r(a(q),2);I(m),o(q);var R=r(q,2),k=a(R),F=a(k);I(F),j(2),o(k),o(R),o(C);var Q=r(C,2),D=a(Q);J(D,{type:"button",variant:"secondary",$$events:{click:()=>n(Z,!1)},children:(d,te)=>{j();var w=Y("Cancel");u(d,w)},$$slots:{default:!0}});var W=r(D,2);J(W,{type:"submit",variant:"primary",get disabled(){return e(S)},children:(d,te)=>{j();var w=Y();G(()=>ie(w,e(S)?"Saving...":e(z)?"Update":"Create")),u(d,w)},$$slots:{default:!0}}),o(Q),o(s),T(c,()=>e(l).name,d=>b(l,e(l).name=d)),$e(v,()=>e(l).type,d=>b(l,e(l).type=d)),T(V,()=>e(l).description,d=>b(l,e(l).description=d)),T(L,()=>e(l).costPerHour,d=>b(l,e(l).costPerHour=d)),T($,()=>e(l).costPerUnit,d=>b(l,e(l).costPerUnit=d)),T(m,()=>e(l).location,d=>b(l,e(l).location=d)),pt(F,()=>e(l).isAvailable,d=>b(l,e(l).isAvailable=d)),Le("submit",s,je(Je)),u(t,s)},$$slots:{default:!0},$$legacy:!0});var He=r(Te,2);ze(He,{title:"Add Maintenance Entry",get show(){return e(ae)},set show(t){n(ae,t)},children:(t,p)=>{var s=Et(),y=a(s),i=a(y),c=r(a(i),2);I(c),o(i);var M=r(i,2),v=r(a(M),2);G(()=>{e(f),we(()=>{})});var _=a(v);_.value=_.__value="Routine";var H=r(_);H.value=H.__value="Repair";var U=r(H);U.value=U.__value="Inspection",o(v),o(M),o(y);var N=r(y,2),O=r(a(N),2);Be(O),o(N);var V=r(N,2),x=a(V),h=r(a(x),2);I(h),o(x);var L=r(x,2),B=r(a(L),2);I(B),o(L),o(V);var $=r(V,2),C=a($);J(C,{type:"button",variant:"secondary",$$events:{click:()=>n(ae,!1)},children:(m,R)=>{j();var k=Y("Cancel");u(m,k)},$$slots:{default:!0}});var q=r(C,2);J(q,{type:"submit",variant:"primary",children:(m,R)=>{j();var k=Y("Add Entry");u(m,k)},$$slots:{default:!0}}),o($),o(s),T(c,()=>e(f).date,m=>b(f,e(f).date=m)),$e(v,()=>e(f).type,m=>b(f,e(f).type=m)),T(O,()=>e(f).description,m=>b(f,e(f).description=m)),T(h,()=>e(f).cost,m=>b(f,e(f).cost=m)),T(B,()=>e(f).performedBy,m=>b(f,e(f).performedBy=m)),Le("submit",s,je(Ye)),u(t,s)},$$slots:{default:!0},$$legacy:!0});var st=r(He,2);bt(st,{title:"Delete Resource",message:"Are you sure you want to delete this resource? This action cannot be undone.",get show(){return e(le)},set show(t){n(le,t)},$$events:{confirm:Qe},$$legacy:!0}),T(De,()=>e(se),t=>n(se,t)),$e(ge,()=>e(ee),t=>n(ee,t)),u(Ne,Ae),ct()}export{tr as component};
