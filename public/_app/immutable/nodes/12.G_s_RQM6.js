import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as ot,f as oe,g as it,i as a,k as e,m as N,n as ie,s as r,j as m,o as ne,r as t,t as H}from"../chunks/p3DoyA09.js";import{r as nt,s as d}from"../chunks/BUelSUke.js";import{i as S}from"../chunks/DwdToawP.js";import{e as Le,i as Te}from"../chunks/DEqeA9IH.js";import{t as j,a as i,e as Ae,b as W}from"../chunks/B67foYpL.js";import{r as Fe,d as Me}from"../chunks/DdRd56Yq.js";import{b as ye}from"../chunks/WI3NPOEW.js";import{i as lt}from"../chunks/D3pqaimu.js";import{s as vt,a as dt}from"../chunks/qYb16FSw.js";import{o as ct}from"../chunks/C_WNR8j8.js";import{p as mt}from"../chunks/C3hpKoCs.js";import{g as pt}from"../chunks/CSyJhG7e.js";import{P as ft}from"../chunks/CC9utfo3.js";import{L as ut}from"../chunks/C8F602cz.js";import{a as G}from"../chunks/Ce-0qAhV.js";import{j as gt,a as be}from"../chunks/DJsmiAoD.js";import{f as le}from"../chunks/Chsk6cZE.js";import"../chunks/BvrzullT.js";import{B as ve}from"../chunks/6Zk3JFqZ.js";import"../chunks/Atsggda0.js";import{M as jt}from"../chunks/BWn8tY11.js";const Ce="ejp_email_history";function qe(){const b=localStorage.getItem(Ce);return b?JSON.parse(b):[]}function _t(b){localStorage.setItem(Ce,JSON.stringify(b))}function Ne(){return Math.random().toString(36).substr(2,9)}async function ht(b,O="custom",B){return new Promise(L=>{setTimeout(()=>{const E=Math.random()>.1,K=E?`msg_${Ne()}`:void 0,s={id:Ne(),to:b.to,subject:b.subject,status:E?"sent":"failed",sentAt:new Date().toISOString(),error:E?void 0:"Mock error: Email service temporarily unavailable",type:O,relatedId:B},C=qe();C.unshift(s),_t(C),L({success:E,messageId:K,message:E?"Email sent successfully":"Failed to send email",error:E?void 0:"Mock error: Email service temporarily unavailable"})},1e3+Math.random()*2e3)})}async function yt(b,O,B,L,E,K){const s=`Invoice ${L} - ${xe(E)}`;return`${B}${L}${xe(E)}${new Date().toLocaleDateString()}${new Date(Date.now()+30*24*60*60*1e3).toLocaleDateString()}`,`${B}${L}${xe(E)}${new Date().toLocaleDateString()}${new Date(Date.now()+30*24*60*60*1e3).toLocaleDateString()}`,ht({to:O,subject:s},"invoice",b)}async function Be(b){return new Promise(O=>{setTimeout(()=>{const B=qe();O(B.filter(L=>L.relatedId===b&&L.type==="invoice"))},100)})}function xe(b){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(b)}var bt=j("<!> <!> <!>",1),xt=j('<div class="error-message svelte-jfmj8m"><p> </p></div>'),$t=j('<div><h3 class="svelte-jfmj8m">Notes</h3> <p class="svelte-jfmj8m"> </p></div>'),Dt=j('<div><h3 class="svelte-jfmj8m">Payment Terms</h3> <p class="svelte-jfmj8m"> </p></div>'),wt=j('<div class="notes-section svelte-jfmj8m"><!> <!></div>'),It=j('<tr class="svelte-jfmj8m"><td class="svelte-jfmj8m"> </td><td class="svelte-jfmj8m"> </td><td class="svelte-jfmj8m"> </td><td class="svelte-jfmj8m"> </td><td class="svelte-jfmj8m"> </td></tr>'),Et=j('<div class="table-container svelte-jfmj8m"><table class="line-items-table svelte-jfmj8m"><thead><tr><th class="svelte-jfmj8m">Description</th><th class="svelte-jfmj8m">Quantity</th><th class="svelte-jfmj8m">Unit Price</th><th class="svelte-jfmj8m">Tax Rate</th><th class="svelte-jfmj8m">Line Total</th></tr></thead><tbody></tbody></table></div>'),Pt=j("<p>No line items found.</p>"),St=j('<div class="email-error svelte-jfmj8m"> </div>'),kt=j('<div class="email-history-item svelte-jfmj8m"><div class="email-info svelte-jfmj8m"><div class="email-header svelte-jfmj8m"><span class="email-to svelte-jfmj8m"> </span> <span class="email-status svelte-jfmj8m"> </span></div> <div class="email-subject svelte-jfmj8m"> </div> <div class="email-date svelte-jfmj8m"> </div> <!></div></div>'),Lt=j('<div class="email-history-section svelte-jfmj8m"><h3 class="svelte-jfmj8m">Email History</h3> <div class="email-history-list svelte-jfmj8m"></div></div>'),Tt=j('<div class="invoice-details svelte-jfmj8m"><div class="invoice-header svelte-jfmj8m"><h2 class="svelte-jfmj8m"> </h2> <div class="invoice-status"><span class="status-badge svelte-jfmj8m"> </span></div></div> <div class="invoice-info-grid svelte-jfmj8m"><div class="info-section svelte-jfmj8m"><h3 class="svelte-jfmj8m">Customer Information</h3> <p class="svelte-jfmj8m"><strong>Name:</strong> Customer</p> <p class="svelte-jfmj8m"><strong>Email:</strong> N/A</p></div> <div class="info-section svelte-jfmj8m"><h3 class="svelte-jfmj8m">Invoice Information</h3> <p class="svelte-jfmj8m"><strong>Issue Date:</strong> </p> <p class="svelte-jfmj8m"><strong>Due Date:</strong> </p> <p class="svelte-jfmj8m"><strong>Total Amount:</strong> </p></div></div> <!> <h3>Line Items</h3> <!> <!></div>'),At=j("<p>Invoice not found.</p>"),Ft=j('<div class="preview-details svelte-jfmj8m"><p class="svelte-jfmj8m"><strong>Invoice:</strong> </p> <p class="svelte-jfmj8m"><strong>Amount:</strong> </p> <p class="svelte-jfmj8m"><strong>Customer:</strong> Customer</p></div>'),Mt=j('<div class="modal-body svelte-jfmj8m"><div class="form-group svelte-jfmj8m"><label for="email-address" class="svelte-jfmj8m">Email Address</label> <input id="email-address" type="email" placeholder="<EMAIL>" required class="svelte-jfmj8m"></div> <div class="form-group svelte-jfmj8m"><label for="email-subject" class="svelte-jfmj8m">Subject</label> <input id="email-subject" type="text" placeholder="Invoice subject" required class="svelte-jfmj8m"></div> <div class="form-group svelte-jfmj8m"><label for="email-message" class="svelte-jfmj8m">Additional Message (Optional)</label> <textarea id="email-message" placeholder="Add a personal message to include with the invoice..." rows="4" class="svelte-jfmj8m"></textarea></div> <div class="email-preview svelte-jfmj8m"><h4 class="svelte-jfmj8m">Email Preview</h4> <p class="svelte-jfmj8m">The invoice will be sent as a PDF attachment with a professional email template.</p> <!></div></div>'),Nt=j("<!> <!>",1),Bt=j('<div class="container"><!> <main class="svelte-jfmj8m"><!></main></div> <!>',1);function na(b,O){ot(O,!1);const[B,L]=vt(),E=()=>dt(mt,"$page",B);let K=null,s=N(null),C=N(!0),R=N(null),V=N(!1),de=N([]),X=N(!1),Z=N(""),pe=N(""),$e=N("");ct(async()=>{K=E().params.invoiceId,K?await He(K):(m(R,"Invoice ID not provided."),m(C,!1))});async function He(o){m(C,!0),m(R,null);try{if(m(s,await gt(o)),!e(s))throw new Error("Invoice not found");m(de,await Be(o));const l=e(s).invoiceLines.reduce((f,c)=>f+c.total,0);m(pe,`Invoice ${e(s).invoiceNumber||"Draft"} - ${le(l)}`)}catch(l){console.error("Error fetching invoice details:",l),m(R,l instanceof Error?l.message:"An unknown error occurred"),G({type:"error",message:`Failed to load invoice: ${e(R)}`})}finally{m(C,!1)}}async function Oe(){if(e(s))try{G({message:"PDF export not yet implemented for new format",type:"info"})}catch(o){console.error("Error exporting PDF:",o),G({message:"Failed to export PDF",type:"error"})}}async function Re(){var o;if(!e(s)||!e(Z).trim()){G({message:"Please enter a valid email address",type:"error"});return}m(V,!0);try{const l=await Ue(e(s)),f=await Ye(l),c={filename:`${e(s).invoiceNumber}.pdf`,content:f,contentType:"application/pdf"},_=e(s).invoiceLines.reduce((h,$)=>h+$.total,0),v=await yt(e(s).id||"",e(Z),"Customer",((o=e(s).invoiceNumber)==null?void 0:o.toString())||"Draft",_,c);if(v.success)G({message:"Invoice email sent successfully",type:"success"}),m(de,await Be(e(s).id||"")),m(X,!1);else throw new Error(v.error||"Failed to send email")}catch(l){console.error("Error sending email:",l),G({message:l instanceof Error?l.message:"Failed to send email",type:"error"})}finally{m(V,!1)}}function Je(){m(X,!0)}function De(){m(X,!1)}async function Ue(o){const l=`Mock PDF content for invoice ${o.invoiceNumber||"Draft"}`;return new Blob([l],{type:"application/pdf"})}function Ye(o){return new Promise((l,f)=>{const c=new FileReader;c.onload=()=>{const v=c.result.split(",")[1];l(v)},c.onerror=f,c.readAsDataURL(o)})}function ze(o){switch(o){case"sent":return"#10b981";case"failed":return"#ef4444";case"pending":return"#f59e0b";default:return"#6b7280"}}lt();var we=Bt(),fe=oe(we),Ie=a(fe);ft(Ie,{title:"Invoice Details",$$slots:{actions:(o,l)=>{var f=Ae(),c=oe(f);{var _=v=>{var h=bt(),$=oe(h);ve($,{variant:"secondary",$$events:{click:()=>{var y;return pt(`/invoices/${(y=e(s))==null?void 0:y.id}/edit`)}},children:(y,T)=>{ne();var n=W("Edit Invoice");i(y,n)},$$slots:{default:!0}});var u=r($,2);ve(u,{variant:"secondary",$$events:{click:Oe},children:(y,T)=>{ne();var n=W("Export PDF");i(y,n)},$$slots:{default:!0}});var P=r(u,2);ve(P,{variant:"primary",$$events:{click:Je},children:(y,T)=>{ne();var n=W("Send Email");i(y,n)},$$slots:{default:!0}}),i(v,h)};S(c,v=>{e(s)&&v(_)})}i(o,f)}}});var Ee=r(Ie,2),Ke=a(Ee);{var Qe=o=>{ut(o,{message:"Loading invoice details..."})},We=(o,l)=>{{var f=_=>{var v=xt(),h=a(v),$=a(h);t(h),t(v),H(()=>d($,`Error: ${e(R)??""}`)),i(_,v)},c=(_,v)=>{{var h=u=>{var P=Tt(),y=a(P),T=a(y),n=a(T);t(T);var A=r(T,2),J=a(A),ue=a(J,!0);t(J),t(A),t(y);var Q=r(y,2),ce=r(a(Q),2),U=r(a(ce),2),ge=r(a(U));t(U);var je=r(U,2),Ve=r(a(je));t(je);var Pe=r(je,2),Xe=r(a(Pe));t(Pe),t(ce),t(Q);var Se=r(Q,2);{var Ze=p=>{var g=wt(),F=a(g);{var q=D=>{var x=$t(),k=r(a(x),2),M=a(k,!0);t(k),t(x),H(()=>d(M,e(s).notes)),i(D,x)};S(F,D=>{e(s).notes&&D(q)})}var w=r(F,2);{var I=D=>{var x=Dt(),k=r(a(x),2),M=a(k,!0);t(k),t(x),H(()=>d(M,e(s).paymentTerms)),i(D,x)};S(w,D=>{e(s).paymentTerms&&D(I)})}t(g),i(p,g)};S(Se,p=>{(e(s).notes||e(s).paymentTerms)&&p(Ze)})}var ke=r(Se,4);{var et=p=>{var g=Et(),F=a(g),q=r(a(F));Le(q,5,()=>e(s).invoiceLines,Te,(w,I)=>{var D=It(),x=a(D),k=a(x,!0);t(x);var M=r(x),ee=a(M,!0);t(M);var te=r(M),ae=a(te,!0);t(te);var se=r(te),re=a(se);t(se);var me=r(se),_e=a(me,!0);t(me),t(D),H((he,Y,z)=>{d(k,e(I).description),d(ee,e(I).quantity),d(ae,he),d(re,`${Y??""}%`),d(_e,z)},[()=>le(e(I).unitPrice),()=>(e(I).taxRate*100).toFixed(1),()=>le(e(I).total)],ie),i(w,D)}),t(q),t(F),t(g),i(p,g)},tt=p=>{var g=Pt();i(p,g)};S(ke,p=>{e(s).invoiceLines&&e(s).invoiceLines.length>0?p(et):p(tt,!1)})}var at=r(ke,2);{var st=p=>{var g=Lt(),F=r(a(g),2);Le(F,5,()=>e(de),Te,(q,w)=>{var I=kt(),D=a(I),x=a(D),k=a(x),M=a(k,!0);t(k);var ee=r(k,2),te=a(ee,!0);t(ee),t(x);var ae=r(x,2),se=a(ae,!0);t(ae);var re=r(ae,2),me=a(re,!0);t(re);var _e=r(re,2);{var he=Y=>{var z=St(),rt=a(z,!0);t(z),H(()=>d(rt,e(w).error)),i(Y,z)};S(_e,Y=>{e(w).error&&Y(he)})}t(D),t(I),H((Y,z)=>{d(M,e(w).to),Me(ee,`color: ${Y??""};`),d(te,e(w).status),d(se,e(w).subject),d(me,z)},[()=>ze(e(w).status),()=>new Date(e(w).sentAt).toLocaleString()],ie),i(q,I)}),t(F),t(g),i(p,g)};S(at,p=>{e(de).length>0&&p(st)})}t(P),H((p,g,F,q,w,I)=>{d(n,`Invoice Number: ${e(s).invoiceNumber||"Draft"}`),Me(J,`background-color: ${p??""}20; color: ${g??""};`),d(ue,F),d(ge,` ${q??""}`),d(Ve,` ${w??""}`),d(Xe,` ${I??""}`)},[()=>be(e(s).status).color,()=>be(e(s).status).color,()=>be(e(s).status).name,()=>e(s).issueDate?new Date(e(s).issueDate).toLocaleDateString():"N/A",()=>e(s).dueDate?new Date(e(s).dueDate).toLocaleDateString():"N/A",()=>le(e(s).invoiceLines.reduce((p,g)=>p+g.total,0))],ie),i(u,P)},$=u=>{var P=At();i(u,P)};S(_,u=>{e(s)?u(h):u($,!1)},v)}};S(o,_=>{e(R)?_(f):_(c,!1)},l)}};S(Ke,o=>{e(C)?o(Qe):o(We,!1)})}t(Ee),t(fe);var Ge=r(fe,2);jt(Ge,{title:"Send Invoice Email",get show(){return e(X)},set show(o){m(X,o)},$$events:{close:De},children:(o,l)=>{var f=Mt(),c=a(f),_=r(a(c),2);Fe(_),t(c);var v=r(c,2),h=r(a(v),2);Fe(h),t(v);var $=r(v,2),u=r(a($),2);nt(u),t($);var P=r($,2),y=r(a(P),4);{var T=n=>{var A=Ft(),J=a(A),ue=r(a(J));t(J);var Q=r(J,2),ce=r(a(Q));t(Q),ne(2),t(A),H(U=>{d(ue,` ${e(s).invoiceNumber??""}`),d(ce,` ${U??""}`)},[()=>le(e(s).invoiceLines.reduce((U,ge)=>U+ge.total,0))],ie),i(n,A)};S(y,n=>{e(s)&&n(T)})}t(P),t(f),ye(_,()=>e(Z),n=>m(Z,n)),ye(h,()=>e(pe),n=>m(pe,n)),ye(u,()=>e($e),n=>m($e,n)),i(o,f)},$$slots:{default:!0,footer:(o,l)=>{var f=Nt(),c=oe(f);ve(c,{variant:"secondary",get disabled(){return e(V)},$$events:{click:De},children:(h,$)=>{ne();var u=W("Cancel");i(h,u)},$$slots:{default:!0}});var _=r(c,2);const v=ie(()=>e(V)||!e(Z).trim());ve(_,{variant:"primary",get disabled(){return e(v)},$$events:{click:Re},children:(h,$)=>{var u=Ae(),P=oe(u);{var y=n=>{var A=W("Sending...");i(n,A)},T=n=>{var A=W("Send Email");i(n,A)};S(P,n=>{e(V)?n(y):n(T,!1)})}i(h,u)},$$slots:{default:!0}}),i(o,f)}},$$legacy:!0}),i(b,we),it(),L()}export{na as component};
