import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{s as t,i as l,r as n}from"../chunks/p3DoyA09.js";import{t as p,a as m}from"../chunks/B67foYpL.js";import{s as d}from"../chunks/D5jlwPKM.js";import{S as f}from"../chunks/DSgydMIZ.js";import{T as u}from"../chunks/RoTT3oWY.js";const v=!1,O=Object.freeze(Object.defineProperty({__proto__:null,prerender:v},Symbol.toStringTag,{value:"Module"}));var c=p('<div class="app-layout"><!> <!> <!></div>');function h(e,s){var o=c(),r=l(o);f(r,{});var a=t(r,2);d(a,s,"default",{},null);var i=t(a,2);u(i,{}),n(o),m(e,o)}export{h as component,O as universal};
