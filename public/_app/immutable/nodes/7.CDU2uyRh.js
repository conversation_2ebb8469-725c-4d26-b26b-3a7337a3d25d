import{r as He}from"../chunks/DGCjcLwA.js";import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as Ke,f as W,g as Le,ae as Re,i as y,j as t,m as p,k as e,s as v,t as K,n as re,l as T,o as U,r as _}from"../chunks/p3DoyA09.js";import{h as Ge,e as Be,s as V}from"../chunks/BUelSUke.js";import{i as D}from"../chunks/DwdToawP.js";import{t as A,a as o,b as E,e as ne}from"../chunks/B67foYpL.js";import{r as le}from"../chunks/DdRd56Yq.js";import{s as me}from"../chunks/D7jLSc-x.js";import{b as ue}from"../chunks/WI3NPOEW.js";import{p as We}from"../chunks/Bfc47y5P.js";import{i as Ve}from"../chunks/D3pqaimu.js";import{o as Je}from"../chunks/C_WNR8j8.js";import{g as ce}from"../chunks/CSyJhG7e.js";import{b as Xe}from"../chunks/Atsggda0.js";import{M as ke}from"../chunks/BWn8tY11.js";import{C as Ye}from"../chunks/BbevrtJW.js";import{a as L}from"../chunks/Ce-0qAhV.js";import{B as M}from"../chunks/6Zk3JFqZ.js";import{P as Ze}from"../chunks/CC9utfo3.js";import{L as er}from"../chunks/C8F602cz.js";import{G as rr}from"../chunks/DEPinlMt.js";import{u as tr}from"../chunks/CGKBDcrf.js";import{api as de,ApiError as xe}from"../chunks/C5jwvbV4.js";const ar=()=>{if(!localStorage.getItem("user"))throw He(302,"/login");return{}},Tr=Object.freeze(Object.defineProperty({__proto__:null,load:ar},Symbol.toStringTag,{value:"Module"}));var sr=A('<p class="error-message"> <!></p>'),or=A('<div class="empty-state"><h3>No customers found</h3> <p>Get started by adding your first customer.</p> <!></div>'),ir=A('<div class="grid-actions svelte-1yds5dg"><!> <!> <!></div>'),nr=A('<div class="customers-grid-container svelte-1yds5dg"><!></div>'),lr=A('<div class="error-message"> </div>'),mr=A('<div class="error-message"> </div>'),ur=A('<div class="error-message"> </div>'),cr=A('<form class="customer-form"><div class="form-group"><label for="customerName">Customer Name *</label> <input type="text" id="customerName" placeholder="Enter customer name"> <!></div> <div class="form-group"><label for="customerEmail">Email *</label> <input type="email" id="customerEmail" placeholder="Enter email address"> <!></div> <div class="form-group"><label for="customerPhone">Phone *</label> <input type="tel" id="customerPhone" placeholder="Enter phone number"> <!></div></form>'),dr=A("<!> <!>",1),vr=A('<div class="customers-page"><!> <main><!></main></div> <!> <!>',1);function Ur(ve,we){Ke(we,!1);let J=p([]),te=p(!0),j=p(null),R=p(!1),O=p(null),ae=p(!1),X=p(0),Se=[{key:"fullName",text:"Name",sortable:!0},{key:"email",text:"Email",sortable:!0},{key:"phone",text:"Phone",sortable:!0},{key:"actions",text:"Actions",sortable:!1}],$=p({key:"",direction:""}),Y=p([{displayName:"Search",queryKey:"query",currentQuery:""}]),fe=[10,25,50],k=p(fe[0]),b=p(1);Je(()=>{const r=tr.subscribe(i=>{i||ce("/login")});return q(e(b),e(k),{},e($)),r});async function q(r,i,c,C){t(te,!0),t(j,null);try{let a;const m=c.query;m&&m.trim()?a=await de.get(`/customers/search?query=${encodeURIComponent(m.trim())}`):a=await de.get("/Customers"),Array.isArray(a)?(t(J,a.map(s=>({id:s.id,fullName:s.name||"Unknown Customer",companyName:s.companyName||"",emails:s.emails||[{email:s.email||"",type:"Work",isPrimary:!0}],phones:s.phones||[{phone:s.phone||"",type:"Work",isPrimary:!0}],addresses:s.addresses||[],status:s.status||"Customer",notes:s.notes||[],checklists:s.checklists||[],communicationTimeline:s.communicationTimeline||[],createdAt:s.createdAt||new Date().toISOString(),updatedAt:s.updatedAt||new Date().toISOString(),name:s.name||"Unknown Customer",email:s.email||"",phone:s.phone||""}))),t(X,e(J).length)):(console.error("API response was not an array:",a),t(J,[]),t(X,0),t(j,"Received unexpected data format from the server."))}catch(a){a instanceof xe?(t(j,`Failed to fetch customers: ${a.message}`),console.error("API Error:",a.status,a.message)):(t(j,"An unexpected error occurred while fetching customers."),console.error("Fetch customers error:",a))}finally{t(te,!1)}}function Ee(r){let i="ascending";e($).key===r&&(i=e($).direction==="ascending"?"descending":""),t($,{key:i===""?"":r,direction:i}),q(e(b),e(k),z(),e($))}function Ae(r){t(Y,e(Y).map(i=>({...i,currentQuery:r[i.queryKey]||""}))),t(b,1),q(e(b),e(k),r,e($))}function z(){const r={};return e(Y).forEach(i=>{i.currentQuery&&i.currentQuery.trim()!==""&&(r[i.queryKey]=i.currentQuery)}),r}function Ne(r){t(k,r),t(b,1),q(e(b),e(k),z(),e($))}function De(r){t(b,r),q(e(b),e(k),z(),e($))}function qe(r){Xe.set({id:r,name:"",email:"",phone:""}),ce(`/customers/${r}`)}function Fe(r){t(O,r),t(R,!0)}async function Ie(){if(e(O))try{console.log("Simulating delete for:",e(O).fullName),L({message:`Customer "${e(O).fullName}" deleted successfully. (Simulated)`,type:"success"}),q(e(b),e(k),z(),e($))}catch(r){L({message:r.message||"Error deleting customer.",type:"error"})}finally{t(R,!1),t(O,null)}}let u=p({name:"",email:"",phone:""}),F=p(!1),l=p({}),N=p(!1);function pe(){t(ae,!0)}function se(){t(ae,!1),t(u,{name:"",email:"",phone:""}),t(l,{}),t(F,!1),t(N,!1)}function Me(){return t(l,{}),e(u).name.trim()||T(l,e(l).name="Customer name is required"),e(u).email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e(u).email)||T(l,e(l).email="Please enter a valid email address"):T(l,e(l).email="Email is required"),e(u).phone.trim()||T(l,e(l).phone="Phone number is required"),Object.keys(e(l)).length===0}async function ge(){if(t(F,!0),!!Me()){t(N,!0);try{const r={...e(u)},i=await de.post("/Customers",r);L({message:`Customer "${e(u).name}" created successfully!`,type:"success"}),q(e(b),e(k),z(),e($)),se(),ce(`/customers/${i.id}`)}catch(r){r instanceof xe?(L({message:`Failed to create customer: ${r.message}`,type:"error"}),console.error("API Error:",r.status,r.message)):(L({message:"An unexpected error occurred while creating the customer.",type:"error"}),console.error("Create customer error:",r))}finally{t(N,!1)}}}function Oe(r){r&&r.id&&(console.log("Edit customer:",r.id),L({message:"Edit functionality not yet implemented.",type:"info"}))}Ve();var he=vr();Ge(r=>{Re.title="Customers"});var oe=W(he),ye=y(oe);Ze(ye,{title:"Customers",$$slots:{actions:(r,i)=>{M(r,{variant:"primary",type:"button",$$events:{click:pe},children:(c,C)=>{U();var a=E("Add Customer");o(c,a)},$$slots:{default:!0}})}}});var _e=v(ye,2),Qe=y(_e);{var Te=r=>{er(r,{message:"Loading customers..."})},Ue=(r,i)=>{{var c=a=>{var m=sr(),s=y(m),x=v(s);M(x,{type:"button",$$events:{click:()=>q(e(b),e(k),z(),e($))},children:(g,d)=>{U();var w=E("Retry");o(g,w)},$$slots:{default:!0}}),_(m),K(()=>V(s,`${e(j)??""} `)),o(a,m)},C=(a,m)=>{{var s=g=>{var d=or(),w=v(y(d),4);M(w,{variant:"primary",$$events:{click:pe},children:(P,S)=>{U();var I=E("Add Customer");o(P,I)},$$slots:{default:!0}}),_(d),o(g,d)},x=g=>{var d=nr(),w=y(d);rr(w,{headers:Se,get dataRows(){return e(J)},emptyMessage:"No customers found.",get currentSort(){return e($)},onHeaderClick:Ee,get searchFields(){return e(Y)},itemsPerPageOptions:fe,get itemsPerPage(){return e(k)},get currentPage(){return e(b)},get totalItems(){return e(X)},onSearch:Ae,onItemsPerPageChange:Ne,onPageChange:De,$$slots:{cell:(P,S)=>{const I=re(()=>S.row),Q=re(()=>S.headerKey),Z=re(()=>S.value);var ee=ne(),ie=W(ee);{var n=h=>{var H=ir(),be=y(H);M(be,{variant:"tertiary",size:"small",$$events:{click:()=>qe(e(I).id)},children:(G,Pe)=>{U();var B=E("View");o(G,B)},$$slots:{default:!0}});var Ce=v(be,2);M(Ce,{variant:"tertiary",size:"small",$$events:{click:()=>Oe(e(I))},children:(G,Pe)=>{U();var B=E("Edit");o(G,B)},$$slots:{default:!0}});var ze=v(Ce,2);M(ze,{variant:"tertiary",size:"small",$$events:{click:()=>Fe(e(I))},children:(G,Pe)=>{U();var B=E("Delete");o(G,B)},$$slots:{default:!0}}),_(H),o(h,H)},f=h=>{var H=E();K(()=>V(H,e(Z))),o(h,H)};D(ie,h=>{e(Q)==="actions"?h(n):h(f,!1)})}o(P,ee)}}}),_(d),o(g,d)};D(a,g=>{e(X)===0?g(s):g(x,!1)},m)}};D(r,a=>{e(j)?a(c):a(C,!1)},i)}};D(Qe,r=>{e(te)?r(Te):r(Ue,!1)})}_(_e),_(oe);var $e=v(oe,2);ke($e,{title:"Confirm Delete",get show(){return e(R)},$$events:{close:()=>t(R,!1)},children:(r,i)=>{var c=ne(),C=W(c);{var a=m=>{Ye(m,{get itemName(){return e(O).fullName},$$events:{confirm:Ie,cancel:()=>t(R,!1)}})};D(C,m=>{e(O)&&m(a)})}o(r,c)},$$slots:{default:!0}});var je=v($e,2);ke(je,{title:"Add New Customer",get show(){return e(ae)},$$events:{close:se},children:(r,i)=>{var c=cr(),C=y(c),a=v(y(C),2);le(a);let m;var s=v(a,2);{var x=n=>{var f=lr(),h=y(f,!0);_(f),K(()=>V(h,e(l).name)),o(n,f)};D(s,n=>{e(F)&&e(l).name&&n(x)})}_(C);var g=v(C,2),d=v(y(g),2);le(d);let w;var P=v(d,2);{var S=n=>{var f=mr(),h=y(f,!0);_(f),K(()=>V(h,e(l).email)),o(n,f)};D(P,n=>{e(F)&&e(l).email&&n(S)})}_(g);var I=v(g,2),Q=v(y(I),2);le(Q);let Z;var ee=v(Q,2);{var ie=n=>{var f=ur(),h=y(f,!0);_(f),K(()=>V(h,e(l).phone)),o(n,f)};D(ee,n=>{e(F)&&e(l).phone&&n(ie)})}_(I),_(c),K((n,f,h)=>{a.disabled=e(N),m=me(a,1,"",null,m,n),d.disabled=e(N),w=me(d,1,"",null,w,f),Q.disabled=e(N),Z=me(Q,1,"",null,Z,h)},[()=>({error:e(F)&&e(l).name}),()=>({error:e(F)&&e(l).email}),()=>({error:e(F)&&e(l).phone})],re),ue(a,()=>e(u).name,n=>T(u,e(u).name=n)),ue(d,()=>e(u).email,n=>T(u,e(u).email=n)),ue(Q,()=>e(u).phone,n=>T(u,e(u).phone=n)),Be("submit",c,We(ge)),o(r,c)},$$slots:{default:!0,footer:(r,i)=>{var c=dr(),C=W(c);M(C,{type:"button",variant:"secondary",get disabled(){return e(N)},$$events:{click:se},children:(m,s)=>{U();var x=E("Cancel");o(m,x)},$$slots:{default:!0}});var a=v(C,2);M(a,{type:"button",variant:"primary",get disabled(){return e(N)},$$events:{click:ge},children:(m,s)=>{var x=ne(),g=W(x);{var d=P=>{var S=E("Creating...");o(P,S)},w=P=>{var S=E("Create Customer");o(P,S)};D(g,P=>{e(N)?P(d):P(w,!1)})}o(m,x)},$$slots:{default:!0}}),o(r,c)}}}),o(ve,he),Le()}export{Ur as component,Tr as universal};
