import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as z,t as _,k as r,g as B,s as l,ae as C,i,m as u,r as o,j as s}from"../chunks/p3DoyA09.js";import{h as F,e as G,s as j}from"../chunks/BUelSUke.js";import{i as H}from"../chunks/DwdToawP.js";import{t as P,a as x}from"../chunks/B67foYpL.js";import{r as L}from"../chunks/DdRd56Yq.js";import{b as q}from"../chunks/WI3NPOEW.js";import{p as I}from"../chunks/Bfc47y5P.js";import{i as J}from"../chunks/D3pqaimu.js";import{o as K}from"../chunks/C_WNR8j8.js";import{u as N,l as O}from"../chunks/CGKBDcrf.js";import{g as E}from"../chunks/CSyJhG7e.js";var Q=P('<div class="error-message svelte-1rhgjwy"> </div>'),R=P('<div class="login-container svelte-1rhgjwy"><div class="login-card svelte-1rhgjwy"><h1 class="svelte-1rhgjwy">Login</h1> <form><!> <div class="form-group svelte-1rhgjwy"><label for="email" class="svelte-1rhgjwy">Email</label> <input type="email" id="email" placeholder="Enter your email" required class="svelte-1rhgjwy"></div> <div class="form-group svelte-1rhgjwy"><label for="password" class="svelte-1rhgjwy">Password</label> <input type="password" id="password" placeholder="Enter your password" required class="svelte-1rhgjwy"></div> <button type="submit" class="login-button svelte-1rhgjwy"> </button></form></div></div>');function oe($,k){z(k,!1);let d=u(""),m=u(""),t=u(""),a=u(!1);K(()=>N.subscribe(n=>{n&&E("/dashboard")}));async function A(){if(s(t,""),s(a,!0),!r(d)){s(t,"Email is required"),s(a,!1);return}if(!r(m)){s(t,"Password is required"),s(a,!1);return}try{const e=await O(r(d),r(m));e.success?E("/dashboard"):s(t,e.error||"Login failed")}catch{s(t,"An unexpected error occurred. Please try again.")}finally{s(a,!1)}}J();var v=R();F(e=>{C.title="Login"});var y=i(v),p=l(i(y),2),w=i(p);{var D=e=>{var n=Q(),S=i(n,!0);o(n),_(()=>j(S,r(t))),x(e,n)};H(w,e=>{r(t)&&e(D)})}var c=l(w,2),f=l(i(c),2);L(f),o(c);var g=l(c,2),b=l(i(g),2);L(b),o(g);var h=l(g,2),M=i(h,!0);o(h),o(p),o(y),o(v),_(()=>{f.disabled=r(a),b.disabled=r(a),h.disabled=r(a),j(M,r(a)?"Logging in...":"Login")}),q(f,()=>r(d),e=>s(d,e)),q(b,()=>r(m),e=>s(m,e)),G("submit",p,I(A)),x($,v),B()}export{oe as component};
