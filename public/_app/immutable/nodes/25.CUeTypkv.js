import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as De,u as ae,v as xe,g as Te,t as b,f as La,s,k as a,n as K,i as t,j as z,m as q,ae as we,o as M,r as e}from"../chunks/p3DoyA09.js";import{h as Se,s as l}from"../chunks/BUelSUke.js";import{i as p}from"../chunks/DwdToawP.js";import{e as Pa,i as Ca}from"../chunks/DEqeA9IH.js";import{t as n,a as r,b as O,e as Ae}from"../chunks/B67foYpL.js";import{s as Ma}from"../chunks/D7jLSc-x.js";import{d as Je}from"../chunks/DdRd56Yq.js";import{i as Pe}from"../chunks/D3pqaimu.js";import{s as Ce,a as Ie}from"../chunks/qYb16FSw.js";import{o as Ne}from"../chunks/C_WNR8j8.js";import{p as Ee}from"../chunks/C3hpKoCs.js";import{g as ga}from"../chunks/CSyJhG7e.js";import{a as Ia}from"../chunks/Ce-0qAhV.js";import{B as ua}from"../chunks/6Zk3JFqZ.js";import{P as Re}from"../chunks/CC9utfo3.js";import{L as ee}from"../chunks/C8F602cz.js";import{T as Fe}from"../chunks/BIVs0YJ0.js";import{u as Le}from"../chunks/CGKBDcrf.js";import{b as Me,u as Be,d as He}from"../chunks/Ca7pnwKO.js";import{r as Ue}from"../chunks/D9cKHHet.js";import{g as qe}from"../chunks/BXh2naGf.js";import{f as re}from"../chunks/Chsk6cZE.js";var We=n("<!> <!> <!>",1),Ve=n("<!> <!>",1),Ge=n('<div class="error-container svelte-t83zak"><p class="error-message svelte-t83zak"> </p> <!></div>'),Qe=n('<p class="department svelte-t83zak"> </p>'),Xe=n('<div class="detail-row svelte-t83zak"><span class="label svelte-t83zak">Wage:</span> <span class="value svelte-t83zak"> </span></div>'),Ye=n('<span class="skill-badge svelte-t83zak"> </span>'),Ze=n('<div class="skills-section svelte-t83zak"><h4 class="svelte-t83zak">Skills</h4> <div class="skills-list svelte-t83zak"></div></div>'),Ke=n('<div class="notes-section svelte-t83zak"><h4 class="svelte-t83zak">Notes</h4> <p class="svelte-t83zak"> </p></div>'),Oe=n('<div class="details-section svelte-t83zak"><h2 class="svelte-t83zak">Staff Information</h2> <div class="staff-overview svelte-t83zak"><div class="overview-card svelte-t83zak"><div class="staff-header svelte-t83zak"><div class="staff-info svelte-t83zak"><h3 class="svelte-t83zak"> </h3> <p class="position svelte-t83zak"> </p> <!> <span> </span></div></div> <div class="contact-details svelte-t83zak"><div class="detail-row svelte-t83zak"><span class="label svelte-t83zak">Email:</span> <span class="value svelte-t83zak"> </span></div> <div class="detail-row svelte-t83zak"><span class="label svelte-t83zak">Phone:</span> <span class="value svelte-t83zak"> </span></div> <div class="detail-row svelte-t83zak"><span class="label svelte-t83zak">Hire Date:</span> <span class="value svelte-t83zak"> </span></div> <!></div> <!> <!></div></div></div>'),je=n('<div class="empty-state svelte-t83zak"><p class="svelte-t83zak">No upcoming appointments scheduled.</p></div>'),at=n('<p class="address svelte-t83zak"> </p>'),et=n('<div class="event-card svelte-t83zak"><div class="event-time svelte-t83zak"><div class="date svelte-t83zak"> </div> <div class="time svelte-t83zak"> </div></div> <div class="event-details svelte-t83zak"><h4 class="svelte-t83zak"> </h4> <p class="svelte-t83zak"> </p> <!></div> <div class="event-status svelte-t83zak"><span> </span> <span class="status-badge svelte-t83zak"> </span></div></div>'),tt=n('<div class="events-list svelte-t83zak"></div>'),st=n('<div class="empty-state svelte-t83zak"><p class="svelte-t83zak">No recent appointments found.</p></div>'),vt=n('<div class="event-card past svelte-t83zak"><div class="event-time svelte-t83zak"><div class="date svelte-t83zak"> </div> <div class="time svelte-t83zak"> </div></div> <div class="event-details svelte-t83zak"><h4 class="svelte-t83zak"> </h4> <p class="svelte-t83zak"> </p></div> <div class="event-status svelte-t83zak"><span class="status-badge completed svelte-t83zak"> </span></div></div>'),rt=n('<div class="events-list svelte-t83zak"></div>'),lt=n('<div class="schedule-overview svelte-t83zak"><div class="upcoming-section svelte-t83zak"><h3 class="svelte-t83zak"> </h3> <!></div> <div class="recent-section svelte-t83zak"><h3 class="svelte-t83zak">Recent Appointments</h3> <!></div></div>'),it=n('<div class="schedule-section svelte-t83zak"><h2 class="svelte-t83zak">Schedule & Calendar</h2> <!></div>'),dt=n('<div class="empty-state svelte-t83zak"><p class="svelte-t83zak">No jobs assigned to this staff member.</p> <!></div>'),ot=n('<small class="svelte-t83zak"> </small>'),ct=n('<div class="date-info svelte-t83zak"><strong class="svelte-t83zak"> </strong> <small class="svelte-t83zak"> </small></div>'),nt=n('<span class="text-muted svelte-t83zak">Not scheduled</span>'),ft=n('<div class="table-row svelte-t83zak"><div class="table-cell svelte-t83zak"><div class="job-info svelte-t83zak"><strong class="svelte-t83zak"> </strong> <!></div></div> <div class="table-cell svelte-t83zak"> </div> <div class="table-cell svelte-t83zak"><!></div> <div class="table-cell svelte-t83zak"><span class="status-badge svelte-t83zak"> </span></div> <div class="table-cell svelte-t83zak"><!></div> <div class="table-cell svelte-t83zak"><div class="action-buttons svelte-t83zak"><!> <!></div></div></div>'),_t=n('<div class="data-table svelte-t83zak"><div class="table-header svelte-t83zak"><div class="header-cell svelte-t83zak">Job</div> <div class="header-cell svelte-t83zak">Customer</div> <div class="header-cell svelte-t83zak">Scheduled</div> <div class="header-cell svelte-t83zak">Status</div> <div class="header-cell svelte-t83zak">Duration</div> <div class="header-cell svelte-t83zak">Actions</div></div> <!></div>'),ut=n('<div class="jobs-section svelte-t83zak"><div class="section-header svelte-t83zak"><h2 class="svelte-t83zak">Assigned Jobs</h2> <!></div> <!></div>'),mt=n('<div class="time-range svelte-t83zak"> </div>'),pt=n('<div><div class="day-header svelte-t83zak"><h4 class="svelte-t83zak"> </h4> <span class="availability-status svelte-t83zak"> </span></div> <!></div>'),kt=n('<p class="svelte-t83zak"><strong class="svelte-t83zak">Reason:</strong> </p>'),zt=n('<div><div class="time-off-dates svelte-t83zak"><strong class="svelte-t83zak"> </strong></div> <div class="time-off-details svelte-t83zak"><p class="svelte-t83zak"><strong class="svelte-t83zak">Type:</strong> </p> <!></div> <div class="time-off-status svelte-t83zak"><span> </span></div></div>'),gt=n('<div class="time-off-section svelte-t83zak"><h3 class="svelte-t83zak">Time Off Requests</h3> <div class="time-off-list svelte-t83zak"></div></div>'),ht=n('<div class="availability-grid svelte-t83zak"></div> <!>',1),bt=n('<div class="availability-section svelte-t83zak"><h2 class="svelte-t83zak">Availability Schedule</h2> <!></div>'),yt=n('<div class="metrics-grid svelte-t83zak"><div class="metric-card svelte-t83zak"><div class="metric-value svelte-t83zak"> </div> <div class="metric-label svelte-t83zak">Total Jobs</div></div> <div class="metric-card svelte-t83zak"><div class="metric-value svelte-t83zak"> </div> <div class="metric-label svelte-t83zak">Completed Jobs</div></div> <div class="metric-card svelte-t83zak"><div class="metric-value svelte-t83zak"> </div> <div class="metric-label svelte-t83zak">Upcoming Jobs</div></div> <div class="metric-card svelte-t83zak"><div class="metric-value svelte-t83zak"> </div> <div class="metric-label svelte-t83zak">Total Hours</div></div> <div class="metric-card svelte-t83zak"><div class="metric-value svelte-t83zak"> </div> <div class="metric-label svelte-t83zak">Total Revenue</div></div> <div class="metric-card svelte-t83zak"><div class="metric-value svelte-t83zak"> </div> <div class="metric-label svelte-t83zak">Avg Rating</div></div></div> <div class="performance-charts svelte-t83zak"><div class="chart-placeholder svelte-t83zak"><h3 class="svelte-t83zak">Job Completion Rate</h3> <div class="completion-rate svelte-t83zak"><div class="rate-circle svelte-t83zak"><span class="rate-percentage svelte-t83zak"> </span></div> <p class="svelte-t83zak">Jobs completed successfully</p></div></div></div>',1),$t=n('<div class="performance-section svelte-t83zak"><h2 class="svelte-t83zak">Performance Metrics</h2> <!></div>'),Dt=n('<div class="staff-details svelte-t83zak"><!> <div class="tab-content svelte-t83zak"><!></div></div>'),xt=n("<p>Staff member not found.</p>"),Tt=n('<div class="container"><!> <main><!></main></div>');function Kt(le,ie){De(ie,!1);const[de,oe]=Ce(),ce=()=>Ie(Ee,"$page",de),j=q(),Ba=q(),Qa=q();let i=q(null),Ha=q(!0),Ua=q(!1),qa=q(!1),xa=q(null),Q=q(),ha=q("details"),la=q([]),Na=q([]),Xa=q(!1),Ya=q(!1);const ne=[{id:"details",label:"Details"},{id:"schedule",label:"Schedule"},{id:"jobs",label:"Jobs"},{id:"availability",label:"Availability"},{id:"performance",label:"Performance"}];Ne(()=>{const v=Le.subscribe(I=>{I||ga("/login")});return z(Q,ce().params.id),a(Q)?te():(z(xa,"No staff ID provided"),z(Ha,!1)),v});async function te(){z(Ha,!0),z(xa,null);try{z(i,await Me(a(Q))),a(i)?await fe():z(xa,"Staff member not found")}catch(v){z(xa,"Failed to fetch staff details"),console.error("Fetch staff error:",v)}finally{z(Ha,!1)}}async function fe(){await Promise.all([_e(),ue()])}async function _e(){if(a(Q)){z(Xa,!0);try{z(la,await Ue(a(Q)))}catch(v){console.error("Error loading staff jobs:",v),Ia({message:"Failed to load staff jobs",type:"error"})}finally{z(Xa,!1)}}}async function ue(){if(a(Q)){z(Ya,!0);try{const v=new Date(Date.now()-2592e6).toISOString(),I=new Date(Date.now()+90*24*60*60*1e3).toISOString(),ia=await qe(v,I);z(Na,ia.filter(da=>da.assignedStaff.some(sa=>sa.staffId===a(Q))))}catch(v){console.error("Error loading staff events:",v),Ia({message:"Failed to load staff events",type:"error"})}finally{z(Ya,!1)}}}async function me(){if(a(i)){z(Ua,!0);try{const v=await Be(a(Q),{isActive:!a(i).isActive});z(i,v),Ia({message:`Staff member ${a(i).isActive?"activated":"deactivated"} successfully`,type:"success"})}catch(v){console.error("Error updating staff status:",v),Ia({message:"Failed to update staff status",type:"error"})}finally{z(Ua,!1)}}}async function pe(){if(a(i)&&confirm(`Are you sure you want to delete staff member "${a(i).fullName}"? This action cannot be undone.`)){z(qa,!0);try{await He(a(Q)),Ia({message:"Staff member deleted successfully",type:"success"}),ga("/staff")}catch(v){console.error("Error deleting staff:",v),Ia({message:"Failed to delete staff member",type:"error"})}finally{z(qa,!1)}}}function ke(){ga("/staff")}function Ta(v){return new Date(v).toLocaleDateString()}function Ea(v){return new Date(v).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}function ze(v){return{monday:"Monday",tuesday:"Tuesday",wednesday:"Wednesday",thursday:"Thursday",friday:"Friday",saturday:"Saturday",sunday:"Sunday"}[v]||v}ae(()=>(a(i),a(la)),()=>{z(j,a(i)?{totalJobs:a(la).length,completedJobs:a(la).filter(v=>v.status.isCompleted).length,upcomingJobs:a(la).filter(v=>!v.status.isCompleted&&v.scheduledDateTime&&new Date(v.scheduledDateTime)>new Date).length,totalHours:a(la).reduce((v,I)=>v+(I.actualDuration||I.estimatedDuration||0),0)/60,totalRevenue:a(la).reduce((v,I)=>{var ia,da;return v+(((ia=I.actualCost)==null?void 0:ia.totalCost)||((da=I.estimatedCost)==null?void 0:da.totalCost)||0)},0),averageJobRating:4.5}:null)}),ae(()=>a(Na),()=>{z(Ba,a(Na).filter(v=>new Date(v.startDateTime)>new Date).sort((v,I)=>new Date(v.startDateTime).getTime()-new Date(I.startDateTime).getTime()))}),ae(()=>a(Na),()=>{z(Qa,a(Na).filter(v=>new Date(v.endDateTime)<new Date).sort((v,I)=>new Date(I.startDateTime).getTime()-new Date(v.startDateTime).getTime()))}),xe(),Pe();var Za=Tt();Se(v=>{b(()=>we.title=a(i)?`${a(i).fullName} - Staff Details`:"Staff Details")});var se=t(Za);const ge=K(()=>a(i)?`${a(i).fullName}`:"Staff Details");Re(se,{get title(){return a(ge)},$$slots:{actions:(v,I)=>{var ia=Ve(),da=La(ia);ua(da,{variant:"secondary",type:"button",$$events:{click:ke},children:(aa,ya)=>{M();var W=O("Back to Staff");r(aa,W)},$$slots:{default:!0}});var sa=s(da,2);{var ba=aa=>{var ya=We(),W=La(ya);const oa=K(()=>a(i).isActive?"danger":"primary");ua(W,{get variant(){return a(oa)},type:"button",get disabled(){return a(Ua)},$$events:{click:me},children:($a,Wa)=>{M();var ca=O();b(()=>l(ca,a(Ua)?"Updating...":a(i).isActive?"Deactivate":"Activate")),r($a,ca)},$$slots:{default:!0}});var wa=s(W,2);ua(wa,{variant:"primary",type:"button",$$events:{click:()=>ga(`/staff/${a(Q)}/edit`)},children:($a,Wa)=>{M();var ca=O("Edit Staff");r($a,ca)},$$slots:{default:!0}});var Sa=s(wa,2);ua(Sa,{variant:"danger",type:"button",get disabled(){return a(qa)},$$events:{click:pe},children:($a,Wa)=>{M();var ca=O();b(()=>l(ca,a(qa)?"Deleting...":"Delete Staff")),r($a,ca)},$$slots:{default:!0}}),r(aa,ya)};p(sa,aa=>{a(i)&&aa(ba)})}r(v,ia)}}});var ve=s(se,2),he=t(ve);{var be=v=>{ee(v,{message:"Loading staff details..."})},ye=(v,I)=>{{var ia=sa=>{var ba=Ge(),aa=t(ba),ya=t(aa,!0);e(aa);var W=s(aa,2);ua(W,{type:"button",$$events:{click:te},children:(oa,wa)=>{M();var Sa=O("Retry");r(oa,Sa)},$$slots:{default:!0}}),e(ba),b(()=>l(ya,a(xa))),r(sa,ba)},da=(sa,ba)=>{{var aa=W=>{var oa=Dt(),wa=t(oa);Fe(wa,{tabs:ne,get activeTab(){return a(ha)},set activeTab(ma){z(ha,ma)},$$legacy:!0});var Sa=s(wa,2),$a=t(Sa);{var Wa=ma=>{var Ra=Oe(),Va=s(t(Ra),2),Ga=t(Va),va=t(Ga),pa=t(va),Da=t(pa),Fa=t(Da,!0);e(Da);var X=s(Da,2),B=t(X,!0);e(X);var Y=s(X,2);{var na=d=>{var c=Qe(),k=t(c,!0);e(c),b(()=>l(k,a(i).department)),r(d,c)};p(Y,d=>{a(i).department&&d(na)})}var R=s(Y,2);let Z;var fa=t(R,!0);e(R),e(pa),e(va);var x=s(va,2),F=t(x),H=s(t(F),2),ea=t(H,!0);e(H),e(F);var J=s(F,2),N=s(t(J),2),m=t(N,!0);e(N),e(J);var _=s(J,2),o=s(t(_),2),f=t(o,!0);e(o),e(_);var u=s(_,2);{var y=d=>{var c=Xe(),k=s(t(c),2),g=t(k);e(k),e(c),b(L=>l(g,`${L??""}
                          ${a(i).wageInfo.type==="Hourly"?"/hour":a(i).wageInfo.type==="Salary"?"/year":"/job"}`),[()=>re(a(i).wageInfo.rate)],K),r(d,c)};p(u,d=>{a(i).wageInfo&&d(y)})}e(x);var D=s(x,2);{var w=d=>{var c=Ze(),k=s(t(c),2);Pa(k,5,()=>a(i).skills,Ca,(g,L)=>{var E=Ye(),S=t(E,!0);e(E),b(()=>l(S,a(L))),r(g,E)}),e(k),e(c),r(d,c)};p(D,d=>{a(i).skills&&a(i).skills.length>0&&d(w)})}var T=s(D,2);{var U=d=>{var c=Ke(),k=s(t(c),2),g=t(k,!0);e(k),e(c),b(()=>l(g,a(i).notes)),r(d,c)};p(T,d=>{a(i).notes&&d(U)})}e(Ga),e(Va),e(Ra),b((d,c)=>{l(Fa,a(i).fullName),l(B,a(i).position),Z=Ma(R,1,"status-badge svelte-t83zak",null,Z,d),l(fa,a(i).isActive?"Active":"Inactive"),l(ea,a(i).email),l(m,a(i).phone),l(f,c)},[()=>({active:a(i).isActive,inactive:!a(i).isActive}),()=>Ta(a(i).hireDate)],K),r(ma,Ra)},ca=(ma,Ra)=>{{var Va=va=>{var pa=it(),Da=s(t(pa),2);{var Fa=B=>{ee(B,{message:"Loading schedule..."})},X=B=>{var Y=lt(),na=t(Y),R=t(na),Z=t(R);e(R);var fa=s(R,2);{var x=m=>{var _=je();r(m,_)},F=m=>{var _=tt();Pa(_,5,()=>a(Ba).slice(0,10),Ca,(o,f)=>{var u=et(),y=t(u),D=t(y),w=t(D,!0);e(D);var T=s(D,2),U=t(T);e(T),e(y);var d=s(y,2),c=t(d),k=t(c,!0);e(c);var g=s(c,2),L=t(g,!0);e(g);var E=s(g,2);{var S=ra=>{var ta=at(),za=t(ta,!0);e(ta),b(()=>l(za,a(f).jobAddress)),r(ra,ta)};p(E,ra=>{a(f).jobAddress&&ra(S)})}e(d);var A=s(d,2),$=t(A),P=t($,!0);e($);var V=s($,2),ka=t(V,!0);e(V),e(A),e(u),b((ra,ta,za,Aa)=>{l(w,ra),l(U,`${ta??""} - ${za??""}`),l(k,a(f).title),l(L,a(f).customerName),Ma($,1,`priority-badge ${Aa??""}`,"svelte-t83zak"),l(P,a(f).priority),l(ka,a(f).status)},[()=>Ta(a(f).startDateTime),()=>Ea(a(f).startDateTime),()=>Ea(a(f).endDateTime),()=>a(f).priority.toLowerCase()],K),r(o,u)}),e(_),r(m,_)};p(fa,m=>{a(Ba).length===0?m(x):m(F,!1)})}e(na);var H=s(na,2),ea=s(t(H),2);{var J=m=>{var _=st();r(m,_)},N=m=>{var _=rt();Pa(_,5,()=>a(Qa).slice(0,5),Ca,(o,f)=>{var u=vt(),y=t(u),D=t(y),w=t(D,!0);e(D);var T=s(D,2),U=t(T);e(T),e(y);var d=s(y,2),c=t(d),k=t(c,!0);e(c);var g=s(c,2),L=t(g,!0);e(g),e(d);var E=s(d,2),S=t(E),A=t(S,!0);e(S),e(E),e(u),b(($,P,V)=>{l(w,$),l(U,`${P??""} - ${V??""}`),l(k,a(f).title),l(L,a(f).customerName),l(A,a(f).status)},[()=>Ta(a(f).startDateTime),()=>Ea(a(f).startDateTime),()=>Ea(a(f).endDateTime)],K),r(o,u)}),e(_),r(m,_)};p(ea,m=>{a(Qa).length===0?m(J):m(N,!1)})}e(H),e(Y),b(()=>l(Z,`Upcoming Appointments (${a(Ba).length??""})`)),r(B,Y)};p(Da,B=>{a(Ya)?B(Fa):B(X,!1)})}e(pa),r(va,pa)},Ga=(va,pa)=>{{var Da=X=>{var B=ut(),Y=t(B),na=s(t(Y),2);ua(na,{variant:"primary",size:"small",$$events:{click:()=>ga(`/calendar?staffId=${a(Q)}`)},children:(x,F)=>{M();var H=O("Schedule New Job");r(x,H)},$$slots:{default:!0}}),e(Y);var R=s(Y,2);{var Z=x=>{ee(x,{message:"Loading jobs..."})},fa=(x,F)=>{{var H=J=>{var N=dt(),m=s(t(N),2);ua(m,{variant:"primary",$$events:{click:()=>ga(`/calendar?staffId=${a(Q)}`)},children:(_,o)=>{M();var f=O("Assign First Job");r(_,f)},$$slots:{default:!0}}),e(N),r(J,N)},ea=J=>{var N=_t(),m=s(t(N),2);Pa(m,1,()=>a(la).sort((_,o)=>_.scheduledDateTime?o.scheduledDateTime?new Date(o.scheduledDateTime).getTime()-new Date(_.scheduledDateTime).getTime():-1:1),Ca,(_,o)=>{var f=ft(),u=t(f),y=t(u),D=t(y),w=t(D,!0);e(D);var T=s(D,2);{var U=h=>{var C=ot(),G=t(C,!0);e(C),b(()=>l(G,a(o).description)),r(h,C)};p(T,h=>{a(o).description&&h(U)})}e(y),e(u);var d=s(u,2),c=t(d,!0);e(d);var k=s(d,2),g=t(k);{var L=h=>{var C=ct(),G=t(C),Oa=t(G,!0);e(G);var _a=s(G,2),Ja=t(_a,!0);e(_a),e(C),b((ja,$e)=>{l(Oa,ja),l(Ja,$e)},[()=>Ta(a(o).scheduledDateTime),()=>Ea(a(o).scheduledDateTime)],K),r(h,C)},E=h=>{var C=nt();r(h,C)};p(g,h=>{a(o).scheduledDateTime?h(L):h(E,!1)})}e(k);var S=s(k,2),A=t(S),$=t(A,!0);e(A),e(S);var P=s(S,2),V=t(P);{var ka=h=>{var C=O();b(G=>l(C,`${G??""}h ${a(o).actualDuration%60}m`),[()=>Math.floor(a(o).actualDuration/60)],K),r(h,C)},ra=(h,C)=>{{var G=_a=>{var Ja=O();b(ja=>l(Ja,`~${ja??""}h ${a(o).estimatedDuration%60}m`),[()=>Math.floor(a(o).estimatedDuration/60)],K),r(_a,Ja)},Oa=_a=>{var Ja=O("-");r(_a,Ja)};p(h,_a=>{a(o).estimatedDuration?_a(G):_a(Oa,!1)},C)}};p(V,h=>{a(o).actualDuration?h(ka):h(ra,!1)})}e(P);var ta=s(P,2),za=t(ta),Aa=t(za);ua(Aa,{variant:"tertiary",size:"small",$$events:{click:()=>ga(`/jobs/${a(o).id}`)},children:(h,C)=>{M();var G=O("View");r(h,G)},$$slots:{default:!0}});var Ka=s(Aa,2);ua(Ka,{variant:"secondary",size:"small",$$events:{click:()=>ga(`/calendar?jobId=${a(o).id}`)},children:(h,C)=>{M();var G=O("Schedule");r(h,G)},$$slots:{default:!0}}),e(za),e(ta),e(f),b(()=>{l(w,a(o).title),l(c,a(o).customerName||"Unknown Customer"),Je(A,`background-color: ${a(o).status.color??""}20; color: ${a(o).status.color??""};`),l($,a(o).status.name)}),r(_,f)}),e(N),r(J,N)};p(x,J=>{a(la).length===0?J(H):J(ea,!1)},F)}};p(R,x=>{a(Xa)?x(Z):x(fa,!1)})}e(B),r(X,B)},Fa=(X,B)=>{{var Y=R=>{var Z=bt(),fa=s(t(Z),2);{var x=F=>{var H=ht(),ea=La(H);Pa(ea,5,()=>Object.entries(a(i).availability),Ca,(m,_)=>{let o=()=>a(_)[0],f=()=>a(_)[1];var u=Ae(),y=La(u);{var D=w=>{var T=pt();let U;var d=t(T),c=t(d),k=t(c,!0);e(c);var g=s(c,2),L=t(g,!0);e(g),e(d);var E=s(d,2);{var S=A=>{var $=mt(),P=t($);e($),b(()=>l(P,`${f().startTime??""} - ${f().endTime??""}`)),r(A,$)};p(E,A=>{f().isAvailable&&A(S)})}e(T),b((A,$)=>{U=Ma(T,1,"day-availability svelte-t83zak",null,U,A),l(k,$),l(L,f().isAvailable?"Available":"Unavailable")},[()=>({available:f().isAvailable,unavailable:!f().isAvailable}),()=>ze(o())],K),r(w,T)};p(y,w=>{o()!=="timeOff"&&w(D)})}r(m,u)}),e(ea);var J=s(ea,2);{var N=m=>{var _=gt(),o=s(t(_),2);Pa(o,5,()=>a(i).availability.timeOff,Ca,(f,u)=>{var y=zt();let D;var w=t(y),T=t(w),U=t(T);e(T),e(w);var d=s(w,2),c=t(d),k=s(t(c));e(c);var g=s(c,2);{var L=$=>{var P=kt(),V=s(t(P));e(P),b(()=>l(V,` ${a(u).reason??""}`)),r($,P)};p(g,$=>{a(u).reason&&$(L)})}e(d);var E=s(d,2),S=t(E),A=t(S,!0);e(S),e(E),e(y),b(($,P,V,ka)=>{D=Ma(y,1,"time-off-card svelte-t83zak",null,D,$),l(U,`${P??""} - ${V??""}`),l(k,` ${a(u).type??""}`),Ma(S,1,`status-badge ${ka??""}`,"svelte-t83zak"),l(A,a(u).status)},[()=>({approved:a(u).status==="Approved",pending:a(u).status==="Pending",rejected:a(u).status==="Rejected"}),()=>Ta(a(u).startDate),()=>Ta(a(u).endDate),()=>a(u).status.toLowerCase()],K),r(f,y)}),e(o),e(_),r(m,_)};p(J,m=>{a(i).availability.timeOff&&a(i).availability.timeOff.length>0&&m(N)})}r(F,H)};p(fa,F=>{a(i).availability&&F(x)})}e(Z),r(R,Z)},na=(R,Z)=>{{var fa=x=>{var F=$t(),H=s(t(F),2);{var ea=J=>{var N=yt(),m=La(N),_=t(m),o=t(_),f=t(o,!0);e(o),M(2),e(_);var u=s(_,2),y=t(u),D=t(y,!0);e(y),M(2),e(u);var w=s(u,2),T=t(w),U=t(T,!0);e(T),M(2),e(w);var d=s(w,2),c=t(d),k=t(c,!0);e(c),M(2),e(d);var g=s(d,2),L=t(g),E=t(L,!0);e(L),M(2),e(g);var S=s(g,2),A=t(S),$=t(A,!0);e(A),M(2),e(S),e(m);var P=s(m,2),V=t(P),ka=s(t(V),2),ra=t(ka),ta=t(ra),za=t(ta);e(ta),e(ra),M(2),e(ka),e(V),e(P),b((Aa,Ka,h,C)=>{l(f,a(j).totalJobs),l(D,a(j).completedJobs),l(U,a(j).upcomingJobs),l(k,Aa),l(E,Ka),l($,h),l(za,`${C??""}%`)},[()=>Math.round(a(j).totalHours),()=>re(a(j).totalRevenue),()=>a(j).averageJobRating.toFixed(1),()=>a(j).totalJobs>0?Math.round(a(j).completedJobs/a(j).totalJobs*100):0],K),r(J,N)};p(H,J=>{a(j)&&J(ea)})}e(F),r(x,F)};p(R,x=>{a(ha)==="performance"&&x(fa)},Z)}};p(X,R=>{a(ha)==="availability"?R(Y):R(na,!1)},B)}};p(va,X=>{a(ha)==="jobs"?X(Da):X(Fa,!1)},pa)}};p(ma,va=>{a(ha)==="schedule"?va(Va):va(Ga,!1)},Ra)}};p($a,ma=>{a(ha)==="details"?ma(Wa):ma(ca,!1)})}e(Sa),e(oa),r(W,oa)},ya=W=>{var oa=xt();r(W,oa)};p(sa,W=>{a(i)?W(aa):W(ya,!1)},ba)}};p(v,sa=>{a(xa)?sa(ia):sa(da,!1)},I)}};p(he,v=>{a(Ha)?v(be):v(ye,!1)})}e(ve),e(Za),r(le,Za),Te(),oe()}export{Kt as component};
