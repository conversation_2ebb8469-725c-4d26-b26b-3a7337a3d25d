import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as V,m as a,u as n,v as X,g as Z,t as ee,k as e,n as te,i as C,j as s,ae,r as T,s as se,o as oe}from"../chunks/p3DoyA09.js";import{h as ie}from"../chunks/BUelSUke.js";import{t as v,a as d}from"../chunks/B67foYpL.js";import{i as O}from"../chunks/DwdToawP.js";import{i as re}from"../chunks/D3pqaimu.js";import{s as ne,a as x}from"../chunks/qYb16FSw.js";import{o as le}from"../chunks/C_WNR8j8.js";import{b as ce,g as me}from"../chunks/CSyJhG7e.js";import{p as de}from"../chunks/C3hpKoCs.js";import{P as ve}from"../chunks/CC9utfo3.js";import{L as ue}from"../chunks/C8F602cz.js";import{a as j}from"../chunks/Ce-0qAhV.js";import{a as pe,c as fe}from"../chunks/Atsggda0.js";import"../chunks/CGKBDcrf.js";/* empty css                */import{c as ge,b as ye,d as _e,e as we,f as he,j as Ie}from"../chunks/DJsmiAoD.js";import"../chunks/D9cKHHet.js";import"../chunks/BvrzullT.js";var De=v('<div class="save-status unsaved svelte-n17m1y"><div class="status-dot svelte-n17m1y"></div> Unsaved changes</div>'),Le=v('<div class="save-status saved svelte-n17m1y"><div class="status-dot svelte-n17m1y"></div> All changes saved</div>'),Se=v('<div class="save-status-container svelte-n17m1y"><!></div>'),$e=v('<div class="loading-container svelte-n17m1y"><!> <p class="svelte-n17m1y">Loading invoice data...</p></div>'),Ce=v('<div class="container"><!> <!></div>');function Ge(k,J){V(J,!1);const[A,q]=ne(),F=()=>x(de,"$page",A),y=()=>x(fe,"$customers",A),u=a(),p=a(),_=a(),B=a(),H=a();let M=!0,U=a(!1),w=null,W=[],h=a([]),Y=a([]),I=a(!0),l=a(""),D=a(!1),c=a(),N=a([]),m=a({status:0,issueDate:new Date().toISOString().split("T")[0],dueDate:new Date(Date.now()+30*24*60*60*1e3).toISOString().split("T")[0],notes:"",paymentTerms:"Payment due within 30 days.",invoiceLines:[ge()]}),b=a(""),f=a(0);function P(){if(!e(c))return;const t=JSON.stringify({formData:e(m),customFields:e(N),discountAmount:e(f)});s(D,t!==e(c))}async function z(){s(I,!0);try{await pe.loadContacts();const[t,o,i]=await Promise.all([_e(),we(),he()]);if(W=t,s(h,o),s(Y,i),!e(p))if(w=await Ie(e(u)),w)s(m,{...w});else{j({message:"Invoice not found",type:"error"}),me("/invoices");return}if(e(h).length>0&&!e(b)){const g=e(h).find(S=>S.isDefault);g&&s(b,g.id)}}catch(t){console.error("Error loading data:",t),j({message:"Failed to load data",type:"error"})}finally{s(I,!1)}}ce(({cancel:t,to:o})=>{e(U)||(P(),e(D)&&(o==null?void 0:o.url.pathname)!==window.location.pathname&&(window.confirm("You have unsaved changes. Are you sure you want to leave?")||t()))}),le(async()=>{await z(),setTimeout(()=>{s(c,JSON.stringify({formData:e(m),customFields:e(N),discountAmount:e(f)}))},100)}),n(()=>F(),()=>{s(u,F().params.invoiceId)}),n(()=>e(u),()=>{s(p,e(u)==="new")}),n(()=>e(m),()=>{s(_,ye(e(m).invoiceLines))}),n(()=>(e(_),e(f)),()=>{s(B,e(_).totalAmount-e(f))}),n(()=>(e(l),y()),()=>{s(H,e(l)?y().filter(t=>t.fullName.toLowerCase().includes(e(l).toLowerCase())||t.companyName&&t.companyName.toLowerCase().includes(e(l).toLowerCase())||t.emails.some(o=>o.email.toLowerCase().includes(e(l).toLowerCase()))):y())}),n(()=>e(c),()=>{e(c)&&P()}),X(),re();var L=Ce();ie(t=>{ee(()=>ae.title=e(p)?"Create Invoice":"Edit Invoice")});var E=C(L);const G=te(()=>e(p)?"Create Invoice":"Edit Invoice");ve(E,{get title(){return e(G)},$$slots:{actions:(t,o)=>{var i=Se(),g=C(i);{var S=r=>{var $=De();d(r,$)},R=r=>{var $=Le();d(r,$)};O(g,r=>{e(D)?r(S):r(R,!1)})}T(i),d(t,i)}}});var K=se(E,2);{var Q=t=>{var o=$e(),i=C(o);ue(i,{}),oe(2),T(o),d(t,o)};O(K,t=>{(e(I)||M)&&t(Q)})}T(L),d(k,L),Z(),q()}export{Ge as component};
