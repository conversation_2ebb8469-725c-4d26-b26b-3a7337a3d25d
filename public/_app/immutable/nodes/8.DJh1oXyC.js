import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as na,u as da,v as ca,f as Fe,g as ua,t as $,k as e,n as A,i as s,m as L,s as o,j as i,ae as ma,o as P,r as t,l as bt}from"../chunks/p3DoyA09.js";import{h as fa,e as Ge,s as m,r as Lt}from"../chunks/BUelSUke.js";import{i as d}from"../chunks/DwdToawP.js";import{e as He,i as Ye}from"../chunks/DEqeA9IH.js";import{t as n,a,b as h,e as nt}from"../chunks/B67foYpL.js";import{r as xt,d as jt}from"../chunks/DdRd56Yq.js";import{s as Qe}from"../chunks/D7jLSc-x.js";import{b as st}from"../chunks/WI3NPOEW.js";import{p as wt}from"../chunks/Bfc47y5P.js";import{i as _a}from"../chunks/D3pqaimu.js";import{s as pa,a as ga}from"../chunks/qYb16FSw.js";import{o as ha}from"../chunks/C_WNR8j8.js";import{p as ya}from"../chunks/C3hpKoCs.js";import{g as ee}from"../chunks/CSyJhG7e.js";import{a as re}from"../chunks/Ce-0qAhV.js";import{B as k}from"../chunks/6Zk3JFqZ.js";import{P as $a}from"../chunks/CC9utfo3.js";import{L as dt}from"../chunks/C8F602cz.js";import{T as ba}from"../chunks/BIVs0YJ0.js";import{u as xa}from"../chunks/CGKBDcrf.js";import{api as Dt,ApiError as Ct}from"../chunks/C5jwvbV4.js";import{a as wa,b as Da,e as Ca}from"../chunks/B_pWBBGB.js";import{g as Pa,a as Pt}from"../chunks/DJsmiAoD.js";import{a as ka}from"../chunks/D9cKHHet.js";import{g as Na}from"../chunks/BXh2naGf.js";import{f as ct}from"../chunks/Chsk6cZE.js";import{M as Ft}from"../chunks/BWn8tY11.js";import{G as zt}from"../chunks/DEPinlMt.js";var Aa=n("<!> <!>",1),Ia=n("<!> <!>",1),Ea=n('<div class="error-container svelte-ovcodn"><p class="error-message svelte-ovcodn"> </p> <!></div>'),Sa=n('<span class="primary-badge svelte-ovcodn">Primary</span>'),Ta=n('<div><div class="item-value svelte-ovcodn"> </div> <span class="item-type svelte-ovcodn"> </span> <!></div>'),La=n('<div class="contact-section svelte-ovcodn"><h3 class="svelte-ovcodn">All Email Addresses</h3> <!></div>'),ja=n('<span class="primary-badge svelte-ovcodn">Primary</span>'),Fa=n('<div><div class="item-value svelte-ovcodn"> </div> <span class="item-type svelte-ovcodn"> </span> <!></div>'),za=n('<div class="contact-section svelte-ovcodn"><h3 class="svelte-ovcodn">All Phone Numbers</h3> <!></div>'),Ma=n('<span class="primary-badge svelte-ovcodn">Primary</span>'),Ja=n('<div><div class="item-value svelte-ovcodn"> <br> </div> <span class="item-type svelte-ovcodn"> </span> <!></div>'),Ba=n('<div class="contact-section svelte-ovcodn"><h3 class="svelte-ovcodn">All Addresses</h3> <!></div>'),qa=n('<div class="details-section svelte-ovcodn"><h2 class="svelte-ovcodn">Customer Information</h2> <form class="details-form svelte-ovcodn"><div class="form-group svelte-ovcodn"><label for="name" class="svelte-ovcodn">Name</label> <input id="name" type="text" required class="form-input svelte-ovcodn"></div> <div class="form-group svelte-ovcodn"><label for="email" class="svelte-ovcodn">Email</label> <input id="email" type="email" required class="form-input svelte-ovcodn"></div> <div class="form-group svelte-ovcodn"><label for="phone" class="svelte-ovcodn">Phone</label> <input id="phone" type="tel" required class="form-input svelte-ovcodn"></div></form> <!> <!> <!></div>'),Va=n('<div class="empty-state svelte-ovcodn"><p class="svelte-ovcodn">No invoices found for this customer.</p> <!></div>'),Ka=n('<div class="table-row svelte-ovcodn"><div class="table-cell svelte-ovcodn"><strong> </strong></div> <div class="table-cell svelte-ovcodn"> </div> <div class="table-cell svelte-ovcodn"><span class="status-badge svelte-ovcodn"> </span></div> <div class="table-cell svelte-ovcodn"> </div> <div class="table-cell svelte-ovcodn"> </div> <div class="table-cell svelte-ovcodn"><div class="action-buttons svelte-ovcodn"><!> <!></div></div></div>'),Ra=n('<div class="data-table svelte-ovcodn"><div class="table-header svelte-ovcodn"><div class="header-cell svelte-ovcodn">Invoice #</div> <div class="header-cell svelte-ovcodn">Date</div> <div class="header-cell svelte-ovcodn">Status</div> <div class="header-cell svelte-ovcodn">Amount</div> <div class="header-cell svelte-ovcodn">Balance</div> <div class="header-cell svelte-ovcodn">Actions</div></div> <!></div> <div class="summary-stats svelte-ovcodn"><div class="stat-card svelte-ovcodn"><div class="stat-label svelte-ovcodn">Total Invoices</div> <div class="stat-value svelte-ovcodn"> </div></div> <div class="stat-card svelte-ovcodn"><div class="stat-label svelte-ovcodn">Total Amount</div> <div class="stat-value svelte-ovcodn"> </div></div> <div class="stat-card svelte-ovcodn"><div class="stat-label svelte-ovcodn">Outstanding Balance</div> <div class="stat-value svelte-ovcodn"> </div></div></div>',1),Oa=n('<div class="invoices-section svelte-ovcodn"><div class="section-header svelte-ovcodn"><h2 class="svelte-ovcodn">Customer Invoices</h2> <!></div> <!></div>'),Ua=n('<div class="empty-state svelte-ovcodn"><p class="svelte-ovcodn">No events found for this customer.</p> <!></div>'),Ga=n('<div class="date-info svelte-ovcodn"><strong class="svelte-ovcodn"> </strong> <small class="svelte-ovcodn"> </small></div>'),Ha=n('<small class="svelte-ovcodn"> </small>'),Ya=n('<div class="service-info svelte-ovcodn"><strong class="svelte-ovcodn"> </strong> <!></div>'),Qa=n("<span> </span>"),Wa=n('<span class="staff-badge svelte-ovcodn"> </span>'),Xa=n('<div class="staff-list svelte-ovcodn"></div>'),Za=n("<span> </span>"),es=n('<div class="action-buttons svelte-ovcodn"><!> <!></div>'),ts=n('<div class="schedule-section svelte-ovcodn"><div class="section-header svelte-ovcodn"><h2 class="svelte-ovcodn">Schedule</h2> <!></div> <!></div>'),as=n('<div class="empty-state svelte-ovcodn"><p class="svelte-ovcodn">No jobs found for this customer.</p> <!></div>'),ss=n("<strong> </strong>"),rs=n('<span class="status-badge svelte-ovcodn"> </span>'),os=n("<span> </span>"),ls=n('<div class="action-buttons svelte-ovcodn"><!> <!></div>'),is=n('<div class="jobs-section svelte-ovcodn"><div class="section-header svelte-ovcodn"><h2 class="svelte-ovcodn">Customer Jobs</h2> <!></div> <!></div>'),vs=n('<div class="note-item svelte-ovcodn"><div class="note-header svelte-ovcodn"><div class="note-date svelte-ovcodn"> </div> <div class="note-actions svelte-ovcodn"><!> <!></div></div> <div class="note-content svelte-ovcodn"> </div></div>'),ns=n('<div class="notes-list svelte-ovcodn"></div>'),ds=n('<div class="empty-state svelte-ovcodn"><p class="svelte-ovcodn">No notes found for this customer.</p> <!></div>'),cs=n('<div class="notes-section svelte-ovcodn"><div class="section-header svelte-ovcodn"><h2 class="svelte-ovcodn">Customer Notes</h2> <!></div> <!></div>'),us=n('<div class="checklists-section svelte-ovcodn"><h2 class="svelte-ovcodn">Customer Checklists</h2> <p>Checklists functionality will be implemented here.</p></div>'),ms=n('<div class="timeline-section svelte-ovcodn"><h2 class="svelte-ovcodn">Communication Timeline</h2> <p>Timeline functionality will be implemented here.</p></div>'),fs=n('<div class="customer-details svelte-ovcodn"><!> <div class="tab-content svelte-ovcodn"><!></div></div>'),_s=n("<p>Customer not found.</p>"),ps=n('<form class="note-form svelte-ovcodn"><div class="form-group svelte-ovcodn"><label for="noteContent" class="svelte-ovcodn">Note Content</label> <textarea id="noteContent" placeholder="Enter your note here..." rows="4" required class="svelte-ovcodn"></textarea></div> <div class="modal-actions svelte-ovcodn"><!> <!></div></form>'),gs=n('<form class="note-form svelte-ovcodn"><div class="form-group svelte-ovcodn"><label for="editNoteContent" class="svelte-ovcodn">Note Content</label> <textarea id="editNoteContent" placeholder="Enter your note here..." rows="4" required class="svelte-ovcodn"></textarea></div> <div class="modal-actions svelte-ovcodn"><!> <!></div></form>'),hs=n('<div class="container"><!> <main><!></main></div> <!> <!>',1);function Qs(Mt,Jt){na(Jt,!1);const[Bt,qt]=pa(),Vt=()=>ga(ya,"$page",Bt);let l=L(null),rt=L(!0),ze=L(!1),Me=L(!1),ye=L(null),I=L(),Je=L(""),Be=L(""),qe=L(""),Ve=L(!1),Ie=L("details"),Re=L([]),ot=L([]),kt=L([]),ut=L(!1),mt=L(!1),ft=L(!1),lt=L(!1),_t=L(!1),ue=L(""),We=null,$e=L(!1);const Kt=[{id:"details",label:"Details"},{id:"invoices",label:"Invoices"},{id:"schedule",label:"Schedule"},{id:"jobs",label:"Jobs"},{id:"notes",label:"Notes"},{id:"checklists",label:"Checklists"},{id:"timeline",label:"Timeline"}];ha(()=>{const r=xa.subscribe(te=>{te||ee("/login")});return i(I,Vt().params.id),e(I)?Nt():(i(ye,"No customer ID provided"),i(rt,!1)),r});async function Nt(){i(rt,!0),i(ye,null);try{const r=await Dt.get(`/Customers/${e(I)}`);i(l,r),i(Je,e(l).name),i(Be,e(l).email),i(qe,e(l).phone),i(Ve,!1),await Rt()}catch(r){r instanceof Ct?(i(ye,`Failed to fetch customer: ${r.message}`),console.error("API Error:",r.status,r.message)):(i(ye,"An unexpected error occurred while fetching customer details."),console.error("Fetch customer error:",r))}finally{i(rt,!1)}}async function Rt(){await Promise.all([Ot(),Ut(),Gt()])}async function Ot(){if(e(I)){i(ut,!0);try{i(Re,await Pa(e(I)))}catch(r){console.error("Error loading customer invoices:",r),re({message:"Failed to load customer invoices",type:"error"})}finally{i(ut,!1)}}}async function Ut(){if(e(I)){i(mt,!0);try{i(ot,await ka(e(I)))}catch(r){console.error("Error loading customer jobs:",r),re({message:"Failed to load customer jobs",type:"error"})}finally{i(mt,!1)}}}async function Gt(){if(e(I)){i(ft,!0);try{const r=new Date(Date.now()-31536e6).toISOString(),te=new Date(Date.now()+365*24*60*60*1e3).toISOString(),J=await Na(r,te);i(kt,J.filter(oe=>oe.customerId===e(I)))}catch(r){console.error("Error loading customer events:",r),re({message:"Failed to load customer events",type:"error"})}finally{i(ft,!1)}}}async function At(){if(!(!e(l)||!e(Ve))){i(ze,!0),i(ye,null);try{const r={name:e(Je).trim(),email:e(Be).trim(),phone:e(qe).trim()};await Dt.put(`/Customers/${e(I)}`,r),i(l,{...e(l),name:r.name,email:r.email,phone:r.phone}),i(Ve,!1),re({message:"Customer updated successfully",type:"success"})}catch(r){r instanceof Ct?(i(ye,`Failed to update customer: ${r.message}`),console.error("API Error:",r.status,r.message),re({message:`Failed to update customer: ${r.message}`,type:"error"})):(i(ye,"An unexpected error occurred while updating customer."),console.error("Update customer error:",r),re({message:"An unexpected error occurred while updating customer.",type:"error"}))}finally{i(ze,!1)}}}async function Ht(){if(e(l)&&confirm(`Are you sure you want to delete customer "${e(l).name}"? This action cannot be undone.`)){i(Me,!0),i(ye,null);try{await Dt.delete(`/Customers/${e(I)}`),re({message:"Customer deleted successfully",type:"success"}),ee("/customers")}catch(r){r instanceof Ct?(i(ye,`Failed to delete customer: ${r.message}`),console.error("API Error:",r.status,r.message),re({message:`Failed to delete customer: ${r.message}`,type:"error"})):(i(ye,"An unexpected error occurred while deleting customer."),console.error("Delete customer error:",r),re({message:"An unexpected error occurred while deleting customer.",type:"error"}))}finally{i(Me,!1)}}}function Yt(){e(Ve)&&!confirm("You have unsaved changes. Are you sure you want to leave?")||ee("/customers")}function pt(){e(l)&&i(Ve,e(Je)!==e(l).name||e(Be)!==e(l).email||e(qe)!==e(l).phone)}async function Qt(){if(!(!e(ue).trim()||!e(I))){i($e,!0);try{const r=await wa(e(I),e(ue).trim());e(l)&&(bt(l,e(l).notes=e(l).notes||[]),e(l).notes.push(r),i(l,{...e(l)})),re({message:"Note added successfully",type:"success"}),gt()}catch(r){console.error("Error adding note:",r),re({message:"Failed to add note",type:"error"})}finally{i($e,!1)}}}function Wt(r){We=r,i(ue,r.content),i(_t,!0)}async function Xt(){if(!(!e(ue).trim()||!We||!e(I))){i($e,!0);try{const r=await Da(e(I),We.id,e(ue).trim());if(e(l)&&e(l).notes){const te=e(l).notes.findIndex(J=>J.id===We.id);te!==-1&&(bt(l,e(l).notes[te]=r),i(l,{...e(l)}))}re({message:"Note updated successfully",type:"success"}),ht()}catch(r){console.error("Error updating note:",r),re({message:"Failed to update note",type:"error"})}finally{i($e,!1)}}}async function Zt(r){if(confirm("Are you sure you want to delete this note?"))try{await Ca(e(I),r),e(l)&&e(l).notes&&(bt(l,e(l).notes=e(l).notes.filter(te=>te.id!==r)),i(l,{...e(l)})),re({message:"Note deleted successfully",type:"success"})}catch(te){console.error("Error deleting note:",te),re({message:"Failed to delete note",type:"error"})}}function gt(){i(lt,!1),i(ue,"")}function ht(){i(_t,!1),i(ue,""),We=null}da(()=>(e(l),e(Je),e(Be),e(qe)),()=>{e(l)&&i(Ve,e(Je)!==e(l).name||e(Be)!==e(l).email||e(qe)!==e(l).phone)}),ca(),_a();var It=hs();fa(r=>{$(()=>ma.title=e(l)?`${e(l).name} - Edit Customer`:"Edit Customer")});var yt=Fe(It),Et=s(yt);const ea=A(()=>e(l)?`Edit ${e(l).name}`:"Edit Customer");$a(Et,{get title(){return e(ea)},$$slots:{actions:(r,te)=>{var J=Ia(),oe=Fe(J);k(oe,{variant:"secondary",type:"button",$$events:{click:Yt},children:(O,xe)=>{P();var U=h("Back to Customers");a(O,U)},$$slots:{default:!0}});var W=o(oe,2);{var me=O=>{var xe=Aa(),U=Fe(xe);k(U,{variant:"danger",type:"button",get disabled(){return e(Me)},$$events:{click:Ht},children:(B,$t)=>{P();var Ke=h();$(()=>m(Ke,e(Me)?"Deleting...":"Delete Customer")),a(B,Ke)},$$slots:{default:!0}});var T=o(U,2);const Ee=A(()=>!e(Ve)||e(ze));k(T,{variant:"primary",type:"button",get disabled(){return e(Ee)},$$events:{click:At},children:(B,$t)=>{P();var Ke=h();$(()=>m(Ke,e(ze)?"Saving...":"Save Changes")),a(B,Ke)},$$slots:{default:!0}}),a(O,xe)};d(W,O=>{e(l)&&O(me)})}a(r,J)}}});var St=o(Et,2),ta=s(St);{var aa=r=>{dt(r,{message:"Loading customer details..."})},sa=(r,te)=>{{var J=W=>{var me=Ea(),O=s(me),xe=s(O,!0);t(O);var U=o(O,2);k(U,{type:"button",$$events:{click:Nt},children:(T,Ee)=>{P();var B=h("Retry");a(T,B)},$$slots:{default:!0}}),t(me),$(()=>m(xe,e(ye))),a(W,me)},oe=(W,me)=>{{var O=U=>{var T=fs(),Ee=s(T);ba(Ee,{tabs:Kt,get activeTab(){return e(Ie)},set activeTab(Le){i(Ie,Le)},$$legacy:!0});var B=o(Ee,2),$t=s(B);{var Ke=Le=>{var Xe=qa(),Oe=o(s(Xe),2),Ze=s(Oe),we=o(s(Ze),2);xt(we),t(Ze);var Se=o(Ze,2),ke=o(s(Se),2);xt(ke),t(Se);var Ue=o(Se,2),fe=o(s(Ue),2);xt(fe),t(Ue),t(Oe);var Ne=o(Oe,2);{var Te=f=>{var y=La(),C=o(s(y),2);He(C,1,()=>e(l).emails,Ye,(F,_,x,w)=>{var b=Ta();let u;var v=s(b),g=s(v,!0);t(v);var c=o(v,2),N=s(c,!0);t(c);var z=o(c,2);{var D=p=>{var E=Sa();a(p,E)};d(z,p=>{e(_).isPrimary&&p(D)})}t(b),$(p=>{u=Qe(b,1,"contact-item svelte-ovcodn",null,u,p),m(g,e(_).email),m(N,e(_).type)},[()=>({primary:e(_).isPrimary})],A),a(F,b)}),t(y),a(f,y)};d(Ne,f=>{e(l).emails&&e(l).emails.length>0&&f(Te)})}var ae=o(Ne,2);{var _e=f=>{var y=za(),C=o(s(y),2);He(C,1,()=>e(l).phones,Ye,(F,_,x,w)=>{var b=Fa();let u;var v=s(b),g=s(v,!0);t(v);var c=o(v,2),N=s(c,!0);t(c);var z=o(c,2);{var D=p=>{var E=ja();a(p,E)};d(z,p=>{e(_).isPrimary&&p(D)})}t(b),$(p=>{u=Qe(b,1,"contact-item svelte-ovcodn",null,u,p),m(g,e(_).phone),m(N,e(_).type)},[()=>({primary:e(_).isPrimary})],A),a(F,b)}),t(y),a(f,y)};d(ae,f=>{e(l).phones&&e(l).phones.length>0&&f(_e)})}var pe=o(ae,2);{var De=f=>{var y=Ba(),C=o(s(y),2);He(C,1,()=>e(l).addresses,Ye,(F,_)=>{var x=Ja();let w;var b=s(x),u=s(b,!0),v=o(u,2);t(b);var g=o(b,2),c=s(g,!0);t(g);var N=o(g,2);{var z=D=>{var p=Ma();a(D,p)};d(N,D=>{e(_).isPrimary&&D(z)})}t(x),$(D=>{w=Qe(x,1,"contact-item svelte-ovcodn",null,w,D),m(u,e(_).street),m(v,` ${e(_).city??""}, ${e(_).state??""} ${e(_).zipCode??""}`),m(c,e(_).type)},[()=>({primary:e(_).isPrimary})],A),a(F,x)}),t(y),a(f,y)};d(pe,f=>{e(l).addresses&&e(l).addresses.length>0&&f(De)})}t(Xe),$(()=>{we.disabled=e(ze)||e(Me),ke.disabled=e(ze)||e(Me),fe.disabled=e(ze)||e(Me)}),st(we,()=>e(Je),f=>i(Je,f)),Ge("input",we,pt),st(ke,()=>e(Be),f=>i(Be,f)),Ge("input",ke,pt),st(fe,()=>e(qe),f=>i(qe,f)),Ge("input",fe,pt),Ge("submit",Oe,wt(At)),a(Le,Xe)},oa=(Le,Xe)=>{{var Oe=we=>{var Se=Oa(),ke=s(Se),Ue=o(s(ke),2);k(Ue,{variant:"primary",size:"small",$$events:{click:()=>ee(`/invoices/new?customerId=${e(I)}`)},children:(ae,_e)=>{P();var pe=h("Create New Invoice");a(ae,pe)},$$slots:{default:!0}}),t(ke);var fe=o(ke,2);{var Ne=ae=>{dt(ae,{message:"Loading invoices..."})},Te=(ae,_e)=>{{var pe=f=>{var y=Va(),C=o(s(y),2);k(C,{variant:"primary",$$events:{click:()=>ee(`/invoices/new?customerId=${e(I)}`)},children:(F,_)=>{P();var x=h("Create First Invoice");a(F,x)},$$slots:{default:!0}}),t(y),a(f,y)},De=f=>{var y=Ra(),C=Fe(y),F=o(s(C),2);He(F,1,()=>e(Re),Ye,(D,p)=>{var E=Ka(),q=s(E),G=s(q),le=s(G,!0);t(G),t(q);var M=o(q,2),H=s(M,!0);t(M);var S=o(M,2),V=s(S),be=s(V,!0);t(V),t(S);var ie=o(S,2),X=s(ie,!0);t(ie);var K=o(ie,2),ve=s(K,!0);t(K);var Ae=o(K,2),ge=s(Ae),he=s(ge);k(he,{variant:"tertiary",size:"small",$$events:{click:()=>ee(`/invoices/${e(p).id}`)},children:(j,R)=>{P();var ne=h("View");a(j,ne)},$$slots:{default:!0}});var Z=o(he,2);k(Z,{variant:"secondary",size:"small",$$events:{click:()=>ee(`/invoices/${e(p).id}/edit`)},children:(j,R)=>{P();var ne=h("Edit");a(j,ne)},$$slots:{default:!0}}),t(ge),t(Ae),t(E),$((j,R,ne,de,Y,Q)=>{m(le,e(p).invoiceNumber),m(H,j),jt(V,`background-color: ${R??""}20; color: ${ne??""};`),m(be,de),m(X,Y),m(ve,Q)},[()=>new Date(e(p).issueDate).toLocaleDateString(),()=>Pt(e(p).status).color,()=>Pt(e(p).status).color,()=>Pt(e(p).status).name,()=>ct(e(p).invoiceLines.reduce((j,R)=>j+R.total,0)),()=>ct(e(p).invoiceLines.reduce((j,R)=>j+R.total,0))],A),a(D,E)}),t(C);var _=o(C,2),x=s(_),w=o(s(x),2),b=s(w,!0);t(w),t(x);var u=o(x,2),v=o(s(u),2),g=s(v,!0);t(v),t(u);var c=o(u,2),N=o(s(c),2),z=s(N,!0);t(N),t(c),t(_),$((D,p)=>{m(b,e(Re).length),m(g,D),m(z,p)},[()=>ct(e(Re).reduce((D,p)=>D+p.invoiceLines.reduce((E,q)=>E+q.total,0),0)),()=>ct(e(Re).reduce((D,p)=>D+p.invoiceLines.reduce((E,q)=>E+q.total,0),0))],A),a(f,y)};d(ae,f=>{e(Re).length===0?f(pe):f(De,!1)},_e)}};d(fe,ae=>{e(ut)?ae(Ne):ae(Te,!1)})}t(Se),a(we,Se)},Ze=(we,Se)=>{{var ke=fe=>{var Ne=ts(),Te=s(Ne),ae=o(s(Te),2);k(ae,{variant:"primary",size:"small",$$events:{click:()=>ee(`/calendar?customerId=${e(I)}`)},children:(f,y)=>{P();var C=h("Schedule New Visit");a(f,C)},$$slots:{default:!0}}),t(Te);var _e=o(Te,2);{var pe=f=>{dt(f,{message:"Loading schedule..."})},De=f=>{var y=nt();const C=A(()=>e(kt).sort((w,b)=>new Date(w.startDateTime).getTime()-new Date(b.startDateTime).getTime()));var F=Fe(y);{var _=w=>{var b=Ua(),u=o(s(b),2);k(u,{variant:"primary",$$events:{click:()=>ee(`/calendar?customerId=${e(I)}`)},children:(v,g)=>{P();var c=h("Schedule First Visit");a(v,c)},$$slots:{default:!0}}),t(b),a(w,b)},x=w=>{zt(w,{headers:[{text:"Date",key:"startDateTime",sortable:!0},{text:"Service",key:"title",sortable:!0},{text:"Status",key:"status",sortable:!0},{text:"Staff",key:"assignedStaff",sortable:!1},{text:"Duration",key:"duration",sortable:!1},{text:"Priority",key:"priority",sortable:!0},{text:"Actions",key:"actions",sortable:!1}],get dataRows(){return e(C)},emptyMessage:"No events found for this customer.",get totalItems(){return e(C).length},itemsPerPage:25,currentPage:1,$$slots:{cell:(b,u)=>{const v=A(()=>u.row),g=A(()=>u.headerKey),c=A(()=>u.value);var N=nt(),z=Fe(N);{var D=E=>{var q=Ga(),G=s(q),le=s(G,!0);t(G);var M=o(G,2),H=s(M,!0);t(M),t(q),$((S,V)=>{m(le,S),m(H,V)},[()=>new Date(e(v).startDateTime).toLocaleDateString(),()=>new Date(e(v).startDateTime).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})],A),a(E,q)},p=(E,q)=>{{var G=M=>{var H=Ya(),S=s(H),V=s(S,!0);t(S);var be=o(S,2);{var ie=X=>{var K=Ha(),ve=s(K,!0);t(K),$(()=>m(ve,e(v).description)),a(X,K)};d(be,X=>{e(v).description&&X(ie)})}t(H),$(()=>m(V,e(v).title)),a(M,H)},le=(M,H)=>{{var S=be=>{var ie=Qa(),X=s(ie,!0);t(ie),$(K=>{Qe(ie,1,`status-badge ${K??""}`,"svelte-ovcodn"),m(X,e(v).status)},[()=>e(v).status.toLowerCase()],A),a(be,ie)},V=(be,ie)=>{{var X=ve=>{var Ae=Xa();He(Ae,5,()=>e(v).assignedStaff,Ye,(ge,he)=>{var Z=Wa(),j=s(Z,!0);t(Z),$(()=>m(j,e(he).staffName)),a(ge,Z)}),t(Ae),a(ve,Ae)},K=(ve,Ae)=>{{var ge=Z=>{var j=nt(),R=Fe(j);{var ne=Y=>{var Q=h();$(ce=>m(Q,`${ce??""}h ${e(v).actualDuration%60}m`),[()=>Math.floor(e(v).actualDuration/60)],A),a(Y,Q)},de=(Y,Q)=>{{var ce=se=>{var Pe=h();$(et=>m(Pe,`~${et??""}h ${e(v).estimatedDuration%60}m`),[()=>Math.floor(e(v).estimatedDuration/60)],A),a(se,Pe)},Ce=se=>{var Pe=h("-");a(se,Pe)};d(Y,se=>{e(v).estimatedDuration?se(ce):se(Ce,!1)},Q)}};d(R,Y=>{new Date(e(v).startDateTime)<new Date&&e(v).actualDuration?Y(ne):Y(de,!1)})}a(Z,j)},he=(Z,j)=>{{var R=de=>{var Y=Za(),Q=s(Y,!0);t(Y),$(ce=>{Qe(Y,1,`priority-badge ${ce??""}`,"svelte-ovcodn"),m(Q,e(v).priority)},[()=>e(v).priority.toLowerCase()],A),a(de,Y)},ne=(de,Y)=>{{var Q=Ce=>{var se=es(),Pe=s(se);k(Pe,{variant:"tertiary",size:"small",$$events:{click:()=>ee(`/calendar?eventId=${e(v).id}`)},children:(je,it)=>{P();var vt=h("View");a(je,vt)},$$slots:{default:!0}});var et=o(Pe,2);{var tt=je=>{k(je,{variant:"secondary",size:"small",$$events:{click:()=>ee(`/calendar?eventId=${e(v).id}&action=edit`)},children:(it,vt)=>{P();var at=h("Reschedule");a(it,at)},$$slots:{default:!0}})},la=(je,it)=>{{var vt=at=>{k(at,{variant:"secondary",size:"small",$$events:{click:()=>ee(`/invoices/new?jobId=${e(v).jobId}&customerId=${e(I)}`)},children:(ia,ys)=>{P();var va=h("Invoice");a(ia,va)},$$slots:{default:!0}})};d(je,at=>{e(v).jobId&&at(vt)},it)}};d(et,je=>{new Date(e(v).startDateTime)>new Date?je(tt):je(la,!1)})}t(se),a(Ce,se)},ce=Ce=>{var se=h();$(()=>m(se,e(c))),a(Ce,se)};d(de,Ce=>{e(g)==="actions"?Ce(Q):Ce(ce,!1)},Y)}};d(Z,de=>{e(g)==="priority"?de(R):de(ne,!1)},j)}};d(ve,Z=>{e(g)==="duration"?Z(ge):Z(he,!1)},Ae)}};d(be,ve=>{e(g)==="assignedStaff"?ve(X):ve(K,!1)},ie)}};d(M,be=>{e(g)==="status"?be(S):be(V,!1)},H)}};d(E,M=>{e(g)==="title"?M(G):M(le,!1)},q)}};d(z,E=>{e(g)==="startDateTime"?E(D):E(p,!1)})}a(b,N)}}})};d(F,w=>{e(C).length===0?w(_):w(x,!1)})}a(f,y)};d(_e,f=>{e(ft)?f(pe):f(De,!1)})}t(Ne),a(fe,Ne)},Ue=(fe,Ne)=>{{var Te=_e=>{var pe=is(),De=s(pe),f=o(s(De),2);k(f,{variant:"primary",size:"small",$$events:{click:()=>ee(`/jobs/new?customerId=${e(I)}`)},children:(_,x)=>{P();var w=h("Create New Job");a(_,w)},$$slots:{default:!0}}),t(De);var y=o(De,2);{var C=_=>{dt(_,{message:"Loading jobs..."})},F=(_,x)=>{{var w=u=>{var v=as(),g=o(s(v),2);k(g,{variant:"primary",$$events:{click:()=>ee(`/jobs/new?customerId=${e(I)}`)},children:(c,N)=>{P();var z=h("Create First Job");a(c,z)},$$slots:{default:!0}}),t(v),a(u,v)},b=u=>{zt(u,{headers:[{text:"Job ID",key:"id",sortable:!0},{text:"Title",key:"title",sortable:!0},{text:"Status",key:"status",sortable:!0},{text:"Priority",key:"priority",sortable:!0},{text:"Created",key:"createdAt",sortable:!0},{text:"Actions",key:"actions",sortable:!1}],get dataRows(){return e(ot)},emptyMessage:"No jobs found for this customer.",get totalItems(){return e(ot).length},itemsPerPage:25,currentPage:1,$$slots:{cell:(v,g)=>{const c=A(()=>g.row),N=A(()=>g.headerKey),z=A(()=>g.value);var D=nt(),p=Fe(D);{var E=G=>{var le=ss(),M=s(le,!0);t(le),$(()=>m(M,e(c).id)),a(G,le)},q=(G,le)=>{{var M=S=>{var V=h();$(()=>m(V,e(c).title)),a(S,V)},H=(S,V)=>{{var be=X=>{var K=rs(),ve=s(K,!0);t(K),$(()=>{jt(K,`background-color: ${e(c).status.color??""}20; color: ${e(c).status.color??""};`),m(ve,e(c).status.name)}),a(X,K)},ie=(X,K)=>{{var ve=ge=>{var he=os(),Z=s(he,!0);t(he),$(j=>{Qe(he,1,`priority-badge ${j??""}`,"svelte-ovcodn"),m(Z,e(c).priority)},[()=>e(c).priority.toLowerCase()],A),a(ge,he)},Ae=(ge,he)=>{{var Z=R=>{var ne=h();$(de=>m(ne,de),[()=>new Date(e(c).createdAt).toLocaleDateString()],A),a(R,ne)},j=(R,ne)=>{{var de=Q=>{var ce=ls(),Ce=s(ce);k(Ce,{variant:"tertiary",size:"small",$$events:{click:()=>ee(`/jobs/${e(c).id}`)},children:(Pe,et)=>{P();var tt=h("View");a(Pe,tt)},$$slots:{default:!0}});var se=o(Ce,2);k(se,{variant:"secondary",size:"small",$$events:{click:()=>ee(`/jobs/${e(c).id}/edit`)},children:(Pe,et)=>{P();var tt=h("Edit");a(Pe,tt)},$$slots:{default:!0}}),t(ce),a(Q,ce)},Y=Q=>{var ce=h();$(()=>m(ce,e(z))),a(Q,ce)};d(R,Q=>{e(N)==="actions"?Q(de):Q(Y,!1)},ne)}};d(ge,R=>{e(N)==="createdAt"?R(Z):R(j,!1)},he)}};d(X,ge=>{e(N)==="priority"?ge(ve):ge(Ae,!1)},K)}};d(S,X=>{e(N)==="status"?X(be):X(ie,!1)},V)}};d(G,S=>{e(N)==="title"?S(M):S(H,!1)},le)}};d(p,G=>{e(N)==="id"?G(E):G(q,!1)})}a(v,D)}}})};d(_,u=>{e(ot).length===0?u(w):u(b,!1)},x)}};d(y,_=>{e(mt)?_(C):_(F,!1)})}t(pe),a(_e,pe)},ae=(_e,pe)=>{{var De=y=>{var C=cs(),F=s(C),_=o(s(F),2);k(_,{variant:"primary",size:"small",$$events:{click:()=>i(lt,!0)},children:(u,v)=>{P();var g=h("Add Note");a(u,g)},$$slots:{default:!0}}),t(F);var x=o(F,2);{var w=u=>{var v=ns();He(v,5,()=>e(l).notes.sort((g,c)=>new Date(c.createdAt).getTime()-new Date(g.createdAt).getTime()),Ye,(g,c)=>{var N=vs(),z=s(N),D=s(z),p=s(D);t(D);var E=o(D,2),q=s(E);k(q,{variant:"tertiary",size:"small",$$events:{click:()=>Wt(e(c))},children:(H,S)=>{P();var V=h("Edit");a(H,V)},$$slots:{default:!0}});var G=o(q,2);k(G,{variant:"tertiary",size:"small",$$events:{click:()=>Zt(e(c).id)},children:(H,S)=>{P();var V=h("Delete");a(H,V)},$$slots:{default:!0}}),t(E),t(z);var le=o(z,2),M=s(le,!0);t(le),t(N),$((H,S)=>{m(p,`${H??""} at ${S??""}`),m(M,e(c).content)},[()=>new Date(e(c).createdAt).toLocaleDateString(),()=>new Date(e(c).createdAt).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})],A),a(g,N)}),t(v),a(u,v)},b=u=>{var v=ds(),g=o(s(v),2);k(g,{variant:"primary",$$events:{click:()=>i(lt,!0)},children:(c,N)=>{P();var z=h("Add First Note");a(c,z)},$$slots:{default:!0}}),t(v),a(u,v)};d(x,u=>{e(l).notes&&e(l).notes.length>0?u(w):u(b,!1)})}t(C),a(y,C)},f=(y,C)=>{{var F=x=>{var w=us();a(x,w)},_=(x,w)=>{{var b=u=>{var v=ms();a(u,v)};d(x,u=>{e(Ie)==="timeline"&&u(b)},w)}};d(y,x=>{e(Ie)==="checklists"?x(F):x(_,!1)},C)}};d(_e,y=>{e(Ie)==="notes"?y(De):y(f,!1)},pe)}};d(fe,_e=>{e(Ie)==="jobs"?_e(Te):_e(ae,!1)},Ne)}};d(we,fe=>{e(Ie)==="schedule"?fe(ke):fe(Ue,!1)},Se)}};d(Le,we=>{e(Ie)==="invoices"?we(Oe):we(Ze,!1)},Xe)}};d($t,Le=>{e(Ie)==="details"?Le(Ke):Le(oa,!1)})}t(B),t(T),a(U,T)},xe=U=>{var T=_s();a(U,T)};d(W,U=>{e(l)?U(O):U(xe,!1)},me)}};d(r,W=>{e(ye)?W(J):W(oe,!1)},te)}};d(ta,r=>{e(rt)?r(aa):r(sa,!1)})}t(St),t(yt);var Tt=o(yt,2);Ft(Tt,{title:"Add Note",get show(){return e(lt)},$$events:{close:gt},children:(r,te)=>{var J=ps(),oe=s(J),W=o(s(oe),2);Lt(W),t(oe);var me=o(oe,2),O=s(me);k(O,{variant:"secondary",get disabled(){return e($e)},$$events:{click:gt},children:(T,Ee)=>{P();var B=h("Cancel");a(T,B)},$$slots:{default:!0}});var xe=o(O,2);const U=A(()=>e($e)||!e(ue).trim());k(xe,{type:"submit",variant:"primary",get disabled(){return e(U)},children:(T,Ee)=>{P();var B=h();$(()=>m(B,e($e)?"Adding...":"Add Note")),a(T,B)},$$slots:{default:!0}}),t(me),t(J),$(()=>W.disabled=e($e)),st(W,()=>e(ue),T=>i(ue,T)),Ge("submit",J,wt(Qt)),a(r,J)},$$slots:{default:!0}});var ra=o(Tt,2);Ft(ra,{title:"Edit Note",get show(){return e(_t)},$$events:{close:ht},children:(r,te)=>{var J=gs(),oe=s(J),W=o(s(oe),2);Lt(W),t(oe);var me=o(oe,2),O=s(me);k(O,{variant:"secondary",get disabled(){return e($e)},$$events:{click:ht},children:(T,Ee)=>{P();var B=h("Cancel");a(T,B)},$$slots:{default:!0}});var xe=o(O,2);const U=A(()=>e($e)||!e(ue).trim());k(xe,{type:"submit",variant:"primary",get disabled(){return e(U)},children:(T,Ee)=>{P();var B=h();$(()=>m(B,e($e)?"Updating...":"Update Note")),a(T,B)},$$slots:{default:!0}}),t(me),t(J),$(()=>W.disabled=e($e)),st(W,()=>e(ue),T=>i(ue,T)),Ge("submit",J,wt(Xt)),a(r,J)},$$slots:{default:!0}}),a(Mt,It),ua(),qt()}export{Qs as component};
