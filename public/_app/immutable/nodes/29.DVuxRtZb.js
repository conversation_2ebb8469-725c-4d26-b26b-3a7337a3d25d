import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as V,u as W,v as X,g as Y,ae as Z,s as b,i as r,j as n,m as y,k as t,r as a,t as I,n as B}from"../chunks/p3DoyA09.js";import{h as $,s as F,e as ee}from"../chunks/BUelSUke.js";import{i as j}from"../chunks/DwdToawP.js";import{e as te,i as se}from"../chunks/DEqeA9IH.js";import{t as d,a as v}from"../chunks/B67foYpL.js";import{s as ae}from"../chunks/D7jLSc-x.js";import{i as oe}from"../chunks/D3pqaimu.js";import{o as re}from"../chunks/C_WNR8j8.js";let k=null,g=null;async function ie(){if(k)return k;try{const o=await fetch("/docs-index.json");if(!o.ok)throw new Error(`Failed to fetch docs index: ${o.statusText}`);return k=await o.json(),k}catch(o){throw console.error("Error loading docs index:",o),new Error("Failed to load documentation index")}}async function ne(o){if(!g)try{const f=await fetch("/docs-content.json");if(!f.ok)throw new Error(`Failed to fetch docs content: ${f.statusText}`);g=await f.json()}catch(f){throw console.error("Error loading docs content:",f),new Error("Failed to load documentation content")}if(!g||!g[o])throw new Error(`Documentation file "${o}" not found`);return g[o]}var le=d('<li class="svelte-1byf2s4"><button> </button></li>'),ce=d('<ul class="svelte-1byf2s4"></ul>'),ve=d("<p>Loading docs...</p>"),de=d('<div class="error svelte-1byf2s4"><h3>Error</h3> <p> </p></div>'),fe=d('<div class="loading svelte-1byf2s4"><p>Loading document...</p></div>'),ue=d('<div class="document svelte-1byf2s4"><h3 class="svelte-1byf2s4"> </h3> <pre class="doc-content svelte-1byf2s4"> </pre></div>'),pe=d('<div class="empty-state svelte-1byf2s4"><p>Select a document from the sidebar to view its content.</p></div>'),me=d('<div class="container svelte-1byf2s4"><h1 class="svelte-1byf2s4">Documentation Test</h1> <div class="docs-interface svelte-1byf2s4"><div class="docs-sidebar svelte-1byf2s4"><h2 class="svelte-1byf2s4">Available Docs</h2> <!></div> <div class="docs-content svelte-1byf2s4"><!></div></div></div>');function je(o,f){V(f,!1);let w=y([]),p=y(""),x=y(""),C=y(!1),h=y("");re(async()=>{try{n(w,await ie())}catch(e){n(h,e instanceof Error?e.message:"Failed to load docs")}});async function G(e){if(e){n(C,!0),n(h,"");try{n(x,await ne(e))}catch(s){n(h,s instanceof Error?s.message:"Failed to load document"),n(x,"")}finally{n(C,!1)}}}W(()=>t(p),()=>{t(p)&&G(t(p))}),X(),oe();var L=me();$(e=>{Z.title="Docs Test"});var M=b(r(L),2),T=r(M),H=b(r(T),2);{var J=e=>{var s=ce();te(s,5,()=>t(w),se,(m,u)=>{var i=le(),l=r(i);let E;var q=r(l,!0);a(l),a(i),I(c=>{E=ae(l,1,"doc-button svelte-1byf2s4",null,E,c),F(q,t(u).label)},[()=>({active:t(p)===t(u).filename})],B),ee("click",l,()=>n(p,t(u).filename)),v(m,i)}),a(s),v(e,s)},K=e=>{var s=ve();v(e,s)};j(H,e=>{t(w).length>0?e(J):e(K,!1)})}a(T);var S=b(T,2),N=r(S);{var O=e=>{var s=de(),m=b(r(s),2),u=r(m,!0);a(m),a(s),I(()=>F(u,t(h))),v(e,s)},P=(e,s)=>{{var m=i=>{var l=fe();v(i,l)},u=(i,l)=>{{var E=c=>{var _=ue(),A=r(_),Q=r(A,!0);a(A);var z=b(A,2),R=r(z,!0);a(z),a(_),I(D=>{F(Q,D),F(R,t(x))},[()=>{var D;return((D=t(w).find(U=>U.filename===t(p)))==null?void 0:D.label)||"Document"}],B),v(c,_)},q=c=>{var _=pe();v(c,_)};j(i,c=>{t(x)?c(E):c(q,!1)},l)}};j(e,i=>{t(C)?i(m):i(u,!1)},s)}};j(N,e=>{t(h)?e(O):e(P,!1)})}a(S),a(M),a(L),v(o,L),Y()}export{je as component};
