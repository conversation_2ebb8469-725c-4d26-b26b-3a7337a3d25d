import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as fa,f as st,g as ma,j as d,m as B,i as s,o as N,r as a,k as e,s as l,t as S,n as Be,l as ce,q as ft,u as $a,v as Ta,ae as wa}from"../chunks/p3DoyA09.js";import{s as M,e as $,r as Ca,h as xa}from"../chunks/BUelSUke.js";import{i as f}from"../chunks/DwdToawP.js";import{e as xe,i as Ae}from"../chunks/DEqeA9IH.js";import{e as St,a as o,t as c,b as F}from"../chunks/B67foYpL.js";import{r as Me,d as Pt,s as ca}from"../chunks/DdRd56Yq.js";import{s as Ft}from"../chunks/D7jLSc-x.js";import{b as Qe,a as Sa}from"../chunks/WI3NPOEW.js";import{b as Qt}from"../chunks/DSjDIsro.js";import{i as _a}from"../chunks/D3pqaimu.js";import{c as Pa,o as Fa}from"../chunks/C_WNR8j8.js";import{B as le}from"../chunks/6Zk3JFqZ.js";import{P as ka}from"../chunks/CC9utfo3.js";import{L as ga}from"../chunks/C8F602cz.js";import{T as ha}from"../chunks/BIVs0YJ0.js";import{b as La}from"../chunks/chpzhzGT.js";import{s as Aa}from"../chunks/Bfc47y5P.js";import{p as Ea}from"../chunks/qYb16FSw.js";import{a as Ye}from"../chunks/Ce-0qAhV.js";import{e as Na,u as Ra,l as za,m as Ua,k as Wa,f as Oa,a as at}from"../chunks/DJsmiAoD.js";import{g as ya}from"../chunks/CSyJhG7e.js";import{f as ua}from"../chunks/Chsk6cZE.js";import{S as Ht}from"../chunks/BYzA1sSu.js";var Ha=c('<div class="loading-container svelte-13y1yrd"><!> <p class="svelte-13y1yrd">Loading templates...</p></div>'),Ba=c('<div class="empty-state svelte-13y1yrd"><p class="svelte-13y1yrd">No templates found. Create your first invoice template to get started.</p> <!></div>'),Va=c('<span class="default-badge svelte-13y1yrd">Default</span>'),ja=c('<div class="template-card svelte-13y1yrd"><div class="template-preview svelte-13y1yrd"><div class="template-header svelte-13y1yrd"> </div> <div class="template-content svelte-13y1yrd"><div class="sample-line svelte-13y1yrd"></div> <div class="sample-line short svelte-13y1yrd"></div> <div class="sample-line svelte-13y1yrd"></div></div></div> <div class="template-info svelte-13y1yrd"><h3 class="svelte-13y1yrd"> </h3> <!></div> <div class="template-actions svelte-13y1yrd"><!> <!></div></div>'),qa=c('<div class="templates-grid svelte-13y1yrd"></div>'),Ga=c('<main class="svelte-13y1yrd"><div class="templates-section svelte-13y1yrd"><div class="section-header svelte-13y1yrd"><h2 class="svelte-13y1yrd">Invoice Templates</h2> <!></div> <!></div></main>'),Ma=c('<div class="property-group svelte-13y1yrd"><h4 class="svelte-13y1yrd">Typography</h4> <div class="input-group svelte-13y1yrd"><label class="svelte-13y1yrd">Font Family</label> <select class="svelte-13y1yrd"><option class="svelte-13y1yrd">Arial</option><option class="svelte-13y1yrd">Helvetica</option><option class="svelte-13y1yrd">Times New Roman</option><option class="svelte-13y1yrd">Georgia</option><option class="svelte-13y1yrd">Courier New</option></select></div> <div class="input-row svelte-13y1yrd"><div class="input-group svelte-13y1yrd"><label class="svelte-13y1yrd">Font Size</label> <input type="number" min="8" max="72" class="svelte-13y1yrd"></div> <div class="input-group svelte-13y1yrd"><label class="svelte-13y1yrd">Font Weight</label> <select class="svelte-13y1yrd"><option class="svelte-13y1yrd">Normal</option><option class="svelte-13y1yrd">Bold</option><option class="svelte-13y1yrd">Light</option></select></div></div> <div class="input-group svelte-13y1yrd"><label class="svelte-13y1yrd">Color</label> <input type="color" class="svelte-13y1yrd"></div></div>'),Qa=c('<div class="property-group svelte-13y1yrd"><h4 class="svelte-13y1yrd">Content</h4> <div class="input-group svelte-13y1yrd"><textarea rows="4" placeholder="Enter content..." class="svelte-13y1yrd"></textarea></div></div>'),Ya=c('<div class="image-preview svelte-13y1yrd"><img alt="Logo preview" class="svelte-13y1yrd"> <!></div>'),Ja=c('<div class="property-group svelte-13y1yrd"><h4 class="svelte-13y1yrd">Logo</h4> <!> <input type="file" accept="image/*" style="display: none;" class="svelte-13y1yrd"> <!></div>'),Ka=c('<div class="field-row svelte-13y1yrd"><input type="text" placeholder="Label" class="field-label svelte-13y1yrd"> <input type="text" placeholder="Value" class="field-value svelte-13y1yrd"> <!></div>'),Xa=c('<div class="property-group svelte-13y1yrd"><h4 class="svelte-13y1yrd">Fields</h4> <!> <!></div>'),Za=c('<div class="property-group svelte-13y1yrd"><h4 class="svelte-13y1yrd">Table Style</h4> <div class="input-group svelte-13y1yrd"><label for="header-background-color" class="svelte-13y1yrd">Header Background</label> <input type="color" id="header-background-color" class="svelte-13y1yrd"></div> <div class="input-group svelte-13y1yrd"><label for="border-color" class="svelte-13y1yrd">Border Color</label> <input type="color" id="border-color" class="svelte-13y1yrd"></div></div>'),Ia=c('<div class="property-section svelte-13y1yrd"><p class="component-name svelte-13y1yrd"> </p> <!> <!> <!> <!> <!></div>'),es=c('<div class="property-section svelte-13y1yrd"><p class="no-selection svelte-13y1yrd">Select a component to edit its properties</p></div>'),ts=c('<div class="component-item svelte-13y1yrd"><span class="column-label svelte-13y1yrd"></span> <span class="component-desc svelte-13y1yrd"> </span></div>'),as=c('<div role="button" tabindex="0"><span class="svelte-13y1yrd">Drop row here</span></div> <div role="button" tabindex="0"><div class="row-header svelte-13y1yrd"><div class="row-drag-handle svelte-13y1yrd" draggable="true" title="Drag to reorder row" role="button" tabindex="0">⋮⋮</div> <h4 class="svelte-13y1yrd"></h4> <!></div> <div class="row-settings svelte-13y1yrd"><div class="input-group svelte-13y1yrd"><select aria-label="Number of columns" class="svelte-13y1yrd"><option class="svelte-13y1yrd">1 Column</option><option class="svelte-13y1yrd">2 Columns</option><option class="svelte-13y1yrd">3 Columns</option></select></div></div> <div class="row-components svelte-13y1yrd"><div class="component-list svelte-13y1yrd"></div></div></div>',1),ss=c('<div class="property-section svelte-13y1yrd"><!> <!> <div role="button" tabindex="0"><span class="svelte-13y1yrd">Drop row here</span></div></div>'),ls=c('<div class="library-component svelte-13y1yrd"><div class="drag-handle svelte-13y1yrd" draggable="true" title="Drag to place component" role="button" tabindex="0">⋮⋮</div> <div class="component-icon svelte-13y1yrd"><!></div> <span class="component-name svelte-13y1yrd"> </span></div>'),rs=c('<div class="property-section svelte-13y1yrd"><p class="section-description svelte-13y1yrd">Drag components into the layout below</p> <div class="component-library svelte-13y1yrd"></div></div>'),os=c('<div class="drag-placeholder svelte-13y1yrd"><span class="svelte-13y1yrd">Drop here</span></div>'),is=c('<div class="title-component svelte-13y1yrd"> </div>'),ns=c('<img alt="Logo" class="svelte-13y1yrd">'),vs=c('<div class="logo-placeholder svelte-13y1yrd">Logo</div>'),ds=c('<div class="logo-component svelte-13y1yrd"><!></div>'),cs=c('<div class="svelte-13y1yrd"> </div>'),ps=c('<div class="text-component svelte-13y1yrd"></div>'),ys=c('<div class="field-row svelte-13y1yrd"><span class="field-label svelte-13y1yrd"> </span> <span class="field-value svelte-13y1yrd"> </span></div>'),us=c('<div class="fields-component svelte-13y1yrd"></div>'),fs=c('<div class="table-component svelte-13y1yrd"><table class="svelte-13y1yrd"><thead class="svelte-13y1yrd"><tr class="svelte-13y1yrd"><th class="svelte-13y1yrd">Description</th><th class="svelte-13y1yrd">Qty</th><th class="svelte-13y1yrd">Rate</th><th class="svelte-13y1yrd">Amount</th></tr></thead><tbody class="svelte-13y1yrd"><tr class="svelte-13y1yrd"><td class="svelte-13y1yrd">Sample Item 1</td><td class="svelte-13y1yrd">1</td><td class="svelte-13y1yrd">$100.00</td><td class="svelte-13y1yrd">$100.00</td></tr><tr class="svelte-13y1yrd"><td class="svelte-13y1yrd">Sample Item 2</td><td class="svelte-13y1yrd">2</td><td class="svelte-13y1yrd">$50.00</td><td class="svelte-13y1yrd">$100.00</td></tr></tbody></table> <div class="totals-section svelte-13y1yrd"><div class="total-row svelte-13y1yrd"><span class="svelte-13y1yrd">Subtotal:</span> <span class="svelte-13y1yrd">$200.00</span></div> <div class="total-row svelte-13y1yrd"><span class="svelte-13y1yrd">Tax (10%):</span> <span class="svelte-13y1yrd">$20.00</span></div> <div class="total-row total svelte-13y1yrd"><span class="svelte-13y1yrd">Total:</span> <span class="svelte-13y1yrd">$220.00</span></div></div></div>'),ms=c('<div role="button" tabindex="0"><div class="component-header svelte-13y1yrd"><div class="drag-handle svelte-13y1yrd" draggable="true" title="Drag to move component" role="button" tabindex="0">⋮⋮</div> <button class="remove-component svelte-13y1yrd" title="Remove component">×</button></div> <div class="component-content svelte-13y1yrd"><!></div></div>'),_s=c('<div class="empty-slot svelte-13y1yrd"><span class="svelte-13y1yrd">Drop component here</span></div>'),gs=c('<div role="button" tabindex="0"><!></div>'),hs=c('<div class="layout-row svelte-13y1yrd"><div class="row-columns svelte-13y1yrd"></div></div>'),bs=c('<main class="designer-main svelte-13y1yrd"><div class="editor-container svelte-13y1yrd"><div class="editor-header svelte-13y1yrd"><h2 class="svelte-13y1yrd"> </h2> <div class="form-group svelte-13y1yrd"><input type="text" id="templateName" placeholder="Enter template name" class="svelte-13y1yrd"></div> <div class="form-group svelte-13y1yrd"><label class="svelte-13y1yrd"><input type="checkbox" class="svelte-13y1yrd"> <span class="checkbox-custom svelte-13y1yrd"></span> Set as default template</label></div> <div class="editor-actions svelte-13y1yrd"><!> <!></div></div> <div class="editor-content svelte-13y1yrd"><div class="properties-panel svelte-13y1yrd"><!> <div class="tab-content svelte-13y1yrd"><!></div></div> <div class="layout-designer svelte-13y1yrd"><div class="a4-canvas svelte-13y1yrd"></div></div></div></div></main>');function Ds(Yt,Bt){fa(Bt,!1);const mt=Pa();let lt=Ea(Bt,"isLoading",8,!1),kt=B([]),_t=B(!1),Z=B(null),Je=B(!1),rt=B(),Se=null,V=B(null),Q=B(null),he=B(null),be=B(null),q=null,pe=null,gt=B(null),Ve=B("properties"),Ke=0;const Jt=[{id:"properties",label:"Properties"},{id:"layout",label:"Layout"},{id:"add",label:"Add"}];let Lt=[{id:"row1",columns:2,components:[["title"],["buyerLogo"]]},{id:"row2",columns:2,components:[["sellerDetails"],["headerFields"]]},{id:"row3",columns:1,components:[["buyerDetails"]]},{id:"row4",columns:1,components:[["lineItems"]]},{id:"row5",columns:2,components:[["terms"],["notes"]]}],ot={title:{id:"title",type:"title",content:"INVOICE",x:0,y:0,width:0,height:0,fontSize:32,fontWeight:"bold",color:"#2563eb",fontFamily:"Arial, sans-serif"},buyerLogo:{id:"buyerLogo",type:"logo",content:"",x:0,y:0,width:0,height:0,logoUrl:""},sellerDetails:{id:"sellerDetails",type:"text",content:`Your Company Name
123 Business Street
City, State 12345
Phone: (*************
Email: <EMAIL>`,x:0,y:0,width:0,height:0,fontSize:12,fontWeight:"normal",color:"#000000",fontFamily:"Arial, sans-serif"},buyerDetails:{id:"buyerDetails",type:"text",content:`Bill To:
Client Company Name
456 Client Street
Client City, State 67890`,x:0,y:0,width:0,height:0,fontSize:12,fontWeight:"normal",color:"#000000",fontFamily:"Arial, sans-serif"},headerFields:{id:"headerFields",type:"fields",content:"",x:0,y:0,width:0,height:0,fontSize:12,fontWeight:"normal",color:"#000000",fontFamily:"Arial, sans-serif",fields:[{label:"Invoice #:",value:"INV-001"},{label:"Date:",value:"2024-01-15"},{label:"Due Date:",value:"2024-02-15"},{label:"Terms:",value:"Net 30"}]},lineItems:{id:"lineItems",type:"table",content:"",x:0,y:0,width:0,height:0,fontSize:12,fontWeight:"normal",color:"#000000",fontFamily:"Arial, sans-serif",headerColor:"#f3f4f6",borderColor:"#e5e7eb"},terms:{id:"terms",type:"text",content:`Terms & Conditions:
Payment is due within 30 days of invoice date. Late payments may incur additional charges.`,x:0,y:0,width:0,height:0,fontSize:10,fontWeight:"normal",color:"#666666",fontFamily:"Arial, sans-serif"},notes:{id:"notes",type:"text",content:`Notes:
Thank you for your business!`,x:0,y:0,width:0,height:0,fontSize:10,fontWeight:"normal",color:"#666666",fontFamily:"Arial, sans-serif"}},r=B({name:"",isDefault:!1,components:{...ot},layout:[...Lt]});async function ht(){try{d(kt,await Na())}catch(t){console.error("Error loading templates:",t),Ye({message:"Failed to load templates",type:"error"})}}ht();function Vt(){d(Z,null),d(_t,!0),Xt()}function Kt(t){d(Z,t),d(_t,!0),y(t)}function Xt(){d(r,{name:"",isDefault:!1,components:{...ot},layout:[...Lt]}),d(V,null)}function y(t){d(r,{name:t.name,isDefault:t.isDefault,components:t.components||{...ot},layout:t.layout||[...Lt]}),It()}function R(){const t={id:`row_${Date.now()}`,columns:1,components:[[]]};ce(r,e(r).layout=[...e(r).layout,t])}function I(t){ce(r,e(r).layout=e(r).layout.filter((i,n)=>n!==t))}function ye(t,i){if(!t.dataTransfer)return;d(he,i),t.dataTransfer.effectAllowed="move",t.dataTransfer.setData("text/plain",i.toString());const n=t.target;n.style.opacity="0.5"}function je(t,i){t.preventDefault(),t.stopPropagation(),e(he)!==null&&(t.dataTransfer&&(t.dataTransfer.dropEffect="move"),q&&(clearTimeout(q),q=null),e(be)!==i&&d(be,i))}function ee(t){t.stopPropagation();const i=t.relatedTarget;t.currentTarget.contains(i)||(q=setTimeout(()=>{d(be,null),q=null},100))}function De(t,i){if(t.preventDefault(),t.stopPropagation(),e(he)===null)return;const n=[...e(r).layout],m=n[e(he)];n.splice(e(he),1);const j=e(he)<i?i-1:i;n.splice(j,0,m),ce(r,e(r).layout=n),d(gt,j),setTimeout(()=>{d(gt,null)},1e3),d(he,null),d(be,null)}function Xe(t){const i=t.target;i.style.opacity="1",d(he,null),d(be,null),q&&(clearTimeout(q),q=null)}function it(t,i){t.preventDefault(),t.stopPropagation(),e(he)!==null&&(t.dataTransfer&&(t.dataTransfer.dropEffect="move"),q&&(clearTimeout(q),q=null),d(be,i))}function jt(t){t.stopPropagation();const i=t.relatedTarget;t.currentTarget.contains(i)||(q=setTimeout(()=>{d(be,null),q=null},100))}function qt(t){d(Ve,t.detail.tabId)}function Gt(t,i){const n=e(r).layout[t],m=n.components.slice(0,i);for(;m.length<i;)m.push([]);ce(r,e(r).layout[t]={...n,columns:i,components:m})}function At(t,i){if(!t.dataTransfer)return;Se=i,t.dataTransfer.effectAllowed="move",t.dataTransfer.setData("text/plain",i);const n=t.target;n.style.opacity="0.5"}function Zt(t,i,n){t.preventDefault(),t.stopPropagation(),Ke++,pe&&(clearTimeout(pe),pe=null),(!e(Q)||e(Q).rowIndex!==i||e(Q).columnIndex!==n)&&d(Q,{rowIndex:i,columnIndex:n})}function Et(t,i,n){t.preventDefault(),t.stopPropagation(),t.dataTransfer&&(t.dataTransfer.dropEffect="move"),(!e(Q)||e(Q).rowIndex!==i||e(Q).columnIndex!==n)&&d(Q,{rowIndex:i,columnIndex:n})}function Nt(t){t.stopPropagation(),Ke--,Ke<=0&&(Ke=0,pe=setTimeout(()=>{Ke===0&&d(Q,null),pe=null},100))}function Mt(t,i,n){var j;t.preventDefault(),t.stopPropagation();const m=((j=t.dataTransfer)==null?void 0:j.getData("text/plain"))||Se;m&&(pe&&(clearTimeout(pe),pe=null),e(r).layout.forEach(ie=>{ie.components.forEach(Y=>{if(Array.isArray(Y)){const $e=Y.indexOf(m);$e>-1&&Y.splice($e,1)}})}),Array.isArray(e(r).layout[i].components[n])||ce(r,e(r).layout[i].components[n]=[]),e(r).layout[i].components[n].push(m),Se=null,d(Q,null),ce(r,e(r).layout=[...e(r).layout]))}function Rt(t){const i=t.target;i.style.opacity="1",Se=null,d(Q,null),Ke=0,pe&&(clearTimeout(pe),pe=null)}function zt(t,i,n){e(r).layout[t].components[i].splice(n,1),ce(r,e(r).layout=[...e(r).layout])}function bt(t){d(V,t),t&&d(Ve,"properties")}function It(){e(r).layout.forEach(t=>{t.components.forEach((i,n)=>{if(!Array.isArray(i)){t.components[n]=[];return}for(let m=i.length-1;m>=0;m--)e(r).components[i[m]]||i.splice(m,1)})}),ce(r,e(r).layout=[...e(r).layout])}function ea(t){var m;const n=(m=t.target.files)==null?void 0:m[0];if(n&&e(V)==="buyerLogo"){const j=new FileReader;j.onload=ie=>{var Y;ce(r,e(r).components.buyerLogo.logoUrl=(Y=ie.target)==null?void 0:Y.result)},j.readAsDataURL(n),Ye({message:"Logo uploaded successfully",type:"success"})}}function ta(){e(r).components.headerFields.fields&&ce(r,e(r).components.headerFields.fields=[...e(r).components.headerFields.fields,{label:"New Field:",value:"Value"}])}function aa(t){e(r).components.headerFields.fields&&ce(r,e(r).components.headerFields.fields=e(r).components.headerFields.fields.filter((i,n)=>n!==t))}function re(){const t=new Set;return e(r).layout.forEach(i=>{i.components.forEach(n=>{Array.isArray(n)&&n.forEach(m=>{t.add(m)})})}),Object.keys(e(r).components).filter(i=>!t.has(i))}function ue(t){var i;if(!t||t.length===0)return"Empty";if(t.length===1){const n=t[0],m=e(r).components[n];if(!m)return"Unknown Component";switch(m.type){case"title":return`Title: "${m.content||"Untitled"}"`;case"logo":return m.logoUrl?"Logo (uploaded)":"Logo (placeholder)";case"text":const j=(m.content||"").split(`
`)[0];return`Text: "${j.length>30?j.substring(0,30)+"...":j||"Empty"}"`;case"fields":return`Fields (${((i=m.fields)==null?void 0:i.length)||0} items)`;case"table":return"Line Items Table";default:return n}}else return`${t.filter(m=>e(r).components[m]).length} components`}async function fe(){if(!e(r).name.trim()){Ye({message:"Template name is required",type:"error"});return}d(Je,!0);try{const t={name:e(r).name,isDefault:e(r).isDefault,components:e(r).components,layout:e(r).layout};e(Z)?(await Ra(e(Z).id,t),Ye({message:"Template updated successfully",type:"success"})):(await za(t),Ye({message:"Template created successfully",type:"success"})),await ht(),Pe(),mt("templateSaved")}catch(t){console.error("Error saving template:",t),Ye({message:"Failed to save template",type:"error"})}finally{d(Je,!1)}}function Pe(){d(_t,!1),d(Z,null),d(V,null)}async function G(t){if(confirm(`Are you sure you want to delete "${t.name}"?`))try{await Ua(t.id),Ye({message:"Template deleted successfully",type:"success"}),await ht(),mt("templateDeleted")}catch(i){console.error("Error deleting template:",i),Ye({message:"Failed to delete template",type:"error"})}}_a();var Ee=St(),nt=st(Ee);{var Dt=t=>{var i=Ha(),n=s(i);ga(n,{}),N(2),a(i),o(t,i)},oe=t=>{var i=St(),n=st(i);{var m=ie=>{var Y=Ga(),$e=s(Y),Ne=s($e),vt=l(s(Ne),2);le(vt,{variant:"primary",$$events:{click:Vt},children:(te,me)=>{N();var z=F("Create New Template");o(te,z)},$$slots:{default:!0}}),a(Ne);var dt=l(Ne,2);{var qe=te=>{var me=Ba(),z=l(s(me),2);le(z,{variant:"primary",$$events:{click:Vt},children:(J,Fe)=>{N();var Ge=F("Create Template");o(J,Ge)},$$slots:{default:!0}}),a(me),o(te,me)},$t=te=>{var me=qa();xe(me,5,()=>e(kt),Ae,(z,J)=>{var Fe=ja(),Ge=s(Fe),Ze=s(Ge),ct=s(Ze,!0);a(Ze),N(2),a(Ge);var Tt=l(Ge,2),wt=s(Tt),sa=s(wt,!0);a(wt);var la=l(wt,2);{var ra=T=>{var k=Va();o(T,k)};f(la,T=>{e(J).isDefault&&T(ra)})}a(Tt);var Ut=l(Tt,2),Wt=s(Ut);le(Wt,{variant:"secondary",size:"small",$$events:{click:()=>Kt(e(J))},children:(T,k)=>{N();var ne=F("Edit");o(T,ne)},$$slots:{default:!0}});var U=l(Wt,2);le(U,{variant:"tertiary",size:"small",$$events:{click:()=>G(e(J))},children:(T,k)=>{N();var ne=F("Delete");o(T,ne)},$$slots:{default:!0}}),a(Ut),a(Fe),S(()=>{var T,k,ne,ae;Pt(Ze,`color: ${((k=(T=e(J).components)==null?void 0:T.title)==null?void 0:k.color)||"#2563eb"}`),M(ct,((ae=(ne=e(J).components)==null?void 0:ne.title)==null?void 0:ae.content)||"INVOICE"),M(sa,e(J).name)}),o(z,Fe)}),a(me),o(te,me)};f(dt,te=>{e(kt).length===0?te(qe):te($t,!1)})}a($e),a(Y),o(ie,Y)},j=ie=>{var Y=bs(),$e=s(Y),Ne=s($e),vt=s(Ne),dt=s(vt,!0);a(vt);var qe=l(vt,2),$t=s(qe);Me($t),a(qe);var te=l(qe,2),me=s(te),z=s(me);Me(z),N(3),a(me),a(te);var J=l(te,2),Fe=s(J);le(Fe,{variant:"tertiary",$$events:{click:Pe},children:(U,T)=>{N();var k=F("Cancel");o(U,k)},$$slots:{default:!0}});var Ge=l(Fe,2);le(Ge,{variant:"primary",get disabled(){return e(Je)},$$events:{click:fe},children:(U,T)=>{N();var k=F();S(()=>M(k,e(Je)?"Saving...":"Save Template")),o(U,k)},$$slots:{default:!0}}),a(J),a(Ne);var Ze=l(Ne,2),ct=s(Ze),Tt=s(ct);ha(Tt,{tabs:Jt,navClass:"editor-tabs",get activeTab(){return e(Ve)},set activeTab(U){d(Ve,U)},$$events:{change:qt},$$legacy:!0});var wt=l(Tt,2),sa=s(wt);{var la=U=>{var T=St(),k=st(T);{var ne=ve=>{var ke=Ia();const v=Be(()=>e(r).components[e(V)]);var L=s(ke),Re=s(L,!0);a(L);var K=l(L,2);{var se=b=>{var u=Ma(),D=l(s(u),2),A=l(s(D),2);S(()=>{e(v),ft(()=>{})});var p=s(A);p.value=p.__value="Arial, sans-serif";var h=l(p);h.value=h.__value="Helvetica, sans-serif";var g=l(h);g.value=g.__value="Times New Roman, serif";var E=l(g);E.value=E.__value="Georgia, serif";var x=l(E);x.value=x.__value="Courier New, monospace",a(A),a(D);var P=l(D,2),_=s(P),H=l(s(_),2);Me(H),a(_);var de=l(_,2),W=l(s(de),2);S(()=>{e(v),ft(()=>{})});var we=s(W);we.value=we.__value="normal";var We=l(we);We.value=We.__value="bold";var Oe=l(We);Oe.value=Oe.__value="lighter",a(W),a(de),a(P);var O=l(P,2),Ie=l(s(O),2);Me(Ie),a(O),a(u),Qt(A,()=>e(v).fontFamily,Le=>e(v).fontFamily=Le),Qe(H,()=>e(v).fontSize,Le=>e(v).fontSize=Le),Qt(W,()=>e(v).fontWeight,Le=>e(v).fontWeight=Le),Qe(Ie,()=>e(v).color,Le=>e(v).color=Le),o(b,u)};f(K,b=>{e(v).type!=="logo"&&b(se)})}var C=l(K,2);{var w=b=>{var u=Qa(),D=l(s(u),2),A=s(D);Ca(A),a(D),a(u),Qe(A,()=>e(v).content,p=>e(v).content=p),o(b,u)};f(C,b=>{(e(v).type==="text"||e(v).type==="title")&&b(w)})}var X=l(C,2);{var ze=b=>{var u=Ja(),D=l(s(u),2);{var A=g=>{var E=Ya(),x=s(E),P=l(x,2);le(P,{variant:"tertiary",size:"small",$$events:{click:()=>{e(r).components[e(v).id]&&(ce(r,e(r).components[e(v).id].logoUrl=""),d(r,{...e(r)}))}},children:(_,H)=>{N();var de=F("Remove");o(_,de)},$$slots:{default:!0}}),a(E),S(()=>ca(x,"src",e(v).logoUrl)),o(g,E)};f(D,g=>{e(v).logoUrl&&g(A)})}var p=l(D,2);La(p,g=>d(rt,g),()=>e(rt));var h=l(p,2);le(h,{variant:"secondary",$$events:{click:()=>e(rt).click()},children:(g,E)=>{N();var x=F("Upload Logo");o(g,x)},$$slots:{default:!0}}),a(u),$("change",p,ea),o(b,u)};f(X,b=>{e(v).type==="logo"&&b(ze)})}var _e=l(X,2);{var ge=b=>{var u=Xa(),D=l(s(u),2);xe(D,1,()=>e(v).fields,Ae,(p,h,g)=>{var E=Ka(),x=s(E);Me(x);var P=l(x,2);Me(P);var _=l(P,2);le(_,{variant:"tertiary",size:"small",$$events:{click:()=>aa(g)},children:(H,de)=>{N();var W=F("×");o(H,W)},$$slots:{default:!0}}),a(E),Qe(x,()=>e(h).label,H=>(e(h).label=H,ft(()=>e(v).fields))),Qe(P,()=>e(h).value,H=>(e(h).value=H,ft(()=>e(v).fields))),o(p,E)});var A=l(D,2);le(A,{variant:"secondary",size:"small",$$events:{click:ta},children:(p,h)=>{N();var g=F("Add Field");o(p,g)},$$slots:{default:!0}}),a(u),o(b,u)};f(_e,b=>{e(v).type==="fields"&&e(v).fields&&b(ge)})}var Te=l(_e,2);{var Ue=b=>{var u=Za(),D=l(s(u),2),A=l(s(D),2);Me(A),a(D);var p=l(D,2),h=l(s(p),2);Me(h),a(p),a(u),Qe(A,()=>e(v).headerColor,g=>e(v).headerColor=g),Qe(h,()=>e(v).borderColor,g=>e(v).borderColor=g),o(b,u)};f(Te,b=>{e(v).type==="table"&&b(Ue)})}a(ke),S(()=>M(Re,e(V))),o(ve,ke)},ae=ve=>{var ke=es();o(ve,ke)};f(k,ve=>{e(V)&&e(r).components[e(V)]?ve(ne):ve(ae,!1)})}o(U,T)},ra=(U,T)=>{{var k=ae=>{var ve=ss(),ke=s(ve);xe(ke,1,()=>e(r).layout,Ae,(K,se,C)=>{var w=as(),X=st(w);let ze;var _e=l(X,2);let ge;var Te=s(_e),Ue=s(Te),b=l(Ue,2);b.textContent=`Row ${C+1}`;var u=l(b,2);le(u,{variant:"tertiary",size:"small",$$events:{click:()=>I(C)},children:(_,H)=>{N();var de=F("×");o(_,de)},$$slots:{default:!0}}),a(Te);var D=l(Te,2),A=s(D),p=s(A);S(()=>{e(se),ft(()=>{})}),ca(p,"id",`columns-select-${C}`);var h=s(p);h.value=h.__value=1;var g=l(h);g.value=g.__value=2;var E=l(g);E.value=E.__value=3,a(p),a(A),a(D);var x=l(D,2),P=s(x);xe(P,5,()=>Array(e(se).columns),Ae,(_,H,de)=>{var W=ts(),we=s(W);we.textContent=`Col ${de+1}:`;var We=l(we,2),Oe=s(We,!0);a(We),a(W),S(O=>M(Oe,O),[()=>ue(e(se).components[de])],Be),o(_,W)}),a(P),a(x),a(_e),S((_,H)=>{ze=Ft(X,1,"row-drop-placeholder svelte-13y1yrd",null,ze,_),ge=Ft(_e,1,"row-control-item svelte-13y1yrd",null,ge,H)},[()=>({active:e(be)===C}),()=>({dragging:e(he)===C,"recently-moved":e(gt)===C})],Be),$("dragover",X,_=>it(_,C)),$("dragleave",X,jt),$("drop",X,_=>De(_,C)),$("dragstart",Ue,_=>ye(_,C)),$("dragend",Ue,Xe),Qt(p,()=>e(se).columns,_=>(e(se).columns=_,ft(()=>e(r).layout))),$("change",p,()=>Gt(C,e(se).columns)),$("dragover",_e,_=>je(_,C)),$("dragleave",_e,ee),$("drop",_e,_=>De(_,C)),o(K,w)});var v=l(ke,2);le(v,{variant:"secondary",$$events:{click:R},children:(K,se)=>{N();var C=F("Add Row");o(K,C)},$$slots:{default:!0}});var L=l(v,2);let Re;a(ve),S(K=>Re=Ft(L,1,"row-drop-placeholder svelte-13y1yrd",null,Re,K),[()=>({active:e(be)===e(r).layout.length})],Be),$("dragover",L,K=>it(K,e(r).layout.length)),$("dragleave",L,jt),$("drop",L,K=>De(K,e(r).layout.length)),o(ae,ve)},ne=(ae,ve)=>{{var ke=v=>{var L=rs(),Re=l(s(L),2);xe(Re,5,re,Ae,(K,se)=>{var C=ls();const w=Be(()=>e(r).components[e(se)]);var X=s(C),ze=l(X,2),_e=s(ze);{var ge=u=>{var D=F("📄");o(u,D)},Te=(u,D)=>{{var A=h=>{var g=F("🖼️");o(h,g)},p=(h,g)=>{{var E=P=>{var _=F("📝");o(P,_)},x=(P,_)=>{{var H=W=>{var we=F("📋");o(W,we)},de=(W,we)=>{{var We=Oe=>{var O=F("📊");o(Oe,O)};f(W,Oe=>{e(w).type==="table"&&Oe(We)},we)}};f(P,W=>{e(w).type==="fields"?W(H):W(de,!1)},_)}};f(h,P=>{e(w).type==="text"?P(E):P(x,!1)},g)}};f(u,h=>{e(w).type==="logo"?h(A):h(p,!1)},D)}};f(_e,u=>{e(w).type==="title"?u(ge):u(Te,!1)})}a(ze);var Ue=l(ze,2),b=s(Ue,!0);a(Ue),a(C),S(()=>M(b,e(se))),$("dragstart",X,u=>At(u,e(se))),$("dragend",X,Rt),o(K,C)}),a(Re),a(L),o(v,L)};f(ae,v=>{e(Ve)==="add"&&v(ke)},ve)}};f(U,ae=>{e(Ve)==="layout"?ae(k):ae(ne,!1)},T)}};f(sa,U=>{e(Ve)==="properties"?U(la):U(ra,!1)})}a(wt),a(ct);var Ut=l(ct,2),Wt=s(Ut);xe(Wt,5,()=>e(r).layout,Ae,(U,T,k)=>{var ne=hs(),ae=s(ne);xe(ae,5,()=>Array(e(T).columns),Ae,(ve,ke,v)=>{var L=gs();let Re;var K=s(L);{var se=w=>{var X=os();o(w,X)},C=(w,X)=>{{var ze=ge=>{var Te=St(),Ue=st(Te);xe(Ue,1,()=>e(T).components[v],Ae,(b,u,D)=>{var A=St();const p=Be(()=>e(r).components[e(u)]);var h=st(A);{var g=E=>{var x=ms();let P;var _=s(x),H=s(_),de=l(H,2);a(_);var W=l(_,2),we=s(W);{var We=O=>{var Ie=is(),Le=s(Ie,!0);a(Ie),S(()=>M(Le,e(p).content)),o(O,Ie)},Oe=(O,Ie)=>{{var Le=Ct=>{var Ot=ds(),oa=s(Ot);{var ia=Ce=>{var tt=ns();S(()=>ca(tt,"src",e(p).logoUrl)),o(Ce,tt)},et=Ce=>{var tt=vs();o(Ce,tt)};f(oa,Ce=>{e(p).logoUrl?Ce(ia):Ce(et,!1)})}a(Ot),o(Ct,Ot)},ba=(Ct,Ot)=>{{var oa=et=>{var Ce=ps();xe(Ce,5,()=>e(p).content.split(`
`),Ae,(tt,na)=>{var He=cs(),pt=s(He,!0);a(He),S(()=>M(pt,e(na))),o(tt,He)}),a(Ce),o(et,Ce)},ia=(et,Ce)=>{{var tt=He=>{var pt=us();xe(pt,5,()=>e(p).fields||[],Ae,(va,xt)=>{var yt=ys(),ut=s(yt),da=s(ut,!0);a(ut);var pa=l(ut,2),Da=s(pa,!0);a(pa),a(yt),S(()=>{M(da,e(xt).label),M(Da,e(xt).value)}),o(va,yt)}),a(pt),o(He,pt)},na=(He,pt)=>{{var va=xt=>{var yt=fs(),ut=s(yt),da=s(ut);N(),a(ut),N(2),a(yt),S(()=>{Pt(ut,`border-color: ${e(p).borderColor??""};`),Pt(da,`background-color: ${e(p).headerColor??""};`)}),o(xt,yt)};f(He,xt=>{e(p).type==="table"&&xt(va)},pt)}};f(et,He=>{e(p).type==="fields"?He(tt):He(na,!1)},Ce)}};f(Ct,et=>{e(p).type==="text"?et(oa):et(ia,!1)},Ot)}};f(O,Ct=>{e(p).type==="logo"?Ct(Le):Ct(ba,!1)},Ie)}};f(we,O=>{e(p).type==="title"?O(We):O(Oe,!1)})}a(W),a(x),S(O=>{P=Ft(x,1,"placed-component svelte-13y1yrd",null,P,O),Pt(W,`
                                font-family: ${e(p).fontFamily||"Arial, sans-serif"};
                                font-size: ${e(p).fontSize||12}px;
                                font-weight: ${e(p).fontWeight||"normal"};
                                color: ${e(p).color||"#000000"};
                              `)},[()=>({selected:e(V)===e(u)})],Be),$("dragstart",H,O=>At(O,e(u))),$("dragend",H,Rt),$("click",de,Aa(()=>zt(k,v,D))),$("click",x,()=>bt(e(u))),$("keydown",x,O=>{(O.key==="Enter"||O.key===" ")&&bt(e(u))}),o(E,x)};f(h,E=>{e(p)&&E(g)})}o(b,A)}),o(ge,Te)},_e=ge=>{var Te=_s();o(ge,Te)};f(w,ge=>{e(T).components[v]&&e(T).components[v].length>0?ge(ze):ge(_e,!1)},X)}};f(K,w=>{e(Q)&&e(Q).rowIndex===k&&e(Q).columnIndex===v?w(se):w(C,!1)})}a(L),S(w=>Re=Ft(L,1,"column-slot svelte-13y1yrd",null,Re,w),[()=>({occupied:e(T).components[v]&&e(T).components[v].length>0})],Be),$("dragenter",L,w=>Zt(w,k,v)),$("dragover",L,w=>Et(w,k,v)),$("dragleave",L,Nt),$("drop",L,w=>Mt(w,k,v)),o(ve,L)}),a(ae),a(ne),S(()=>Pt(ae,`grid-template-columns: repeat(${e(T).columns??""}, 1fr);`)),o(U,ne)}),a(Wt),a(Ut),a(Ze),a($e),a(Y),S(()=>M(dt,e(Z)?"Edit Template":"Create New Template")),Qe($t,()=>e(r).name,U=>ce(r,e(r).name=U)),Sa(z,()=>e(r).isDefault,U=>ce(r,e(r).isDefault=U)),o(ie,Y)};f(n,ie=>{e(_t)?ie(j,!1):ie(m)})}o(t,i)};f(nt,t=>{lt()?t(Dt):t(oe,!1)})}o(Yt,Ee),ma()}var $s=c('<div class="loading-container svelte-106fa2p"><!> <p class="svelte-106fa2p">Loading invoices...</p></div>'),Ts=c("<option> </option>"),ws=c('<div class="empty-state svelte-106fa2p"><h3 class="svelte-106fa2p">No invoices found</h3> <p class="svelte-106fa2p"><!></p> <!></div>'),Cs=c('<div class="table-row svelte-106fa2p" role="button" tabindex="0"><div class="table-cell svelte-106fa2p"><span class="invoice-number svelte-106fa2p"> </span></div> <div class="table-cell svelte-106fa2p"><span class="customer-name svelte-106fa2p">Customer Name</span></div> <div class="table-cell svelte-106fa2p"><span class="date svelte-106fa2p"> </span></div> <div class="table-cell svelte-106fa2p"><span> </span></div> <div class="table-cell svelte-106fa2p"><span class="amount svelte-106fa2p"> </span></div> <div class="table-cell svelte-106fa2p"><span class="status-badge svelte-106fa2p"> </span></div> <div class="table-cell svelte-106fa2p"><!></div></div>'),xs=c('<div class="invoices-table svelte-106fa2p"><div class="table-header svelte-106fa2p"><div class="header-cell svelte-106fa2p">Invoice #</div> <div class="header-cell svelte-106fa2p">Customer</div> <div class="header-cell svelte-106fa2p">Issue Date</div> <div class="header-cell svelte-106fa2p">Due Date</div> <div class="header-cell svelte-106fa2p">Amount</div> <div class="header-cell svelte-106fa2p">Status</div> <div class="header-cell svelte-106fa2p">Actions</div></div> <!></div>'),Ss=c('<div class="stats svelte-106fa2p"><!> <!> <!> <!> <!></div> <div class="controls svelte-106fa2p"><div class="search-section svelte-106fa2p"><input type="text" placeholder="Search invoices..." class="search-input svelte-106fa2p"></div> <div class="filter-section svelte-106fa2p"><label for="status-filter" class="svelte-106fa2p">Status:</label> <select id="status-filter" class="svelte-106fa2p"><option>All</option><!></select></div></div> <!>',1),Ps=c('<main class="svelte-106fa2p"><!></main>'),Fs=c('<div class="container"><!> <!> <!></div>');function tl(Yt,Bt){fa(Bt,!1);const mt=B();let lt=B("invoices");const kt=[{id:"invoices",label:"Invoices"},{id:"designer",label:"Template Designer"}];function _t(y){d(lt,y.detail.tabId)}let Z=B([]),Je=B([]),rt=B(!0),Se=B(""),V=B("All"),Q=!1;Fa(async()=>{await he()});async function he(){d(rt,!0);try{const[y,R]=await Promise.all([Wa(),Oa()]);d(Z,y),d(Je,R)}catch(y){console.error("Error loading invoices:",y),Ye({message:"Failed to load invoices",type:"error"})}finally{d(rt,!1)}}function be(){ya("/invoices/new")}function q(y){ya(`/invoices/${y.id}`)}function pe(y){return at(y).color}function gt(y){return new Date(y).toLocaleDateString()}function Ve(){const y=new Date;return e(Z).filter(R=>{const I=at(R.status);return I.name!=="Paid"&&I.name!=="Cancelled"&&new Date(R.dueDate)<y})}function Ke(){return e(Z).filter(y=>at(y.status).name==="Draft")}function Jt(){return e(Z).filter(y=>at(y.status).name==="Paid")}function Lt(){return e(Z).filter(y=>at(y.status).name==="Paid").reduce((y,R)=>{const I=R.invoiceLines.reduce((ye,je)=>ye+je.total,0);return y+I},0)}$a(()=>(e(Z),e(Se),e(V)),()=>{d(mt,e(Z).filter(y=>{var ee;const I=(((ee=y.invoiceNumber)==null?void 0:ee.toString())||"").toLowerCase().includes(e(Se).toLowerCase()),ye=at(y.status),je=e(V)==="All"||ye.name===e(V);return I&&je}))}),Ta(),_a();var ot=Fs();xa(y=>{wa.title="Invoices"});var r=s(ot);ka(r,{title:"Invoices",$$slots:{actions:(y,R)=>{var I=St(),ye=st(I);{var je=ee=>{le(ee,{variant:"primary",type:"button",$$events:{click:be},children:(De,Xe)=>{N();var it=F("Create Invoice");o(De,it)},$$slots:{default:!0}})};f(ye,ee=>{e(lt)==="invoices"&&ee(je)})}o(y,I)}}});var ht=l(r,2);ha(ht,{tabs:kt,get activeTab(){return e(lt)},$$events:{change:_t}});var Vt=l(ht,2);{var Kt=y=>{var R=Ps(),I=s(R);{var ye=ee=>{var De=$s(),Xe=s(De);ga(Xe,{}),N(2),a(De),o(ee,De)},je=ee=>{var De=Ss(),Xe=st(De),it=s(Xe);const jt=Be(()=>ua(Lt()));Ht(it,{title:"Total Revenue",get value(){return e(jt)}});var qt=l(it,2);Ht(qt,{title:"Total Invoices",get value(){return e(Z).length}});var Gt=l(qt,2);Ht(Gt,{title:"Paid",get value(){return Jt().length}});var At=l(Gt,2);Ht(At,{title:"Overdue",get value(){return Ve().length},valueClass:"overdue"});var Zt=l(At,2);Ht(Zt,{title:"Drafts",get value(){return Ke().length}}),a(Xe);var Et=l(Xe,2),Nt=s(Et),Mt=s(Nt);Me(Mt),a(Nt);var Rt=l(Nt,2),zt=l(s(Rt),2);S(()=>{e(V),ft(()=>{e(Je)})});var bt=s(zt);bt.value=bt.__value="All";var It=l(bt);xe(It,1,()=>e(Je),Ae,(re,ue)=>{var fe=Ts(),Pe={},G=s(fe,!0);a(fe),S(()=>{Pe!==(Pe=e(ue).name)&&(fe.value=(fe.__value=e(ue).name)??""),M(G,e(ue).name)}),o(re,fe)}),a(zt),a(Rt),a(Et);var ea=l(Et,2);{var ta=re=>{var ue=ws(),fe=l(s(ue),2),Pe=s(fe);{var G=oe=>{var t=F("Try adjusting your search or filters.");o(oe,t)},Ee=oe=>{var t=F("Get started by creating your first invoice.");o(oe,t)};f(Pe,oe=>{e(Se)||e(V)!=="All"?oe(G):oe(Ee,!1)})}a(fe);var nt=l(fe,2);{var Dt=oe=>{le(oe,{variant:"primary",$$events:{click:be},children:(t,i)=>{N();var n=F("Create Invoice");o(t,n)},$$slots:{default:!0}})};f(nt,oe=>{!e(Se)&&e(V)==="All"&&oe(Dt)})}a(ue),o(re,ue)},aa=re=>{var ue=xs(),fe=l(s(ue),2);xe(fe,1,()=>e(mt),Pe=>Pe.id,(Pe,G)=>{var Ee=Cs(),nt=s(Ee),Dt=s(nt),oe=s(Dt,!0);a(Dt),a(nt);var t=l(nt,4),i=s(t),n=s(i,!0);a(i),a(t);var m=l(t,2),j=s(m);let ie;var Y=s(j,!0);a(j),a(m);var $e=l(m,2),Ne=s($e),vt=s(Ne,!0);a(Ne),a($e);var dt=l($e,2),qe=s(dt),$t=s(qe,!0);a(qe),a(dt);var te=l(dt,2),me=s(te);le(me,{variant:"secondary",size:"small",$$events:{click:z=>{z.stopPropagation(),q(e(G))}},children:(z,J)=>{N();var Fe=F("View");o(z,Fe)},$$slots:{default:!0}}),a(te),a(Ee),S((z,J,Fe,Ge,Ze,ct)=>{M(oe,e(G).invoiceNumber||"Draft"),M(n,z),ie=Ft(j,1,"date svelte-106fa2p",null,ie,J),M(Y,Fe),M(vt,Ge),Pt(qe,`background-color: ${Ze??""}`),M($t,ct)},[()=>gt(e(G).issueDate),()=>({overdue:new Date(e(G).dueDate)<new Date&&at(e(G).status).name!=="Paid"}),()=>gt(e(G).dueDate),()=>ua(e(G).invoiceLines.reduce((z,J)=>z+J.total,0)),()=>pe(e(G).status),()=>at(e(G).status).name],Be),$("click",Ee,()=>q(e(G))),$("keydown",Ee,z=>{(z.key==="Enter"||z.key===" ")&&(z.preventDefault(),q(e(G)))}),o(Pe,Ee)}),a(ue),o(re,ue)};f(ea,re=>{e(mt).length===0?re(ta):re(aa,!1)})}Qe(Mt,()=>e(Se),re=>d(Se,re)),Qt(zt,()=>e(V),re=>d(V,re)),o(ee,De)};f(I,ee=>{e(rt)?ee(ye):ee(je,!1)})}a(R),o(y,R)},Xt=(y,R)=>{{var I=ye=>{Ds(ye,{isLoading:Q})};f(y,ye=>{e(lt)==="designer"&&ye(I)},R)}};f(Vt,y=>{e(lt)==="invoices"?y(Kt):y(Xt,!1)})}a(ot),o(Yt,ot),ma()}export{tl as component};
