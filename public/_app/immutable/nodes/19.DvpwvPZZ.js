import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as Bt,m as B,u as Ut,v as Jt,f as et,g as Kt,ae as Wt,j as y,i as r,k as e,s as a,t as w,l as m,o as O,r as s,q as S,n as tt}from"../chunks/p3DoyA09.js";import{h as Xt,r as pe,s as z,e as Yt}from"../chunks/BUelSUke.js";import{i as F}from"../chunks/DwdToawP.js";import{e as fe,i as _e}from"../chunks/DEqeA9IH.js";import{t as I,a as u,b as A,e as bt}from"../chunks/B67foYpL.js";import{r as M,s as ye}from"../chunks/DdRd56Yq.js";import{s as Te}from"../chunks/D7jLSc-x.js";import{b as g}from"../chunks/WI3NPOEW.js";import{b as Se}from"../chunks/DSjDIsro.js";import{p as Zt}from"../chunks/Bfc47y5P.js";import{i as ea}from"../chunks/D3pqaimu.js";import{s as ta,a as aa}from"../chunks/qYb16FSw.js";import{o as sa}from"../chunks/C_WNR8j8.js";import{g as $t}from"../chunks/CSyJhG7e.js";import{P as ra}from"../chunks/CC9utfo3.js";import{B as R}from"../chunks/6Zk3JFqZ.js";import{L as ia}from"../chunks/C8F602cz.js";import{M as oa}from"../chunks/BWn8tY11.js";import{a as be}from"../chunks/Ce-0qAhV.js";import{c as la,a as na}from"../chunks/Atsggda0.js";import{f as va,h as da,c as ca,b as ua,i as ma}from"../chunks/SnLwHqso.js";import{C as pa}from"../chunks/CxRlB3U5.js";var fa=I('<div class="loading-container svelte-ljyfvj"><!> <p class="svelte-ljyfvj">Loading quote data...</p></div>'),_a=I("<option> </option>"),ya=I("<option> </option>"),ba=I('<div class="error-message"> </div>'),$a=I('<div class="error-message"> </div>'),ja=I('<div class="section-item svelte-ljyfvj"><div class="section-header-row svelte-ljyfvj"><div class="form-group"><label>Section Title</label> <input type="text" placeholder="Section title"></div> <div class="form-group"><label>Type</label> <select><option>Cover/Preamble</option><option>Observational Data</option><option>Recommendations</option><option>Text Section</option><option>Images</option></select></div> <div class="form-group"><!></div></div> <div class="form-group"><label>Content</label> <textarea rows="4" placeholder="Section content..."></textarea></div></div>'),ga=I('<div class="error-message"> </div>'),Ia=I('<div class="error-message"> </div>'),xa=I('<div class="error-message"> </div>'),ha=I('<div class="items-row svelte-ljyfvj"><div class="item-cell svelte-ljyfvj"><input type="text" placeholder="Item description"> <textarea placeholder="Additional info (optional)" rows="2" class="additional-info svelte-ljyfvj"></textarea> <!></div> <div class="item-cell svelte-ljyfvj"><input type="number" min="1" step="1"> <!></div> <div class="item-cell svelte-ljyfvj"><input type="number" min="0" step="0.01"> <!></div> <div class="item-cell svelte-ljyfvj"><input type="number" min="0" max="100" step="0.1" class="svelte-ljyfvj"></div> <div class="item-cell amount svelte-ljyfvj"> </div> <div class="item-cell actions svelte-ljyfvj"><!></div></div>'),Da=I('<form class="quote-form svelte-ljyfvj"><div class="form-section svelte-ljyfvj"><h2 class="svelte-ljyfvj">Quote Details</h2> <div class="form-row svelte-ljyfvj"><div class="form-group"><label for="status">Status</label> <select id="status"></select></div> <div class="form-group"><label for="template">Template</label> <select id="template"><option>Default Template</option><!></select></div></div> <div class="form-row svelte-ljyfvj"><div class="form-group"><label for="issueDate">Issue Date</label> <input type="date" id="issueDate"> <!></div> <div class="form-group"><label for="expiryDate">Expiry Date</label> <input type="date" id="expiryDate"> <!></div></div></div> <div class="form-section svelte-ljyfvj"><h2 class="svelte-ljyfvj">Customer Information</h2> <!></div> <div class="form-section svelte-ljyfvj"><div class="section-header svelte-ljyfvj"><h2 class="svelte-ljyfvj">Quote Sections</h2> <!></div> <!></div> <div class="form-section svelte-ljyfvj"><h2 class="svelte-ljyfvj">Line Items</h2> <div class="items-table svelte-ljyfvj"><div class="items-header svelte-ljyfvj"><div class="item-cell svelte-ljyfvj">Description</div> <div class="item-cell svelte-ljyfvj">Quantity</div> <div class="item-cell svelte-ljyfvj">Unit Price</div> <div class="item-cell svelte-ljyfvj">Tax Rate (%)</div> <div class="item-cell svelte-ljyfvj">Line Total</div> <div class="item-cell actions svelte-ljyfvj"></div></div> <!> <div class="add-item-row svelte-ljyfvj"><!></div></div> <div class="quote-totals svelte-ljyfvj"><div class="totals-row svelte-ljyfvj"><div class="totals-label">Subtotal:</div> <div class="totals-value"> </div></div> <div class="totals-row svelte-ljyfvj"><div class="totals-label"><label for="discountAmount">Discount:</label></div> <div class="totals-value"><input type="number" id="discountAmount" min="0" step="0.01" class="discount-input svelte-ljyfvj"></div></div> <div class="totals-row svelte-ljyfvj"><div class="totals-label">Tax:</div> <div class="totals-value"> </div></div> <div class="totals-row total svelte-ljyfvj"><div class="totals-label">Total:</div> <div class="totals-value"> </div></div></div></div> <div class="form-section svelte-ljyfvj"><h2 class="svelte-ljyfvj">Additional Information</h2> <div class="form-group"><label for="notes">Notes</label> <textarea id="notes" rows="3" placeholder="Notes to the customer"></textarea></div> <div class="form-group"><label for="terms">Terms and Conditions</label> <textarea id="terms" rows="3"></textarea></div></div> <div class="form-actions svelte-ljyfvj"><!> <!></div></form>'),Aa=I('<div class="array-input svelte-ljyfvj"><input type="text" placeholder="Observation..." class="svelte-ljyfvj"> <!></div>'),qa=I('<div class="array-input svelte-ljyfvj"><input type="text" placeholder="Requirement..." class="svelte-ljyfvj"> <!></div>'),Pa=I('<div class="modal-body"><div class="form-group"><label for="jobType">Job Type</label> <input type="text" id="jobType" placeholder="e.g., Plumbing, Electrical, HVAC"></div> <div class="form-group"><label for="jobDescription">Job Description</label> <textarea id="jobDescription" rows="3" placeholder="Describe the work to be done..."></textarea></div> <div class="form-row svelte-ljyfvj"><div class="form-group"><label for="propertyType">Property Type</label> <select id="propertyType"><option>Residential</option><option>Commercial</option><option>Industrial</option></select></div> <div class="form-group"><label for="urgency">Urgency</label> <select id="urgency"><option>Low</option><option>Medium</option><option>High</option></select></div></div> <div class="form-group"><label>Observations</label> <!> <!></div> <div class="form-group"><label>Requirements</label> <!> <!></div> <div class="form-group"><label for="additionalNotes">Additional Notes</label> <textarea id="additionalNotes" rows="2" placeholder="Any additional information..."></textarea></div></div>'),wa=I('<div slot="footer" class="ai-modal-footer svelte-ljyfvj"><!> <!></div>'),Ta=I('<div class="container"><!> <main><!></main></div> <!>',1);function ts(jt,gt){Bt(gt,!1);const[It,xt]=ta(),at=()=>aa(la,"$customers",It);let $e=B(!1),Re=B(!1),Ge=B([]),ie=B([]),Ve=B(!0),je=B(!1),t=B({customerId:"",issueDate:new Date().toISOString().split("T")[0],expiryDate:new Date(Date.now()+30*24*60*60*1e3).toISOString().split("T")[0],status:{id:"1",name:"Draft",color:"#6B7280"},templateId:"",sections:[Qe("cover","Project Overview",1),Qe("observational","Observations",2),Qe("recommendations","Recommendations",3)],lineItems:[st()],subtotal:0,taxAmount:0,discountAmount:0,totalAmount:0,notes:"",terms:"Quote valid for 30 days. Payment due within 30 days of acceptance."}),n=B({customerInfo:{name:"",address:"",propertyType:"Residential"},jobDetails:{jobType:"",description:"",urgency:"Medium",estimatedDuration:""},observations:[""],requirements:[""],additionalNotes:""}),p=B({}),Q=B(!1);function oe(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}function Qe(l,o,i){return{id:oe(),type:l,title:o,content:"",order:i,isVisible:!0}}function st(){return{id:oe(),description:"",quantity:1,unitPrice:0,taxRate:10,taxAmount:0,lineTotal:0,additionalInfo:""}}function ke(){const l=e(t).lineItems.map(i=>{const b=i.quantity*i.unitPrice,T=b*(i.taxRate/100);return{...i,lineTotal:b,taxAmount:T}}),o=da(l,e(t).discountAmount);y(t,{...e(t),lineItems:l,subtotal:o.subtotal,taxAmount:o.taxAmount,totalAmount:o.totalAmount})}function ht(){const l=Qe("text","New Section",e(t).sections.length+1);y(t,{...e(t),sections:[...e(t).sections,l]})}function Dt(l){y(t,{...e(t),sections:e(t).sections.filter((o,i)=>i!==l)})}function At(){y(t,{...e(t),lineItems:[...e(t).lineItems,st()]})}function qt(l){y(t,{...e(t),lineItems:e(t).lineItems.filter((o,i)=>i!==l)}),ke()}function Pt(){m(n,e(n).observations=[...e(n).observations,""])}function wt(l){m(n,e(n).observations=e(n).observations.filter((o,i)=>i!==l))}function Tt(){m(n,e(n).requirements=[...e(n).requirements,""])}function St(l){m(n,e(n).requirements=e(n).requirements.filter((o,i)=>i!==l))}async function Rt(){y(Re,!0);try{const l=at().find(i=>i.id===e(t).customerId);if(l){m(n,e(n).customerInfo.name=l.companyName||l.fullName);const i=l.addresses.find(b=>b.isPrimary);i&&m(n,e(n).customerInfo.address=`${i.street}, ${i.city}, ${i.state}`)}const o=await va(e(n));m(t,e(t).sections=[{id:oe(),type:"cover",title:"Project Overview",content:o.coverSection,order:1,isVisible:!0},{id:oe(),type:"observational",title:"Observations",content:o.observationalData,order:2,isVisible:!0},{id:oe(),type:"recommendations",title:"Recommendations",content:o.recommendations,order:3,isVisible:!0}]),m(t,e(t).lineItems=o.lineItems.map(i=>({id:oe(),description:i.description,quantity:i.quantity,unitPrice:i.unitPrice,taxRate:10,taxAmount:0,lineTotal:0,additionalInfo:i.notes||""}))),m(t,e(t).terms=o.terms),ke(),y(je,!1),be({message:"AI quote generated successfully!",type:"success"})}catch(l){console.error("Error generating AI quote:",l),be({message:"Failed to generate AI quote",type:"error"})}finally{y(Re,!1)}}function Qt(){return y(p,{}),e(t).customerId||m(p,e(p).customerId="Please select a customer"),e(t).issueDate||m(p,e(p).issueDate="Issue date is required"),e(t).expiryDate||m(p,e(p).expiryDate="Expiry date is required"),e(t).lineItems.forEach((l,o)=>{l.description||m(p,e(p)[`lineItems[${o}].description`]="Description is required"),l.quantity<=0&&m(p,e(p)[`lineItems[${o}].quantity`]="Quantity must be greater than 0"),l.unitPrice<0&&m(p,e(p)[`lineItems[${o}].unitPrice`]="Unit price cannot be negative")}),Object.keys(e(p)).length===0}async function kt(){var l;if(y(Q,!0),ke(),!Qt()){be({message:"Please fix the errors in the form before submitting",type:"error"});return}y($e,!0);try{const o=at().find($=>$.id===e(t).customerId),i=o==null?void 0:o.addresses.find($=>$.isPrimary),b={customerId:e(t).customerId,customerName:(o==null?void 0:o.companyName)||(o==null?void 0:o.fullName),customerEmail:(l=o==null?void 0:o.emails.find($=>$.isPrimary))==null?void 0:l.email,customerAddress:i?{street:i.street,city:i.city,state:i.state,zipCode:i.zipCode,country:i.country}:void 0,issueDate:e(t).issueDate,expiryDate:e(t).expiryDate,status:e(t).status,templateId:e(t).templateId||void 0,sections:e(t).sections,lineItems:e(t).lineItems,subtotal:e(t).subtotal,taxAmount:e(t).taxAmount,discountAmount:e(t).discountAmount,totalAmount:e(t).totalAmount,notes:e(t).notes,terms:e(t).terms},T=await ma(b);be({message:"Quote created successfully",type:"success"}),$t("/quotes")}catch(o){console.error("Error creating quote:",o),be({message:o instanceof Error?o.message:"An unknown error occurred",type:"error"})}finally{y($e,!1)}}function Nt(){$t("/quotes")}async function Ot(){y(Ve,!0);try{await na.loadContacts();const[l,o]=await Promise.all([ca(),ua()]);y(Ge,l),y(ie,o),e(ie).length>0&&m(t,e(t).status=e(ie).find(i=>i.name==="Draft")||e(ie)[0])}catch(l){console.error("Error loading data:",l),be({message:"Failed to load data",type:"error"})}finally{y(Ve,!1)}}sa(async()=>{await Ot()}),Ut(()=>e(t),()=>{e(t).lineItems&&ke()}),Jt(),ea();var rt=Ta();Xt(l=>{Wt.title="Create Quote"});var He=et(rt),it=r(He);ra(it,{title:"Create Quote",$$slots:{actions:(l,o)=>{R(l,{variant:"secondary",$$events:{click:()=>y(je,!0)},children:(i,b)=>{O();var T=A("AI Generate");u(i,T)},$$slots:{default:!0}})}}});var ot=a(it,2),Ct=r(ot);{var Et=l=>{var o=fa(),i=r(o);ia(i,{}),O(2),s(o),u(l,o)},Lt=l=>{var o=Da(),i=r(o),b=a(r(i),2),T=r(b),$=a(r(T),2);w(()=>{e(t),S(()=>{e(ie)})}),fe($,5,()=>e(ie),_e,(d,c)=>{var v=_a(),j={},h=r(v,!0);s(v),w(()=>{j!==(j=e(c))&&(v.value=(v.__value=e(c))??""),z(h,e(c).name)}),u(d,v)}),s($),s(T);var Y=a(T,2),k=a(r(Y),2);w(()=>{e(t),S(()=>{e(Ge)})});var U=r(k);U.value=U.__value="";var Z=a(U);fe(Z,1,()=>e(Ge),_e,(d,c)=>{var v=ya(),j={},h=r(v,!0);s(v),w(()=>{j!==(j=e(c).id)&&(v.value=(v.__value=e(c).id)??""),z(h,e(c).name)}),u(d,v)}),s(k),s(Y),s(b);var J=a(b,2),q=r(J),C=a(r(q),2);M(C);let ge;var Ie=a(C,2);{var xe=d=>{var c=ba(),v=r(c,!0);s(c),w(()=>z(v,e(p).issueDate)),u(d,c)};F(Ie,d=>{e(Q)&&e(p).issueDate&&d(xe)})}s(q);var le=a(q,2),ee=a(r(le),2);M(ee);let ne;var Ne=a(ee,2);{var Be=d=>{var c=$a(),v=r(c,!0);s(c),w(()=>z(v,e(p).expiryDate)),u(d,c)};F(Ne,d=>{e(Q)&&e(p).expiryDate&&d(Be)})}s(le),s(J),s(i);var te=a(i,2),Oe=a(r(te),2);const Ue=tt(()=>e(Q)&&!!e(p).customerId);pa(Oe,{get hasError(){return e(Ue)},get errorMessage(){return e(p).customerId},get customerId(){return e(t).customerId},set customerId(d){m(t,e(t).customerId=d)},get customerSearch(){return e(n).customerInfo.name},set customerSearch(d){m(n,e(n).customerInfo.name=d)},$$legacy:!0}),s(te);var ve=a(te,2),de=r(ve),_=a(r(de),2);R(_,{variant:"secondary",size:"small",$$events:{click:ht},children:(d,c)=>{O();var v=A("Add Section");u(d,v)},$$slots:{default:!0}}),s(de);var he=a(de,2);fe(he,1,()=>e(t).sections,_e,(d,c,v)=>{var j=ja(),h=r(j),V=r(h),N=r(V);ye(N,"for",`sectionTitle${v}`);var L=a(N,2);M(L),ye(L,"id",`sectionTitle${v}`),s(V);var qe=a(V,2),Ee=r(qe);ye(Ee,"for",`sectionType${v}`);var K=a(Ee,2);w(()=>{e(c),S(()=>{})}),ye(K,"id",`sectionType${v}`);var W=r(K);W.value=W.__value="cover";var ue=a(W);ue.value=ue.__value="observational";var Pe=a(ue);Pe.value=Pe.__value="recommendations";var we=a(Pe);we.value=we.__value="text";var me=a(we);me.value=me.__value="images",s(K),s(qe);var se=a(qe,2),Le=r(se);R(Le,{variant:"tertiary",size:"small",$$events:{click:()=>Dt(v)},children:(H,Me)=>{O();var Ye=A("Remove");u(H,Ye)},$$slots:{default:!0}}),s(se),s(h);var ze=a(h,2),Fe=r(ze);ye(Fe,"for",`sectionContent${v}`);var re=a(Fe,2);pe(re),ye(re,"id",`sectionContent${v}`),s(ze),s(j),g(L,()=>e(c).title,H=>(e(c).title=H,S(()=>e(t).sections))),Se(K,()=>e(c).type,H=>(e(c).type=H,S(()=>e(t).sections))),g(re,()=>e(c).content,H=>(e(c).content=H,S(()=>e(t).sections))),u(d,j)}),s(ve);var x=a(ve,2),E=a(r(x),2),G=a(r(E),2);fe(G,1,()=>e(t).lineItems,_e,(d,c,v)=>{var j=ha(),h=r(j),V=r(h);M(V);let N;var L=a(V,2);pe(L);var qe=a(L,2);{var Ee=f=>{var D=ga(),X=r(D,!0);s(D),w(()=>z(X,e(p)[`lineItems[${v}].description`])),u(f,D)};F(qe,f=>{e(Q)&&e(p)[`lineItems[${v}].description`]&&f(Ee)})}s(h);var K=a(h,2),W=r(K);M(W);let ue;var Pe=a(W,2);{var we=f=>{var D=Ia(),X=r(D,!0);s(D),w(()=>z(X,e(p)[`lineItems[${v}].quantity`])),u(f,D)};F(Pe,f=>{e(Q)&&e(p)[`lineItems[${v}].quantity`]&&f(we)})}s(K);var me=a(K,2),se=r(me);M(se);let Le;var ze=a(se,2);{var Fe=f=>{var D=xa(),X=r(D,!0);s(D),w(()=>z(X,e(p)[`lineItems[${v}].unitPrice`])),u(f,D)};F(ze,f=>{e(Q)&&e(p)[`lineItems[${v}].unitPrice`]&&f(Fe)})}s(me);var re=a(me,2),H=r(re);M(H),s(re);var Me=a(re,2),Ye=r(Me);s(Me);var yt=a(Me,2),Vt=r(yt);{var Ht=f=>{R(f,{variant:"tertiary",size:"small",$$events:{click:()=>qt(v)},children:(D,X)=>{O();var Ze=A("Remove");u(D,Ze)},$$slots:{default:!0}})};F(Vt,f=>{e(t).lineItems.length>1&&f(Ht)})}s(yt),s(j),w((f,D,X,Ze)=>{N=Te(V,1,"svelte-ljyfvj",null,N,f),ue=Te(W,1,"svelte-ljyfvj",null,ue,D),Le=Te(se,1,"svelte-ljyfvj",null,Le,X),z(Ye,`$${Ze??""}`)},[()=>({error:e(Q)&&e(p)[`lineItems[${v}].description`]}),()=>({error:e(Q)&&e(p)[`lineItems[${v}].quantity`]}),()=>({error:e(Q)&&e(p)[`lineItems[${v}].unitPrice`]}),()=>e(c).lineTotal.toFixed(2)],tt),g(V,()=>e(c).description,f=>(e(c).description=f,S(()=>e(t).lineItems))),g(L,()=>e(c).additionalInfo,f=>(e(c).additionalInfo=f,S(()=>e(t).lineItems))),g(W,()=>e(c).quantity,f=>(e(c).quantity=f,S(()=>e(t).lineItems))),g(se,()=>e(c).unitPrice,f=>(e(c).unitPrice=f,S(()=>e(t).lineItems))),g(H,()=>e(c).taxRate,f=>(e(c).taxRate=f,S(()=>e(t).lineItems))),u(d,j)});var ce=a(G,2),De=r(ce);R(De,{variant:"secondary",size:"small",$$events:{click:At},children:(d,c)=>{O();var v=A("Add Item");u(d,v)},$$slots:{default:!0}}),s(ce),s(E);var P=a(E,2),ae=r(P),Ce=a(r(ae),2),Ae=r(Ce);s(Ce),s(ae);var Je=a(ae,2),lt=a(r(Je),2),nt=r(lt);M(nt),s(lt),s(Je);var Ke=a(Je,2),vt=a(r(Ke),2),Ft=r(vt);s(vt),s(Ke);var dt=a(Ke,2),ct=a(r(dt),2),Mt=r(ct);s(ct),s(dt),s(P),s(x);var We=a(x,2),Xe=a(r(We),2),ut=a(r(Xe),2);pe(ut),s(Xe);var mt=a(Xe,2),pt=a(r(mt),2);pe(pt),s(mt),s(We);var ft=a(We,2),_t=r(ft);R(_t,{variant:"tertiary",get disabled(){return e($e)},$$events:{click:Nt},children:(d,c)=>{O();var v=A("Cancel");u(d,v)},$$slots:{default:!0}});var Gt=a(_t,2);R(Gt,{type:"submit",get disabled(){return e($e)},children:(d,c)=>{var v=bt(),j=et(v);{var h=N=>{var L=A("Saving...");u(N,L)},V=N=>{var L=A("Save Quote");u(N,L)};F(j,N=>{e($e)?N(h):N(V,!1)})}u(d,v)},$$slots:{default:!0}}),s(ft),s(o),w((d,c,v,j,h)=>{ge=Te(C,1,"",null,ge,d),ne=Te(ee,1,"",null,ne,c),z(Ae,`$${v??""}`),z(Ft,`$${j??""}`),z(Mt,`$${h??""}`)},[()=>({error:e(Q)&&e(p).issueDate}),()=>({error:e(Q)&&e(p).expiryDate}),()=>e(t).subtotal.toFixed(2),()=>e(t).taxAmount.toFixed(2),()=>e(t).totalAmount.toFixed(2)],tt),Se($,()=>e(t).status,d=>m(t,e(t).status=d)),Se(k,()=>e(t).templateId,d=>m(t,e(t).templateId=d)),g(C,()=>e(t).issueDate,d=>m(t,e(t).issueDate=d)),g(ee,()=>e(t).expiryDate,d=>m(t,e(t).expiryDate=d)),g(nt,()=>e(t).discountAmount,d=>m(t,e(t).discountAmount=d)),g(ut,()=>e(t).notes,d=>m(t,e(t).notes=d)),g(pt,()=>e(t).terms,d=>m(t,e(t).terms=d)),Yt("submit",o,Zt(kt)),u(l,o)};F(Ct,l=>{e(Ve)?l(Et):l(Lt,!1)})}s(ot),s(He);var zt=a(He,2);oa(zt,{title:"AI Quote Generation",get show(){return e(je)},set show(l){y(je,l)},children:(l,o)=>{var i=Pa(),b=r(i),T=a(r(b),2);M(T),s(b);var $=a(b,2),Y=a(r($),2);pe(Y),s($);var k=a($,2),U=r(k),Z=a(r(U),2);w(()=>{e(n),S(()=>{})});var J=r(Z);J.value=J.__value="Residential";var q=a(J);q.value=q.__value="Commercial";var C=a(q);C.value=C.__value="Industrial",s(Z),s(U);var ge=a(U,2),Ie=a(r(ge),2);w(()=>{e(n),S(()=>{})});var xe=r(Ie);xe.value=xe.__value="Low";var le=a(xe);le.value=le.__value="Medium";var ee=a(le);ee.value=ee.__value="High",s(Ie),s(ge),s(k);var ne=a(k,2),Ne=a(r(ne),2);fe(Ne,1,()=>e(n).observations,_e,(_,he,x)=>{var E=Aa(),G=r(E);M(G);var ce=a(G,2);{var De=P=>{R(P,{variant:"tertiary",size:"small",$$events:{click:()=>wt(x)},children:(ae,Ce)=>{O();var Ae=A("Remove");u(ae,Ae)},$$slots:{default:!0}})};F(ce,P=>{e(n).observations.length>1&&P(De)})}s(E),g(G,()=>e(n).observations[x],P=>m(n,e(n).observations[x]=P)),u(_,E)});var Be=a(Ne,2);R(Be,{variant:"secondary",size:"small",$$events:{click:Pt},children:(_,he)=>{O();var x=A("Add Observation");u(_,x)},$$slots:{default:!0}}),s(ne);var te=a(ne,2),Oe=a(r(te),2);fe(Oe,1,()=>e(n).requirements,_e,(_,he,x)=>{var E=qa(),G=r(E);M(G);var ce=a(G,2);{var De=P=>{R(P,{variant:"tertiary",size:"small",$$events:{click:()=>St(x)},children:(ae,Ce)=>{O();var Ae=A("Remove");u(ae,Ae)},$$slots:{default:!0}})};F(ce,P=>{e(n).requirements.length>1&&P(De)})}s(E),g(G,()=>e(n).requirements[x],P=>m(n,e(n).requirements[x]=P)),u(_,E)});var Ue=a(Oe,2);R(Ue,{variant:"secondary",size:"small",$$events:{click:Tt},children:(_,he)=>{O();var x=A("Add Requirement");u(_,x)},$$slots:{default:!0}}),s(te);var ve=a(te,2),de=a(r(ve),2);pe(de),s(ve),s(i),g(T,()=>e(n).jobDetails.jobType,_=>m(n,e(n).jobDetails.jobType=_)),g(Y,()=>e(n).jobDetails.description,_=>m(n,e(n).jobDetails.description=_)),Se(Z,()=>e(n).customerInfo.propertyType,_=>m(n,e(n).customerInfo.propertyType=_)),Se(Ie,()=>e(n).jobDetails.urgency,_=>m(n,e(n).jobDetails.urgency=_)),g(de,()=>e(n).additionalNotes,_=>m(n,e(n).additionalNotes=_)),u(l,i)},$$slots:{default:!0,footer:(l,o)=>{var i=wa(),b=r(i);R(b,{variant:"tertiary",$$events:{click:()=>y(je,!1)},children:($,Y)=>{O();var k=A("Cancel");u($,k)},$$slots:{default:!0}});var T=a(b,2);R(T,{get disabled(){return e(Re)},$$events:{click:Rt},children:($,Y)=>{var k=bt(),U=et(k);{var Z=q=>{var C=A("Generating...");u(q,C)},J=q=>{var C=A("Generate Quote");u(q,C)};F(U,q=>{e(Re)?q(Z):q(J,!1)})}u($,k)},$$slots:{default:!0}}),s(i),u(l,i)}},$$legacy:!0}),u(jt,rt),Kt(),xt()}export{ts as component};
