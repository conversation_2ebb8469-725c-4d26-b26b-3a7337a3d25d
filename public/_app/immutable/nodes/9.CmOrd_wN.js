import{r as _s}from"../chunks/DGCjcLwA.js";import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{t as I,e as fs,g as hs,ae as Cs,i as s,j as te,m as se,l as h,k as e,o as y,s as i,r as t,f as js,n as A}from"../chunks/p3DoyA09.js";import{h as ys,s as g,e as Je}from"../chunks/BUelSUke.js";import{i as G}from"../chunks/DwdToawP.js";import{e as ae,i as ie}from"../chunks/DEqeA9IH.js";import{n as Te,a as d,t as $,b as L}from"../chunks/B67foYpL.js";import{c as le}from"../chunks/DTrhvrmD.js";import{s as B,d as oe}from"../chunks/DdRd56Yq.js";import{i as $s}from"../chunks/D3pqaimu.js";import{p as O,s as bs,a as Jt}from"../chunks/qYb16FSw.js";import{o as ws}from"../chunks/C_WNR8j8.js";import{B as M}from"../chunks/6Zk3JFqZ.js";import{P as xs}from"../chunks/CC9utfo3.js";import{L as Ds}from"../chunks/C8F602cz.js";import{S as m}from"../chunks/BYzA1sSu.js";import{a as Is}from"../chunks/Ce-0qAhV.js";import{g as k}from"../chunks/CSyJhG7e.js";import{f as E}from"../chunks/Chsk6cZE.js";import{s as Le}from"../chunks/D7jLSc-x.js";import{I as Qs}from"../chunks/CtFnnJUz.js";import{k as Ss,a as ve}from"../chunks/DJsmiAoD.js";import{a as ks}from"../chunks/SnLwHqso.js";import{h as Js}from"../chunks/D9cKHHet.js";import{g as Ts}from"../chunks/BXh2naGf.js";import{s as Ls,a as Ms}from"../chunks/BGnz0MpO.js";import{a as Ns,d as As}from"../chunks/Atsggda0.js";var Hs=Te('<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path></svg>');function Vs(N,Q){let x=O(Q,"size",8,"24"),H=O(Q,"className",8,"");var _=Hs();I(()=>{B(_,"width",x()),B(_,"height",x()),Le(_,0,`icon-check ${H()??""}`)}),d(N,_)}const zs=()=>{if(!localStorage.getItem("user"))throw _s(302,"/login");return{}},La=Object.freeze(Object.defineProperty({__proto__:null,load:zs},Symbol.toStringTag,{value:"Module"}));var Ps=Te('<svg viewBox="0 0 18 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M2.26999 7.91C2.26999 4.18579 5.29578 1.16 9.01999 1.16C12.7342 1.16 15.77 4.19579 15.77 7.91V10.8C15.77 11.0149 15.8186 11.3294 15.9108 11.6654C16.0018 11.9974 16.1194 12.2924 16.2265 12.4797L17.3731 14.3841C18.2787 15.8934 17.5548 17.8595 15.8865 18.4117C11.4226 19.9029 6.6062 19.9028 2.14233 18.4114L2.13955 18.4104C1.29714 18.1253 0.665379 17.5358 0.382911 16.7729C0.100115 16.0092 0.196316 15.1496 0.657354 14.3833L1.80578 12.476C1.80604 12.4755 1.80629 12.4751 1.80654 12.4747C1.91605 12.2906 2.036 11.9966 2.12857 11.6631C2.22142 11.3287 2.26999 11.0146 2.26999 10.8V7.91ZM9.01999 2.66C6.12421 2.66 3.76999 5.01422 3.76999 7.91V10.8C3.76999 11.1954 3.68857 11.6513 3.57392 12.0644C3.4592 12.4776 3.29449 12.9081 3.0942 13.2441L3.09252 13.2469L1.94263 15.1567C1.94259 15.1567 1.94267 15.1566 1.94263 15.1567C1.69378 15.5704 1.67989 15.9558 1.78957 16.2521C1.8995 16.5489 2.16234 16.8342 2.6191 16.9891C6.77445 18.3771 11.2571 18.377 15.4123 16.9887L15.4146 16.9879C16.1653 16.7397 16.5009 15.8468 16.0872 15.1564C16.0871 15.1562 16.0873 15.1566 16.0872 15.1564L14.9302 13.2349C14.7381 12.9019 14.5773 12.4744 14.4642 12.0621C14.3514 11.6507 14.27 11.1951 14.27 10.8V7.91C14.27 5.02422 11.9058 2.66 9.01999 2.66Z" fill="#292D32"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M6.47171 1.92635C6.87115 0.907067 7.86286 0.190002 9.02 0.190002C10.1771 0.190002 11.1688 0.907067 11.5683 1.92635C11.6726 2.19242 11.6166 2.49463 11.4239 2.70568C11.2312 2.91673 10.9353 2.99994 10.6609 2.92026C10.3835 2.83972 10.1035 2.77895 9.81975 2.74455L9.81697 2.74421C8.95256 2.63616 8.1358 2.70029 7.37936 2.92019C7.10489 2.99997 6.80894 2.91683 6.6162 2.70577C6.42345 2.49472 6.36742 2.19246 6.47171 1.92635Z" fill="#292D32"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M6.76999 18.06C6.76999 18.6725 7.02479 19.2441 7.43032 19.6497C7.83584 20.0552 8.40747 20.31 9.01999 20.31C10.2558 20.31 11.27 19.2958 11.27 18.06H12.77C12.77 20.1242 11.0842 21.81 9.01999 21.81C7.99251 21.81 7.04413 21.3848 6.36966 20.7103C5.69518 20.0359 5.26999 19.0875 5.26999 18.06H6.76999Z" fill="#292D32"></path></svg>');function Rs(N,Q){let x=O(Q,"size",8,"24"),H=O(Q,"className",8,"");var _=Ps();I(()=>{B(_,"width",x()),B(_,"height",x()),Le(_,0,H())}),d(N,_)}var Es=Te('<svg viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M5.79001 5.75C5.54768 5.75 5.33123 5.75944 5.13303 5.78422C5.12334 5.78543 5.11363 5.78645 5.1039 5.78728C3.89531 5.89062 3.09368 6.28721 2.58266 6.8883C2.06344 7.49903 1.75 8.43122 1.75 9.79001V13.79C1.75 15.7255 2.14374 16.6413 2.69207 17.1201C3.2656 17.6209 4.20732 17.83 5.79001 17.83H6.19C6.45271 17.83 6.69761 17.9137 6.88205 18.0069C7.06697 18.1003 7.27317 18.2449 7.42576 18.4444L7.43001 18.45L8.63101 20.0514C8.77703 20.247 8.91454 20.2825 8.99001 20.2825C9.06547 20.2825 9.20298 20.247 9.349 20.0514L9.35001 20.05L10.55 18.45C10.8416 18.0611 11.3039 17.83 11.79 17.83H12.19C13.55 17.83 14.482 17.5185 15.0922 17.0006C15.6925 16.4913 16.0893 15.6904 16.1927 14.4763C16.1935 14.4665 16.1946 14.4568 16.1958 14.447C16.2206 14.2488 16.23 14.0323 16.23 13.79V9.79001C16.23 8.31121 15.8597 7.33787 15.2509 6.72908C14.6421 6.12029 13.6688 5.75 12.19 5.75H5.79001ZM4.96177 4.29397C5.2387 4.26021 5.51719 4.25 5.79001 4.25H12.19C13.9012 4.25 15.3229 4.67971 16.3116 5.66842C17.3003 6.65714 17.73 8.0788 17.73 9.79001V13.79C17.73 14.0628 17.7198 14.3413 17.686 14.6182C17.5574 16.0975 17.0443 17.3114 16.0628 18.1444C15.088 18.9715 13.76 19.33 12.19 19.33H11.79C11.7848 19.33 11.7769 19.3313 11.7679 19.3358C11.759 19.3403 11.7531 19.3459 11.75 19.35L10.551 20.9486C10.5509 20.9489 10.5512 20.9484 10.551 20.9486C10.1671 21.4627 9.60928 21.7825 8.99001 21.7825C8.37073 21.7825 7.81345 21.4633 7.42948 20.9493C7.42931 20.9491 7.42964 20.9495 7.42948 20.9493L6.24465 19.3695C6.23609 19.3631 6.22253 19.3542 6.20544 19.3456C6.19004 19.3378 6.1769 19.3328 6.16804 19.33H5.79001C4.1827 19.33 2.72941 19.1441 1.70544 18.2499C0.656266 17.3338 0.25 15.8545 0.25 13.79V9.79001C0.25 8.21879 0.611563 6.89098 1.43984 5.91672C2.27363 4.93598 3.48684 4.42272 4.96177 4.29397Z" fill="#292D32"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M5.92374 4.25H12.19C13.9012 4.25 15.3229 4.67971 16.3116 5.66842C17.3003 6.65714 17.73 8.0788 17.73 9.79001V13.6567C18.4787 13.4728 19.0185 13.1404 19.3977 12.695C19.9163 12.086 20.23 11.1544 20.23 9.79001V5.79001C20.23 4.31121 19.8597 3.33787 19.2509 2.72908C18.6421 2.12029 17.6688 1.75 16.19 1.75H9.79001C8.43123 1.75 7.49904 2.06344 6.8883 2.58266C6.44184 2.96223 6.1082 3.50211 5.92374 4.25ZM5.91672 1.43984C6.89099 0.611563 8.2188 0.25 9.79001 0.25H16.19C17.9012 0.25 19.3229 0.67971 20.3116 1.66842C21.3003 2.65714 21.73 4.0788 21.73 5.79001V9.79001C21.73 11.3657 21.3687 12.694 20.5398 13.6675C19.7028 14.6505 18.484 15.1607 17.0039 15.2873C16.7784 15.3066 16.5563 15.223 16.3994 15.0599C16.2426 14.8968 16.1677 14.6715 16.1958 14.447C16.2206 14.2488 16.23 14.0323 16.23 13.79V9.79001C16.23 8.31121 15.8597 7.33787 15.2509 6.72908C14.6421 6.12029 13.6688 5.75 12.19 5.75H5.79001C5.54769 5.75 5.33124 5.75944 5.13304 5.78422C4.90848 5.81229 4.68326 5.73746 4.52014 5.58059C4.35702 5.42373 4.27346 5.2016 4.29274 4.97612C4.4194 3.4947 4.93281 2.27633 5.91672 1.43984Z" fill="#292D32"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M11.4955 12.25C11.4955 11.6977 11.9432 11.25 12.4955 11.25H12.5045C13.0568 11.25 13.5045 11.6977 13.5045 12.25C13.5045 12.8023 13.0568 13.25 12.5045 13.25H12.4955C11.9432 13.25 11.4955 12.8023 11.4955 12.25Z" fill="#292D32"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M7.9955 12.25C7.9955 11.6977 8.44321 11.25 8.9955 11.25H9.0045C9.55678 11.25 10.0045 11.6977 10.0045 12.25C10.0045 12.8023 9.55678 13.25 9.0045 13.25H8.9955C8.44321 13.25 7.9955 12.8023 7.9955 12.25Z" fill="#292D32"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M4.4955 12.25C4.4955 11.6977 4.94321 11.25 5.4955 11.25H5.5045C6.05678 11.25 6.5045 11.6977 6.5045 12.25C6.5045 12.8023 6.05678 13.25 5.5045 13.25H5.4955C4.94321 13.25 4.4955 12.8023 4.4955 12.25Z" fill="#292D32"></path></svg>');function Zs(N,Q){let x=O(Q,"size",8,"24"),H=O(Q,"className",8,"");var _=Es();I(()=>{B(_,"width",x()),B(_,"height",x()),Le(_,0,H())}),d(N,_)}var qs=$('<div class="loading-container svelte-ogjs1l"><!> <p class="svelte-ogjs1l">Loading dashboard...</p></div>'),Bs=$('<div class="activity-item svelte-ogjs1l" role="button" tabindex="0"><div class="activity-info svelte-ogjs1l"><span class="activity-title svelte-ogjs1l"> </span> <span class="activity-subtitle svelte-ogjs1l">Customer</span></div> <div class="activity-meta svelte-ogjs1l"><span class="activity-amount svelte-ogjs1l"> </span> <span class="activity-status svelte-ogjs1l"> </span></div></div>'),Os=$('<div class="empty-activity svelte-ogjs1l">No recent invoices</div>'),Us=$('<div class="activity-item svelte-ogjs1l" role="button" tabindex="0"><div class="activity-info svelte-ogjs1l"><span class="activity-title svelte-ogjs1l"> </span> <span class="activity-subtitle svelte-ogjs1l"> </span></div> <div class="activity-meta svelte-ogjs1l"><span class="activity-amount svelte-ogjs1l"> </span> <span class="activity-status svelte-ogjs1l"> </span></div></div>'),Fs=$('<div class="empty-activity svelte-ogjs1l">No recent quotes</div>'),Ws=$('<div class="activity-item svelte-ogjs1l" role="button" tabindex="0"><div class="activity-info svelte-ogjs1l"><span class="activity-title svelte-ogjs1l"> </span> <span class="activity-subtitle svelte-ogjs1l"> </span></div> <div class="activity-meta svelte-ogjs1l"><span class="activity-date svelte-ogjs1l"> </span> <span class="activity-status svelte-ogjs1l"> </span></div></div>'),Ys=$('<div class="empty-activity svelte-ogjs1l">No upcoming jobs</div>'),Gs=$('<div class="activity-item svelte-ogjs1l"><div class="activity-info svelte-ogjs1l"><span class="activity-title svelte-ogjs1l"> </span> <span class="activity-subtitle svelte-ogjs1l"> </span></div> <div class="activity-meta svelte-ogjs1l"><span class="activity-date svelte-ogjs1l"> </span> <span class="activity-status svelte-ogjs1l"> </span></div></div>'),Ks=$('<div class="empty-activity svelte-ogjs1l">No upcoming events</div>'),Xs=$('<div class="alert-item overdue svelte-ogjs1l"><!> <span> </span></div>'),ea=$('<div class="alert-item pending svelte-ogjs1l"><!> <span> </span></div>'),ta=$('<div class="alert-item busy svelte-ogjs1l"><!> <span> </span></div>'),sa=$('<div class="alert-item success svelte-ogjs1l"><!> <span>Everything looks good!</span></div>'),aa=$(`<div class="welcome-section svelte-ogjs1l"><div class="welcome-card svelte-ogjs1l"><h2 class="svelte-ogjs1l">Welcome back! 👋</h2> <p class="svelte-ogjs1l">Here's what's happening with your business today.</p> <div class="quick-actions svelte-ogjs1l"><!> <!> <!> <!></div></div></div> <div class="section svelte-ogjs1l"><h3 class="section-title svelte-ogjs1l">💰 Financial Overview</h3> <div class="stats-grid svelte-ogjs1l"><!> <!> <!> <!> <!></div></div> <div class="section svelte-ogjs1l"><h3 class="section-title svelte-ogjs1l">📊 Sales & Quotes</h3> <div class="stats-grid svelte-ogjs1l"><!> <!> <!> <!></div></div> <div class="section svelte-ogjs1l"><h3 class="section-title svelte-ogjs1l">🔧 Operations</h3> <div class="stats-grid svelte-ogjs1l"><!> <!> <!> <!></div></div> <div class="section svelte-ogjs1l"><h3 class="section-title svelte-ogjs1l">👥 Team & Contacts</h3> <div class="stats-grid svelte-ogjs1l"><!> <!> <!> <!> <!></div></div> <div class="activity-section svelte-ogjs1l"><div class="activity-grid svelte-ogjs1l"><div class="activity-card svelte-ogjs1l"><div class="activity-header svelte-ogjs1l"><h4 class="svelte-ogjs1l">📄 Recent Invoices</h4> <!></div> <div class="activity-list svelte-ogjs1l"></div></div> <div class="activity-card svelte-ogjs1l"><div class="activity-header svelte-ogjs1l"><h4 class="svelte-ogjs1l">💼 Recent Quotes</h4> <!></div> <div class="activity-list svelte-ogjs1l"></div></div> <div class="activity-card svelte-ogjs1l"><div class="activity-header svelte-ogjs1l"><h4 class="svelte-ogjs1l">🔧 Upcoming Jobs</h4> <!></div> <div class="activity-list svelte-ogjs1l"></div></div> <div class="activity-card svelte-ogjs1l"><div class="activity-header svelte-ogjs1l"><h4 class="svelte-ogjs1l">📅 This Week's Schedule</h4> <!></div> <div class="activity-list svelte-ogjs1l"></div></div></div></div> <div class="insights-section svelte-ogjs1l"><h3 class="section-title">📈 Performance Insights</h3> <div class="insights-grid svelte-ogjs1l"><div class="insight-card svelte-ogjs1l"><h4 class="svelte-ogjs1l">💡 Business Health</h4> <div class="insight-content svelte-ogjs1l"><div class="insight-metric svelte-ogjs1l"><span class="metric-label svelte-ogjs1l">Invoice Collection Rate</span> <span class="metric-value svelte-ogjs1l"> </span></div> <div class="insight-metric svelte-ogjs1l"><span class="metric-label svelte-ogjs1l">Quote Conversion Rate</span> <span class="metric-value svelte-ogjs1l"> </span></div> <div class="insight-metric svelte-ogjs1l"><span class="metric-label svelte-ogjs1l">Job Completion Rate</span> <span class="metric-value svelte-ogjs1l"> </span></div></div></div> <div class="insight-card svelte-ogjs1l"><h4 class="svelte-ogjs1l">⚠️ Attention Needed</h4> <div class="insight-content svelte-ogjs1l"><!> <!> <!> <!></div></div> <div class="insight-card svelte-ogjs1l"><h4 class="svelte-ogjs1l">🎯 Quick Stats</h4> <div class="insight-content svelte-ogjs1l"><div class="quick-stat svelte-ogjs1l"><span class="stat-icon svelte-ogjs1l">💰</span> <div><div class="stat-value svelte-ogjs1l"> </div> <div class="stat-label svelte-ogjs1l">Avg Invoice Value</div></div></div> <div class="quick-stat svelte-ogjs1l"><span class="stat-icon svelte-ogjs1l">📊</span> <div><div class="stat-value svelte-ogjs1l"> </div> <div class="stat-label svelte-ogjs1l">Avg Quote Value</div></div></div> <div class="quick-stat svelte-ogjs1l"><span class="stat-icon svelte-ogjs1l">👥</span> <div><div class="stat-value svelte-ogjs1l"> </div> <div class="stat-label svelte-ogjs1l">Jobs per Staff</div></div></div></div></div></div></div>`,1),ia=$('<div class="container"><!> <main class="dashboard-content svelte-ogjs1l"><!></main></div>');function Ma(N,Q){fs(Q,!1);const[x,H]=bs(),_=()=>Jt(Ms,"$staff",x),re=()=>Jt(As,"$contacts",x);let ne=se(!0),Me=!0,Ne=!0,Ae=!0,He=!0,U=[],V=se([]),F=[],ce=se([]),a=se({totalRevenue:0,monthlyRevenue:0,totalInvoices:0,paidInvoices:0,overdueInvoices:0,totalQuotes:0,acceptedQuotes:0,pendingQuotes:0,totalJobs:0,completedJobs:0,activeJobs:0,totalStaff:0,activeStaff:0,totalContacts:0,customers:0,leads:0,upcomingAppointments:0});ws(async()=>{await Tt()});async function Tt(){te(ne,!0);try{await Promise.all([Lt(),Mt(),Nt(),At(),Ht(),Vt()]),zt()}catch(v){console.error("Error loading dashboard data:",v),Is({message:"Failed to load dashboard data",type:"error"})}finally{te(ne,!1)}}async function Lt(){try{Me=!0,U=await Ss()}catch(v){console.error("Error loading invoices:",v)}finally{Me=!1}}async function Mt(){try{Ne=!0,te(V,await ks())}catch(v){console.error("Error loading quotes:",v)}finally{Ne=!1}}async function Nt(){try{Ae=!0,F=await Js()}catch(v){console.error("Error loading jobs:",v)}finally{Ae=!1}}async function At(){try{He=!0;const v=new Date,p=new Date(v.getTime()+7*24*60*60*1e3);te(ce,await Ts(v.toISOString().split("T")[0],p.toISOString().split("T")[0]))}catch(v){console.error("Error loading calendar events:",v)}finally{He=!1}}async function Ht(){try{await Ls.loadStaff()}catch(v){console.error("Error loading staff:",v)}}async function Vt(){try{await Ns.loadContacts()}catch(v){console.error("Error loading contacts:",v)}}function zt(){const v=U.filter(n=>ve(n.status).name==="Paid"),p=U.filter(n=>{const w=new Date,J=ve(n.status);return J.name!=="Paid"&&J.name!=="Cancelled"&&new Date(n.dueDate)<w});h(a,e(a).totalRevenue=v.reduce((n,w)=>{const J=w.invoiceLines.reduce((q,Y)=>q+Y.total,0);return n+J},0)),h(a,e(a).totalInvoices=U.length),h(a,e(a).paidInvoices=v.length),h(a,e(a).overdueInvoices=p.length);const D=new Date().getMonth(),W=new Date().getFullYear();h(a,e(a).monthlyRevenue=v.filter(n=>{const w=new Date(n.issueDate);return w.getMonth()===D&&w.getFullYear()===W}).reduce((n,w)=>{const J=w.invoiceLines.reduce((q,Y)=>q+Y.total,0);return n+J},0));const Z=e(V).filter(n=>n.status.name==="Accepted"),K=e(V).filter(n=>n.status.name==="Sent"||n.status.name==="Draft");h(a,e(a).totalQuotes=e(V).length),h(a,e(a).acceptedQuotes=Z.length),h(a,e(a).pendingQuotes=K.length);const X=F.filter(n=>n.status.isCompleted),ee=F.filter(n=>!n.status.isCompleted&&n.status.name!=="Cancelled");h(a,e(a).totalJobs=F.length),h(a,e(a).completedJobs=X.length),h(a,e(a).activeJobs=ee.length),h(a,e(a).totalStaff=_().length),h(a,e(a).activeStaff=_().filter(n=>n.isActive).length),h(a,e(a).totalContacts=re().length),h(a,e(a).customers=re().filter(n=>n.status==="Customer").length),h(a,e(a).leads=re().filter(n=>n.status==="Lead").length),h(a,e(a).upcomingAppointments=e(ce).length)}function Pt(){return U.sort((v,p)=>new Date(p.issueDate).getTime()-new Date(v.issueDate).getTime()).slice(0,5)}function Rt(){return e(V).sort((v,p)=>new Date(p.createdAt).getTime()-new Date(v.createdAt).getTime()).slice(0,5)}function Et(){return F.filter(v=>v.scheduledDateTime&&new Date(v.scheduledDateTime)>new Date).sort((v,p)=>new Date(v.scheduledDateTime).getTime()-new Date(p.scheduledDateTime).getTime()).slice(0,5)}function Ve(v){return new Date(v).toLocaleString()}function de(v){return typeof v=="number"?ve(v).color:v.color||"#6B7280"}function Zt(v){return v.invoiceLines.reduce((p,D)=>p+D.total,0)}function ze(){k("/invoices/new")}function qt(){k("/quotes/create")}function Bt(){k("/jobs/create")}function Pe(){k("/calendar")}$s();var ue=ia();ys(v=>{Cs.title="Dashboard"});var Re=s(ue);xs(Re,{title:"Dashboard",$$slots:{actions:(v,p)=>{M(v,{variant:"primary",$$events:{click:ze},children:(D,W)=>{y();var Z=L("Create Invoice");d(D,Z)},$$slots:{default:!0}})}}});var Ee=i(Re,2),Ot=s(Ee);{var Ut=v=>{var p=qs(),D=s(p);Ds(D,{}),y(2),t(p),d(v,p)},Ft=v=>{var p=aa(),D=js(p),W=s(D),Z=i(s(W),4),K=s(Z);M(K,{variant:"primary",size:"small",$$events:{click:ze},children:(o,l)=>{y();var r=L("New Invoice");d(o,r)},$$slots:{default:!0}});var X=i(K,2);M(X,{variant:"primary",size:"small",$$events:{click:qt},children:(o,l)=>{y();var r=L("New Quote");d(o,r)},$$slots:{default:!0}});var ee=i(X,2);M(ee,{variant:"primary",size:"small",$$events:{click:Bt},children:(o,l)=>{y();var r=L("New Job");d(o,r)},$$slots:{default:!0}});var n=i(ee,2);M(n,{variant:"primary",size:"small",$$events:{click:Pe},children:(o,l)=>{y();var r=L("View Calendar");d(o,r)},$$slots:{default:!0}}),t(Z),t(W),t(D);var w=i(D,2),J=i(s(w),2),q=s(J);const Y=A(()=>E(e(a).totalRevenue));m(q,{title:"Total Revenue",get value(){return e(Y)}});var Ze=i(q,2);const Wt=A(()=>E(e(a).monthlyRevenue));m(Ze,{title:"Monthly Revenue",get value(){return e(Wt)}});var qe=i(Ze,2);m(qe,{title:"Total Invoices",get value(){return e(a).totalInvoices}});var Be=i(qe,2);m(Be,{title:"Paid Invoices",get value(){return e(a).paidInvoices}});var Yt=i(Be,2);m(Yt,{title:"Overdue Invoices",get value(){return e(a).overdueInvoices},valueClass:"overdue"}),t(J),t(w);var ge=i(w,2),Oe=i(s(ge),2),Ue=s(Oe);m(Ue,{title:"Total Quotes",get value(){return e(a).totalQuotes}});var Fe=i(Ue,2);m(Fe,{title:"Accepted Quotes",get value(){return e(a).acceptedQuotes}});var We=i(Fe,2);m(We,{title:"Pending Quotes",get value(){return e(a).pendingQuotes}});var Gt=i(We,2);const Kt=A(()=>E(e(V).filter(o=>o.status.name==="Accepted").reduce((o,l)=>o+l.totalAmount,0)));m(Gt,{title:"Quote Value",get value(){return e(Kt)}}),t(Oe),t(ge);var pe=i(ge,2),Ye=i(s(pe),2),Ge=s(Ye);m(Ge,{title:"Total Jobs",get value(){return e(a).totalJobs}});var Ke=i(Ge,2);m(Ke,{title:"Active Jobs",get value(){return e(a).activeJobs}});var Xe=i(Ke,2);m(Xe,{title:"Completed Jobs",get value(){return e(a).completedJobs}});var Xt=i(Xe,2);m(Xt,{title:"Upcoming Appointments",get value(){return e(a).upcomingAppointments}}),t(Ye),t(pe);var me=i(pe,2),et=i(s(me),2),tt=s(et);m(tt,{title:"Total Staff",get value(){return e(a).totalStaff}});var st=i(tt,2);m(st,{title:"Active Staff",get value(){return e(a).activeStaff}});var at=i(st,2);m(at,{title:"Total Contacts",get value(){return e(a).totalContacts}});var it=i(at,2);m(it,{title:"Customers",get value(){return e(a).customers}});var es=i(it,2);m(es,{title:"Leads",get value(){return e(a).leads}}),t(et),t(me);var _e=i(me,2),lt=s(_e),fe=s(lt),he=s(fe),ts=i(s(he),2);M(ts,{variant:"tertiary",size:"small",$$events:{click:()=>k("/invoices")},children:(o,l)=>{y();var r=L("View All");d(o,r)},$$slots:{default:!0}}),t(he);var ot=i(he,2);ae(ot,5,Pt,ie,(o,l)=>{var r=Bs(),c=s(r),u=s(c),C=s(u,!0);t(u),y(2),t(c);var f=i(c,2),T=s(f),S=s(T,!0);t(T);var j=i(T,2),z=s(j,!0);t(j),t(f),t(r),I((b,P,R)=>{g(C,e(l).invoiceNumber||"Draft"),g(S,b),oe(j,`color: ${P??""}`),g(z,R)},[()=>E(Zt(e(l))),()=>de(e(l).status),()=>ve(e(l).status).name],A),Je("click",r,()=>k(`/invoices/${e(l).id}`)),d(o,r)},o=>{var l=Os();d(o,l)}),t(ot),t(fe);var Ce=i(fe,2),je=s(Ce),ss=i(s(je),2);M(ss,{variant:"tertiary",size:"small",$$events:{click:()=>k("/quotes")},children:(o,l)=>{y();var r=L("View All");d(o,r)},$$slots:{default:!0}}),t(je);var vt=i(je,2);ae(vt,5,Rt,ie,(o,l)=>{var r=Us(),c=s(r),u=s(c),C=s(u,!0);t(u);var f=i(u,2),T=s(f,!0);t(f),t(c);var S=i(c,2),j=s(S),z=s(j,!0);t(j);var b=i(j,2),P=s(b,!0);t(b),t(S),t(r),I((R,ke)=>{g(C,e(l).quoteNumber),g(T,e(l).customerName),g(z,R),oe(b,`color: ${ke??""}`),g(P,e(l).status.name)},[()=>E(e(l).totalAmount),()=>de(e(l).status)],A),Je("click",r,()=>k(`/quotes/${e(l).id}`)),d(o,r)},o=>{var l=Fs();d(o,l)}),t(vt),t(Ce);var ye=i(Ce,2),$e=s(ye),as=i(s($e),2);M(as,{variant:"tertiary",size:"small",$$events:{click:()=>k("/jobs")},children:(o,l)=>{y();var r=L("View All");d(o,r)},$$slots:{default:!0}}),t($e);var rt=i($e,2);ae(rt,5,Et,ie,(o,l)=>{var r=Ws(),c=s(r),u=s(c),C=s(u,!0);t(u);var f=i(u,2),T=s(f,!0);t(f),t(c);var S=i(c,2),j=s(S),z=s(j,!0);t(j);var b=i(j,2),P=s(b,!0);t(b),t(S),t(r),I((R,ke)=>{g(C,e(l).title),g(T,e(l).customerName),g(z,R),oe(b,`color: ${ke??""}`),g(P,e(l).status.name)},[()=>Ve(e(l).scheduledDateTime),()=>de(e(l).status)],A),Je("click",r,()=>k(`/jobs/${e(l).id}`)),d(o,r)},o=>{var l=Ys();d(o,l)}),t(rt),t(ye);var nt=i(ye,2),be=s(nt),is=i(s(be),2);M(is,{variant:"tertiary",size:"small",$$events:{click:Pe},children:(o,l)=>{y();var r=L("View Calendar");d(o,r)},$$slots:{default:!0}}),t(be);var ct=i(be,2);ae(ct,5,()=>e(ce).slice(0,5),ie,(o,l)=>{var r=Gs(),c=s(r),u=s(c),C=s(u,!0);t(u);var f=i(u,2),T=s(f,!0);t(f),t(c);var S=i(c,2),j=s(S),z=s(j,!0);t(j);var b=i(j,2),P=s(b,!0);t(b),t(S),t(r),I(R=>{g(C,e(l).title),g(T,e(l).customerName),g(z,R),oe(b,`color: ${e(l).color||"#6B7280"}`),g(P,e(l).status)},[()=>Ve(e(l).startDateTime)],A),d(o,r)},o=>{var l=Ks();d(o,l)}),t(ct),t(nt),t(lt),t(_e);var dt=i(_e,2),ut=i(s(dt),2),we=s(ut),gt=i(s(we),2),xe=s(gt),pt=i(s(xe),2),ls=s(pt);t(pt),t(xe);var De=i(xe,2),mt=i(s(De),2),os=s(mt);t(mt),t(De);var _t=i(De,2),ft=i(s(_t),2),vs=s(ft);t(ft),t(_t),t(gt),t(we);var Ie=i(we,2),ht=i(s(Ie),2),Ct=s(ht);{var rs=o=>{var l=Xs(),r=s(l);le(r,()=>Rs,(C,f)=>{f(C,{size:"24",className:"alert-icon"})});var c=i(r,2),u=s(c);t(c),t(l),I(()=>g(u,`${e(a).overdueInvoices??""} overdue invoice${e(a).overdueInvoices>1?"s":""}`)),d(o,l)};G(Ct,o=>{e(a).overdueInvoices>0&&o(rs)})}var jt=i(Ct,2);{var ns=o=>{var l=ea(),r=s(l);le(r,()=>Zs,(C,f)=>{f(C,{size:"24",className:"alert-icon"})});var c=i(r,2),u=s(c);t(c),t(l),I(()=>g(u,`${e(a).pendingQuotes??""} pending quote${e(a).pendingQuotes>1?"s":""}`)),d(o,l)};G(jt,o=>{e(a).pendingQuotes>0&&o(ns)})}var yt=i(jt,2);{var cs=o=>{var l=ta(),r=s(l);le(r,()=>Qs,(C,f)=>{f(C,{size:"24",className:"alert-icon"})});var c=i(r,2),u=s(c);t(c),t(l),I(()=>g(u,`High job volume: ${e(a).activeJobs??""} active jobs`)),d(o,l)};G(yt,o=>{e(a).activeJobs>5&&o(cs)})}var ds=i(yt,2);{var us=o=>{var l=sa(),r=s(l);le(r,()=>Vs,(c,u)=>{u(c,{size:"24",className:"alert-icon"})}),y(2),t(l),d(o,l)};G(ds,o=>{e(a).overdueInvoices===0&&e(a).pendingQuotes===0&&e(a).activeJobs<=5&&o(us)})}t(ht),t(Ie);var $t=i(Ie,2),bt=i(s($t),2),Qe=s(bt),wt=i(s(Qe),2),xt=s(wt),gs=s(xt,!0);t(xt),y(2),t(wt),t(Qe);var Se=i(Qe,2),Dt=i(s(Se),2),It=s(Dt),ps=s(It,!0);t(It),y(2),t(Dt),t(Se);var Qt=i(Se,2),St=i(s(Qt),2),kt=s(St),ms=s(kt,!0);t(kt),y(2),t(St),t(Qt),t(bt),t($t),t(ut),t(dt),I((o,l,r,c,u,C)=>{g(ls,`${o??""}%`),g(os,`${l??""}%`),g(vs,`${r??""}%`),g(gs,c),g(ps,u),g(ms,C)},[()=>e(a).totalInvoices>0?Math.round(e(a).paidInvoices/e(a).totalInvoices*100):0,()=>e(a).totalQuotes>0?Math.round(e(a).acceptedQuotes/e(a).totalQuotes*100):0,()=>e(a).totalJobs>0?Math.round(e(a).completedJobs/e(a).totalJobs*100):0,()=>E(e(a).totalRevenue/Math.max(e(a).paidInvoices,1)),()=>E(e(V).filter(o=>o.status.name==="Accepted").reduce((o,l)=>o+l.totalAmount,0)/Math.max(e(a).acceptedQuotes,1)),()=>Math.round(e(a).totalJobs/Math.max(e(a).activeStaff,1))],A),d(v,p)};G(Ot,v=>{e(ne)?v(Ut):v(Ft,!1)})}t(Ee),t(ue),d(N,ue),hs(),H()}export{Ma as component,La as universal};
