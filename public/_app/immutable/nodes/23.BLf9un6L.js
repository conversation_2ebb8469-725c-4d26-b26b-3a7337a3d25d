import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{s as r,i as t,t as U,k as a,m as h,q,r as e,j as S,e as E,g as F,ae as H}from"../chunks/p3DoyA09.js";import{s as k,e as N,h as Y}from"../chunks/BUelSUke.js";import{t as w,a as C}from"../chunks/B67foYpL.js";import{i as L}from"../chunks/DwdToawP.js";import{V as z}from"../chunks/Bg6zarrZ.js";import{e as K,i as O}from"../chunks/DEqeA9IH.js";import{b as Q}from"../chunks/DSjDIsro.js";import{r as j,s as T}from"../chunks/DdRd56Yq.js";import{b as I}from"../chunks/WI3NPOEW.js";import{i as W}from"../chunks/D3pqaimu.js";import{P as X}from"../chunks/CC9utfo3.js";var Z=w("<option> </option>"),ee=w('<div class="settings-section svelte-1lt3vrw"><h2 class="svelte-1lt3vrw">General Settings</h2> <div class="setting-item svelte-1lt3vrw"><label for="currency-select" class="svelte-1lt3vrw">Currency</label> <p class="setting-description svelte-1lt3vrw">Choose the default currency for your application.</p> <select id="currency-select" class="svelte-1lt3vrw"></select></div></div>');function te(y){let i=h("USD");const p=["USD","EUR","GBP","JPY","CAD","AUD"];var v=ee(),o=r(t(v),2),g=r(t(o),4);U(()=>{a(i),q(()=>{})}),K(g,5,()=>p,O,(n,c)=>{var l=Z(),f={},_=t(l,!0);e(l),U(()=>{f!==(f=a(c))&&(l.value=(l.__value=a(c))??""),k(_,a(c))}),C(n,l)}),e(g),e(o),e(v),Q(g,()=>a(i),n=>S(i,n)),C(y,v)}var ae=w('<div class="logo-preview svelte-y5fgc0"><img alt="Brand Logo Preview" class="svelte-y5fgc0"></div>'),se=w('<div class="settings-section svelte-y5fgc0"><h2 class="svelte-y5fgc0">Brand Settings</h2> <div class="setting-item svelte-y5fgc0"><label for="logo-upload" class="svelte-y5fgc0">Brand Logo</label> <p class="setting-description svelte-y5fgc0">Upload your company logo. Recommended format: SVG, PNG, JPG.</p> <input type="file" id="logo-upload" accept="image/svg+xml, image/png, image/jpeg" class="svelte-y5fgc0"> <!></div> <div class="setting-item svelte-y5fgc0"><label for="primary-color" class="svelte-y5fgc0">Primary Brand Color</label> <p class="setting-description svelte-y5fgc0">Choose the main color for your brand identity.</p> <div class="color-picker-container svelte-y5fgc0"><input type="color" id="primary-color" class="svelte-y5fgc0"> <span class="color-value svelte-y5fgc0"> </span></div></div> <div class="setting-item svelte-y5fgc0"><label for="secondary-color" class="svelte-y5fgc0">Secondary Brand Color</label> <p class="setting-description svelte-y5fgc0">Choose an accent or secondary color.</p> <div class="color-picker-container svelte-y5fgc0"><input type="color" id="secondary-color" class="svelte-y5fgc0"> <span class="color-value svelte-y5fgc0"> </span></div></div></div>');function re(y,i){E(i,!1);let p=h(null),v=h("#007bff"),o=h("#6c757d");function g(d){const m=d.target;if(m.files&&m.files[0]){const D=m.files[0],A=new FileReader;A.onload=M=>{var V;S(p,((V=M.target)==null?void 0:V.result)||null)},A.readAsDataURL(D)}}W();var n=se(),c=r(t(n),2),l=r(t(c),4),f=r(l,2);{var _=d=>{var m=ae(),D=t(m);e(m),U(()=>T(D,"src",a(p))),C(d,m)};L(f,d=>{a(p)&&typeof a(p)=="string"&&d(_)})}e(c);var b=r(c,2),B=r(t(b),4),s=t(B);j(s);var P=r(s,2),x=t(P,!0);e(P),e(B),e(b);var u=r(b,2),R=r(t(u),4),G=t(R);j(G);var $=r(G,2),J=t($,!0);e($),e(R),e(u),e(n),U(()=>{k(x,a(v)),k(J,a(o))}),N("change",l,g),I(s,()=>a(v),d=>S(v,d)),I(G,()=>a(o),d=>S(o,d)),C(y,n),F()}var le=w('<div class="container"><!> <main><div class="vertical-sidebar-layout"><!> <div class="vertical-sidebar-content"><!></div></div></main></div>');function be(y){let i=h("general");const p=[{id:"general",label:"General"},{id:"brand",label:"Brand"}];function v(s){S(i,s)}var o=le();Y(s=>{H.title="Settings"});var g=t(o);X(g,{title:"Settings"});var n=r(g,2),c=t(n),l=t(c);z(l,{items:p,get currentItem(){return a(i)},onSelect:v});var f=r(l,2),_=t(f);{var b=s=>{te(s)},B=(s,P)=>{{var x=u=>{re(u,{})};L(s,u=>{a(i)==="brand"&&u(x)},P)}};L(_,s=>{a(i)==="general"?s(b):s(B,!1)})}e(f),e(c),e(n),e(o),C(y,o)}export{be as component};
