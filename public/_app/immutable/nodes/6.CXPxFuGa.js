import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as Ta,u as rt,v as $a,f as wt,g as ka,ae as Ea,j as f,m as ne,k as e,i as r,o as Z,s as o,r as a,t as h,n as W,q as ot,l as C,af as zt}from"../chunks/p3DoyA09.js";import{h as xa,s as p,r as Ia,e as N}from"../chunks/BUelSUke.js";import{i as G}from"../chunks/DwdToawP.js";import{e as _e,i as Te}from"../chunks/DEqeA9IH.js";import{t as g,a as d,b as le}from"../chunks/B67foYpL.js";import{r as ke,s as Ut,b as Jt}from"../chunks/DdRd56Yq.js";import{s as Ee}from"../chunks/D7jLSc-x.js";import{b as Re,a as Ca,c as Ma}from"../chunks/WI3NPOEW.js";import{b as it}from"../chunks/DSjDIsro.js";import{p as Ha,s as nt}from"../chunks/Bfc47y5P.js";import{i as ja}from"../chunks/D3pqaimu.js";import{M as Pa,b as qt}from"../chunks/BWn8tY11.js";import{s as Oa,a as St}from"../chunks/qYb16FSw.js";import{o as Na}from"../chunks/C_WNR8j8.js";import{B as ve}from"../chunks/6Zk3JFqZ.js";import{P as Aa}from"../chunks/CC9utfo3.js";import{L as Ra}from"../chunks/C8F602cz.js";import{C as La}from"../chunks/CxRlB3U5.js";import{a as ee}from"../chunks/Ce-0qAhV.js";import{j as Fa,a as za}from"../chunks/CE5QyBL8.js";import{s as Ua,a as Ja}from"../chunks/BGnz0MpO.js";import{c as qa}from"../chunks/Atsggda0.js";import{g as Ba,u as Bt,c as Wt,d as Wa}from"../chunks/BXh2naGf.js";import{g as Ga}from"../chunks/CSyJhG7e.js";var Ya=g('<div class="loading-container svelte-1o0m66v"><!> <p class="svelte-1o0m66v">Loading calendar...</p></div>'),Ka=g('<div class="selection-actions svelte-1o0m66v"><!> <!> <!></div>'),Qa=g('<div class="paste-info svelte-1o0m66v"><span class="copied-count svelte-1o0m66v"> </span> <span class="paste-hint svelte-1o0m66v">Click on a date to paste</span></div>'),Va=g("<option> </option>"),Xa=g('<div class="day-header svelte-1o0m66v"> </div>'),Za=g('<div class="selection-checkbox svelte-1o0m66v"><input type="checkbox" class="svelte-1o0m66v"></div>'),es=g('<div draggable="true" role="button" tabindex="0"><!> <span class="event-title svelte-1o0m66v"> </span> <span class="event-time svelte-1o0m66v"> </span></div>'),ts=g('<div role="button" tabindex="0"><div class="day-number svelte-1o0m66v"> </div> <div class="day-events svelte-1o0m66v"></div></div>'),as=g('<div class="month-view svelte-1o0m66v"><div class="month-header svelte-1o0m66v"></div> <div class="month-grid svelte-1o0m66v"></div></div>'),ss=g('<div class="week-summary svelte-1o0m66v"><div class="summary-header svelte-1o0m66v"><h3 class="svelte-1o0m66v">Week Summary</h3> <div class="week-totals svelte-1o0m66v"><div class="total-item svelte-1o0m66v"><span class="label svelte-1o0m66v">Total Hours:</span> <span class="value svelte-1o0m66v"> </span></div> <div class="total-item svelte-1o0m66v"><span class="label svelte-1o0m66v">Est. Revenue:</span> <span class="value svelte-1o0m66v"> </span></div> <div class="total-item svelte-1o0m66v"><span class="label svelte-1o0m66v">Actual Revenue:</span> <span class="value svelte-1o0m66v"> </span></div> <div class="total-item svelte-1o0m66v"><span class="label svelte-1o0m66v">Events:</span> <span class="value svelte-1o0m66v"> </span></div></div></div></div>'),rs=g('<div class="day-summary svelte-1o0m66v"><div class="summary-row svelte-1o0m66v"><span class="summary-label svelte-1o0m66v">Hours:</span> <span class="summary-value svelte-1o0m66v"> </span></div> <div class="summary-row svelte-1o0m66v"><span class="summary-label svelte-1o0m66v">Revenue:</span> <span class="summary-value svelte-1o0m66v"> </span></div> <div class="summary-row svelte-1o0m66v"><span class="summary-label svelte-1o0m66v">Events:</span> <span class="summary-value svelte-1o0m66v"> </span></div></div>'),os=g('<div><div class="day-info svelte-1o0m66v"><div class="day-name svelte-1o0m66v"> </div> <div class="day-number svelte-1o0m66v"> </div></div> <!></div>'),is=g('<div class="time-slot svelte-1o0m66v"> </div>'),ns=g('<div class="selection-checkbox svelte-1o0m66v"><input type="checkbox" class="svelte-1o0m66v"></div>'),ls=g('<div draggable="true" role="button" tabindex="0"><!> <div class="event-title svelte-1o0m66v"> </div> <div class="event-time svelte-1o0m66v"> </div></div>'),vs=g('<div class="time-cell svelte-1o0m66v" role="button" tabindex="0"></div>'),ds=g('<div class="day-column svelte-1o0m66v"></div>'),cs=g('<div class="week-view svelte-1o0m66v"><!> <div class="week-header svelte-1o0m66v"><div class="time-column"></div> <!></div> <div class="week-grid svelte-1o0m66v"><div class="time-column svelte-1o0m66v"></div> <!></div></div>'),us=g('<p class="svelte-1o0m66v"><strong class="svelte-1o0m66v">Description:</strong> </p>'),ms=g('<p class="svelte-1o0m66v"><strong class="svelte-1o0m66v">Job:</strong> </p>'),fs=g('<p class="svelte-1o0m66v"><strong class="svelte-1o0m66v">Staff:</strong> </p>'),ps=g('<div class="event-card svelte-1o0m66v" role="button" tabindex="0"><div class="event-header svelte-1o0m66v"><h4 class="svelte-1o0m66v"> </h4> <div class="event-actions"><!></div></div> <div class="event-details svelte-1o0m66v"><p class="svelte-1o0m66v"><strong class="svelte-1o0m66v">Time:</strong> </p> <p class="svelte-1o0m66v"><strong class="svelte-1o0m66v">Customer:</strong> </p> <!> <!> <!></div></div>'),_s=g('<div class="no-events svelte-1o0m66v"><p class="svelte-1o0m66v">No events scheduled for this day.</p> <!></div>'),gs=g('<div class="day-view svelte-1o0m66v"><h3 class="svelte-1o0m66v"> </h3> <div class="day-events svelte-1o0m66v"><!> <!></div></div>'),Ds=g('<div class="calendar-controls svelte-1o0m66v"><div class="navigation svelte-1o0m66v"><!> <!> <!> <h2 class="calendar-title svelte-1o0m66v"> </h2></div> <div class="copy-paste-controls svelte-1o0m66v"><!> <!> <!></div> <div class="view-controls svelte-1o0m66v"><div class="view-buttons svelte-1o0m66v"><!> <!> <!></div> <div class="staff-filter svelte-1o0m66v"><label for="staff-filter" class="svelte-1o0m66v">Staff:</label> <select id="staff-filter" class="svelte-1o0m66v"><option>All Staff</option><!></select></div></div></div> <div class="calendar-container svelte-1o0m66v"><!></div>',1),ys=g('<div class="error-message"> </div>'),hs=g('<div class="error-message"> </div>'),ws=g('<div class="error-message"> </div>'),Ss=g('<div class="error-message"> </div>'),bs=g('<div class="error-message"> </div>'),Ts=g("<option> </option>"),$s=g('<label><input type="checkbox"> <span class="checkbox-custom"></span> </label>'),ks=g('<form class="event-form svelte-1o0m66v"><div class="form-group"><label for="title">Title *</label> <input type="text" id="title" placeholder="Event title"> <!></div> <div class="form-group"><!></div> <div class="form-group"><label for="description">Description</label> <textarea id="description" rows="3" placeholder="Event description"></textarea></div> <div class="form-row svelte-1o0m66v"><div class="form-group"><label for="startDate">Start Date *</label> <input type="date" id="startDate"> <!></div> <div class="form-group"><label for="startTime">Start Time *</label> <input type="time" id="startTime"> <!></div></div> <div class="form-row svelte-1o0m66v"><div class="form-group"><label for="endDate">End Date *</label> <input type="date" id="endDate"> <!></div> <div class="form-group"><label for="endTime">End Time *</label> <input type="time" id="endTime"> <!></div></div> <div class="form-group"><label for="jobId">Related Job</label> <select id="jobId"><option>No related job</option><!></select></div> <div class="form-row svelte-1o0m66v"><div class="form-group"><label for="priority">Priority</label> <select id="priority"><option>Low</option><option>Medium</option><option>High</option><option>Urgent</option></select></div> <div class="form-group"><label for="status">Status</label> <select id="status"><option>Scheduled</option><option>In Progress</option><option>Completed</option><option>Cancelled</option></select></div></div> <div class="form-group"><fieldset class="svelte-1o0m66v"><legend class="svelte-1o0m66v">Assigned Staff</legend> <div class="staff-checkboxes svelte-1o0m66v"></div></fieldset></div> <div class="form-group"><label><input type="checkbox"> <span class="checkbox-custom"></span> All Day Event</label></div></form>'),Es=g("<!> <!> <!>",1),xs=g('<div class="container"><!> <main class="svelte-1o0m66v"><!></main></div> <!>',1);function tr(Gt,lt){Ta(lt,!1);const[vt,Yt]=Oa(),Le=()=>St(za,"$jobs",vt),Fe=()=>St(Ja,"$staff",vt),dt=()=>St(qa,"$customers",vt),bt=ne(),Tt=ne(),ge=ne(),Kt=[];let ct=ne(!0),we=ne([]),H=ne("month"),ze=ne("all"),Ue=ne(!1),de=ne(null),Q=ne(new Date),n=ne({title:"",description:"",startDate:"",startTime:"",endDate:"",endTime:"",jobId:"",customerId:"",customerSearch:"",assignedStaffIds:[],priority:"Medium",allDay:!1,status:"Scheduled"}),D=ne({}),me=ne(!1),xe=null,Se=ne([]),E=ne(new Set),be=ne(!1);function Qt(t){const s=Je(t);let i=0,v=0,c=0;return s.forEach(w=>{var J,De;const F=new Date(w.startDateTime),U=(new Date(w.endDateTime).getTime()-F.getTime())/(1e3*60*60);if(i+=U,w.jobId){const ye=Le().find(pe=>pe.id===w.jobId);ye&&(v+=((J=ye.estimatedCost)==null?void 0:J.totalCost)||0,c+=((De=ye.actualCost)==null?void 0:De.totalCost)||0)}else w.assignedStaff.forEach(ye=>{var Ie;const pe=Fe().find(Me=>Me.id===ye.staffId);pe&&((Ie=pe.wageInfo)==null?void 0:Ie.type)==="Hourly"&&pe.wageInfo.rate&&(v+=pe.wageInfo.rate*U)})}),{date:t,bookedHours:Math.round(i*100)/100,estimatedRevenue:Math.round(v*100)/100,actualRevenue:Math.round(c*100)/100,eventCount:s.length}}function Vt(){const s=Ge().map(v=>Qt(v)),i=s.reduce((v,c)=>({totalBookedHours:v.totalBookedHours+c.bookedHours,totalEstimatedRevenue:v.totalEstimatedRevenue+c.estimatedRevenue,totalActualRevenue:v.totalActualRevenue+c.actualRevenue,totalEvents:v.totalEvents+c.eventCount}),{totalBookedHours:0,totalEstimatedRevenue:0,totalActualRevenue:0,totalEvents:0});return{...i,totalBookedHours:Math.round(i.totalBookedHours*100)/100,totalEstimatedRevenue:Math.round(i.totalEstimatedRevenue*100)/100,totalActualRevenue:Math.round(i.totalActualRevenue*100)/100,dailySummaries:s}}function ut(t){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(t)}function $t(t){const s=Math.floor(t),i=Math.round((t-s)*60);return i===0?`${s}h`:`${s}h ${i}m`}Na(async()=>{const t=localStorage.getItem("ejp_calendar_events");if(!t||JSON.parse(t).length===0){const s={id:"test-event-1",title:"Test Drag Event",description:"A test event for drag and drop functionality",startDateTime:new Date().toISOString(),endDateTime:new Date(Date.now()+72e5).toISOString(),allDay:!1,assignedStaff:[],customerId:"test-customer",customerName:"Test Customer",status:"Scheduled",priority:"Medium",jobId:"",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};localStorage.setItem("ejp_calendar_events",JSON.stringify([s]))}await Xt()});async function Xt(){f(ct,!0);try{await Promise.all([Fa.loadJobs(),Ua.loadStaff(),We()])}catch(t){console.error("Error loading calendar data:",t),ee({message:"Failed to load calendar data",type:"error"})}finally{f(ct,!1)}}async function We(){try{const t=new Date;t.setMonth(t.getMonth()-6);const s=new Date;s.setMonth(s.getMonth()+6);const i=await Ba(t.toISOString().split("T")[0],s.toISOString().split("T")[0]);f(we,[...i]),console.log("Loaded calendar events:",e(we).length,e(we))}catch(t){throw console.error("Error loading calendar events:",t),t}}function Zt(){const t=new Date(e(Q));switch(e(H)){case"day":t.setDate(t.getDate()-1);break;case"week":t.setDate(t.getDate()-7);break;case"month":t.setMonth(t.getMonth()-1);break}f(Q,t)}function ea(){const t=new Date(e(Q));switch(e(H)){case"day":t.setDate(t.getDate()+1);break;case"week":t.setDate(t.getDate()+7);break;case"month":t.setMonth(t.getMonth()+1);break}f(Q,t)}function ta(){f(Q,new Date)}function kt(){let t=e(we);e(ze)!=="all"&&(t=t.filter(v=>v.assignedStaff.some(c=>c.staffId===e(ze))));const s=aa(e(Q),e(H)),i=sa(e(Q),e(H));return t=t.filter(v=>{const c=new Date(v.startDateTime),w=new Date(v.endDateTime);return c<=i&&w>=s}),t}function aa(t,s){const i=new Date(t);switch(s){case"day":i.setHours(0,0,0,0);break;case"week":i.setDate(i.getDate()-i.getDay()),i.setHours(0,0,0,0);break;case"month":i.setDate(1),i.setHours(0,0,0,0);break}return i}function sa(t,s){const i=new Date(t);switch(s){case"day":i.setHours(23,59,59,999);break;case"week":i.setDate(i.getDate()-i.getDay()+6),i.setHours(23,59,59,999);break;case"month":i.setMonth(i.getMonth()+1,0),i.setHours(23,59,59,999);break}return i}function ra(){const t=new Date(e(Q).getFullYear(),e(Q).getMonth(),1),s=new Date(t);s.setDate(s.getDate()-s.getDay());const i=[],v=new Date(s);for(let c=0;c<42;c++)i.push(new Date(v)),v.setDate(v.getDate()+1);return i}function Je(t){const s=t.toDateString();return kt().filter(i=>{const v=new Date(i.startDateTime),c=new Date(i.endDateTime);return v.toDateString()===s||v<=t&&c>=t})}function Ge(){const t=new Date(e(Q));t.setDate(t.getDate()-t.getDay());const s=[];for(let i=0;i<7;i++){const v=new Date(t);v.setDate(v.getDate()+i),s.push(v)}return s}function Et(){const t=[];for(let s=6;s<22;s++)t.push(`${s.toString().padStart(2,"0")}:00`);return t}function oa(t,s){const[i]=s.split(":").map(Number),v=new Date(t);v.setHours(i,0,0,0);const c=new Date(v);return c.setHours(i+1,0,0,0),Je(t).filter(w=>{const F=new Date(w.startDateTime),z=new Date(w.endDateTime);return F<c&&z>v})}function ia(t,s){f(de,null),pt();const[i]=s.split(":").map(Number);C(n,e(n).startDate=t.toISOString().split("T")[0]),C(n,e(n).endDate=t.toISOString().split("T")[0]),C(n,e(n).startTime=`${i.toString().padStart(2,"0")}:00`),C(n,e(n).endTime=`${(i+1).toString().padStart(2,"0")}:00`),f(Ue,!0)}function mt(t){f(de,null),pt(),t&&(C(n,e(n).startDate=t.toISOString().split("T")[0]),C(n,e(n).endDate=t.toISOString().split("T")[0])),f(Ue,!0)}function ft(t){f(de,t),na(t),f(Ue,!0)}function Ye(){f(Ue,!1),f(de,null),pt(),f(D,{}),f(me,!1)}function pt(){f(n,{title:"",description:"",startDate:new Date().toISOString().split("T")[0],startTime:"09:00",endDate:new Date().toISOString().split("T")[0],endTime:"17:00",jobId:"",customerId:"",customerSearch:"",assignedStaffIds:[],priority:"Medium",allDay:!1,status:"Scheduled"})}function na(t){const s=new Date(t.startDateTime),i=new Date(t.endDateTime),v=dt().find(w=>w.id===t.customerId),c=v?v.companyName||v.fullName:"";f(n,{title:t.title,description:t.description||"",startDate:s.toISOString().split("T")[0],startTime:s.toTimeString().slice(0,5),endDate:i.toISOString().split("T")[0],endTime:i.toTimeString().slice(0,5),jobId:t.jobId||"",customerId:t.customerId,customerSearch:c,assignedStaffIds:t.assignedStaff.map(w=>w.staffId),priority:t.priority,allDay:t.allDay,status:t.status})}function la(){if(f(D,{}),e(n).title.trim()||C(D,e(D).title="Title is required"),e(n).startDate||C(D,e(D).startDate="Start date is required"),e(n).endDate||C(D,e(D).endDate="End date is required"),e(n).startTime||C(D,e(D).startTime="Start time is required"),e(n).endTime||C(D,e(D).endTime="End time is required"),e(n).customerId||C(D,e(D).customerId="Customer is required"),e(n).startDate&&e(n).endDate&&e(n).startTime&&e(n).endTime){const t=new Date(`${e(n).startDate}T${e(n).startTime}`);new Date(`${e(n).endDate}T${e(n).endTime}`)<=t&&C(D,e(D).endTime="End time must be after start time")}return Object.keys(e(D)).length===0}async function xt(){if(f(me,!0),!la()){ee({message:"Please fix the errors in the form before submitting",type:"error"});return}try{const t=new Date(`${e(n).startDate}T${e(n).startTime}`),s=new Date(`${e(n).endDate}T${e(n).endTime}`),i=e(n).assignedStaffIds.map(c=>{const w=Fe().find(F=>F.id===c);return{staffId:c,staffName:w?w.fullName:"Unknown Staff",confirmed:!1}}),v={title:e(n).title,description:e(n).description,startDateTime:t.toISOString(),endDateTime:s.toISOString(),jobId:e(n).jobId||"",customerId:e(n).customerId,assignedStaff:i,priority:e(n).priority,allDay:e(n).allDay,status:e(n).status};e(de)?(await Bt(e(de).id,v),ee({message:"Event updated successfully",type:"success"})):(await Wt(v),ee({message:"Event created successfully",type:"success"})),await We(),await zt(),Ye()}catch(t){console.error("Error saving event:",t),ee({message:t instanceof Error?t.message:"An unknown error occurred",type:"error"})}}async function va(t){if(window.confirm(`Are you sure you want to delete "${t.title}"?`))try{await Wa(t.id),await We(),await zt(),ee({message:"Event deleted successfully",type:"success"})}catch(s){console.error("Error deleting event:",s),ee({message:"Failed to delete event",type:"error"})}}async function da(){if(!e(de)||!e(de).jobId){ee({message:"No job associated with this event",type:"error"});return}try{if(!Le().find(s=>s.id===e(de).jobId)){ee({message:"Job not found",type:"error"});return}Ye(),await Ga(`/invoices/new?jobId=${e(de).jobId}&customerId=${e(de).customerId}`),ee({message:"Redirecting to invoice creation...",type:"success"})}catch(t){console.error("Error creating invoice:",t),ee({message:"Failed to create invoice",type:"error"})}}function It(t){return t.toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}function Ke(t){return new Date(t).toLocaleTimeString("en-US",{hour:"2-digit",minute:"2-digit"})}function ca(t){C(n,e(n).customerId=t.detail);const s=dt().find(i=>i.id===t.detail);s&&C(n,e(n).customerSearch=s.companyName||s.fullName)}function ua(t){const s=Le().find(i=>i.id===t);return s?s.title:"Unknown Job"}function ma(t){const s=dt().find(i=>i.id===t);return s?s.companyName||s.fullName:"Unknown Customer"}function Ct(t){const s=new Date;return t.toDateString()===s.toDateString()}function fa(t){return t.getMonth()===e(Q).getMonth()}function Mt(t,s){t.dataTransfer&&(t.dataTransfer.setData("text/plain",s.id),t.dataTransfer.effectAllowed="move",xe=s)}function Ht(t){t.preventDefault(),t.dataTransfer&&(t.dataTransfer.dropEffect="move"),t.currentTarget.classList.add("drag-over")}function jt(t){t.currentTarget.classList.remove("drag-over")}async function Pt(t,s,i){if(t.preventDefault(),t.currentTarget.classList.remove("drag-over"),!!xe)try{let c,w;const F=new Date(xe.startDateTime),z=new Date(xe.endDateTime);if(i){const[U,J]=i.split(":").map(Number);c=new Date(s),c.setHours(U,J,0,0);const De=z.getTime()-F.getTime();w=new Date(c.getTime()+De)}else c=new Date(s),c.setHours(F.getHours(),F.getMinutes(),0,0),w=new Date(s),w.setHours(z.getHours(),z.getMinutes(),0,0);if(c.getTime()!==F.getTime()){const U={...xe,startDateTime:c.toISOString(),endDateTime:w.toISOString()};await Bt(xe.id,{startDateTime:U.startDateTime,endDateTime:U.endDateTime}),f(we,e(we).map(J=>J.id===xe.id?U:J)),ee({message:"Event moved successfully",type:"success"})}}catch(c){console.error("Failed to move event:",c),ee({message:"Failed to move event",type:"error"})}finally{xe=null}}function pa(){f(be,!e(be)),e(be)||(e(E).clear(),f(E,e(E)))}function _t(t){e(E).has(t)?e(E).delete(t):e(E).add(t),f(E,e(E))}function _a(){if(e(E).size===0){ee({message:"No events selected to copy",type:"warning"});return}f(Se,e(we).filter(t=>e(E).has(t.id))),ee({message:`Copied ${e(Se).length} event${e(Se).length>1?"s":""}`,type:"success"})}async function Ot(t){if(e(Se).length===0){ee({message:"No events to paste",type:"warning"});return}try{const s=[];for(const i of e(Se)){const v=new Date(i.startDateTime),w=new Date(i.endDateTime).getTime()-v.getTime(),F=new Date(t);F.setHours(v.getHours(),v.getMinutes(),0,0);const z=new Date(F.getTime()+w),U={...i,id:"",startDateTime:F.toISOString(),endDateTime:z.toISOString(),title:`${i.title} (Copy)`,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};delete U.id;const J=await Wt(U);s.push(J)}await We(),ee({message:`Pasted ${s.length} event${s.length>1?"s":""} to ${t.toLocaleDateString()}`,type:"success"}),e(E).clear(),f(E,e(E)),f(be,!1)}catch(s){console.error("Error pasting events:",s),ee({message:"Failed to paste events",type:"error"})}}function ga(){if(e(H)!=="week")return;const t=Ge(),s=e(we).filter(i=>{const v=new Date(i.startDateTime);return t.some(c=>v.toDateString()===c.toDateString())});s.forEach(i=>e(E).add(i.id)),f(E,e(E)),ee({message:`Selected ${s.length} events in current week`,type:"success"})}function Da(){e(E).clear(),f(E,e(E))}function Nt(t){e(Se).length>0?Ot(t):mt(t)}function Qe(t){e(be)?_t(t.id):ft(t)}function At(t,s){e(Se).length>0?Ot(t):ia(t,s)}rt(()=>e(H),()=>{f(bt,e(H)==="month"?ra():[])}),rt(()=>e(we),()=>{e(we)&&kt()}),rt(()=>(e(H),e(Q)),()=>{f(Tt,(()=>{switch(e(H)){case"day":return It(e(Q));case"week":const t=new Date(e(Q));t.setDate(t.getDate()-t.getDay());const s=new Date(t);return s.setDate(s.getDate()+6),`${t.toLocaleDateString()} - ${s.toLocaleDateString()}`;case"month":return e(Q).toLocaleDateString("en-US",{year:"numeric",month:"long"});default:return""}})())}),rt(()=>e(H),()=>{f(ge,e(H)==="week"?Vt():null)}),$a(),ja();var Rt=xs();xa(t=>{Ea.title="Job Calendar"});var gt=wt(Rt),Lt=r(gt);Aa(Lt,{title:"Job Calendar",$$slots:{actions:(t,s)=>{ve(t,{variant:"primary",$$events:{click:()=>mt()},children:(i,v)=>{Z();var c=le("Add Event");d(i,c)},$$slots:{default:!0}})}}});var Ft=o(Lt,2),ya=r(Ft);{var ha=t=>{var s=Ya(),i=r(s);Ra(i,{}),Z(2),a(s),d(t,s)},wa=t=>{var s=Ds(),i=wt(s),v=r(i),c=r(v);ve(c,{variant:"secondary",size:"small",$$events:{click:Zt},children:(m,x)=>{Z();var y=le("←");d(m,y)},$$slots:{default:!0}});var w=o(c,2);ve(w,{variant:"secondary",size:"small",$$events:{click:ta},children:(m,x)=>{Z();var y=le("Today");d(m,y)},$$slots:{default:!0}});var F=o(w,2);ve(F,{variant:"secondary",size:"small",$$events:{click:ea},children:(m,x)=>{Z();var y=le("→");d(m,y)},$$slots:{default:!0}});var z=o(F,2),U=r(z,!0);a(z),a(v);var J=o(v,2),De=r(J);const ye=W(()=>e(be)?"primary":"secondary");ve(De,{get variant(){return e(ye)},size:"small",$$events:{click:pa},children:(m,x)=>{Z();var y=le();h(()=>p(y,e(be)?"Exit Selection":"Select Events")),d(m,y)},$$slots:{default:!0}});var pe=o(De,2);{var Ie=m=>{var x=Ka(),y=r(x);const ce=W(()=>e(E).size===0);ve(y,{variant:"secondary",size:"small",get disabled(){return e(ce)},$$events:{click:_a},children:(P,ue)=>{Z();var te=le();h(()=>p(te,`Copy (${e(E).size??""})`)),d(P,te)},$$slots:{default:!0}});var V=o(y,2);{var q=P=>{ve(P,{variant:"secondary",size:"small",$$events:{click:ga},children:(ue,te)=>{Z();var R=le("Select Week");d(ue,R)},$$slots:{default:!0}})};G(V,P=>{e(H)==="week"&&P(q)})}var A=o(V,2);const re=W(()=>e(E).size===0);ve(A,{variant:"secondary",size:"small",get disabled(){return e(re)},$$events:{click:Da},children:(P,ue)=>{Z();var te=le("Clear");d(P,te)},$$slots:{default:!0}}),a(x),d(m,x)};G(pe,m=>{e(be)&&m(Ie)})}var Me=o(pe,2);{var Oe=m=>{var x=Qa(),y=r(x),ce=r(y);a(y),Z(2),a(x),h(()=>p(ce,`${e(Se).length??""} event${e(Se).length>1?"s":""} copied`)),d(m,x)};G(Me,m=>{e(Se).length>0&&m(Oe)})}a(J);var qe=o(J,2),Be=r(qe),Ve=r(Be);const Xe=W(()=>e(H)==="day"?"primary":"secondary");ve(Ve,{get variant(){return e(Xe)},size:"small",$$events:{click:()=>f(H,"day"),keydown:m=>m.key==="Enter"&&f(H,"day")},children:(m,x)=>{Z();var y=le("Day");d(m,y)},$$slots:{default:!0}});var He=o(Ve,2);const Ze=W(()=>e(H)==="week"?"primary":"secondary");ve(He,{get variant(){return e(Ze)},size:"small",$$events:{click:()=>f(H,"week"),keydown:m=>m.key==="Enter"&&f(H,"week")},children:(m,x)=>{Z();var y=le("Week");d(m,y)},$$slots:{default:!0}});var Dt=o(He,2);const yt=W(()=>e(H)==="month"?"primary":"secondary");ve(Dt,{get variant(){return e(yt)},size:"small",$$events:{click:()=>f(H,"month"),keydown:m=>m.key==="Enter"&&f(H,"month")},children:(m,x)=>{Z();var y=le("Month");d(m,y)},$$slots:{default:!0}}),a(Be);var Ne=o(Be,2),je=o(r(Ne),2);h(()=>{e(ze),ot(()=>{Fe()})});var Ce=r(je);Ce.value=Ce.__value="all";var et=o(Ce);_e(et,1,()=>Fe().filter(m=>m.isActive),Te,(m,x)=>{var y=Va(),ce={},V=r(y,!0);a(y),h(()=>{ce!==(ce=e(x).id)&&(y.value=(y.__value=e(x).id)??""),p(V,e(x).fullName)}),d(m,y)}),a(je),a(Ne),a(qe),a(i);var tt=o(i,2),ht=r(tt);{var at=m=>{var x=as(),y=r(x);_e(y,4,()=>["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],Te,(V,q)=>{var A=Xa(),re=r(A,!0);a(A),h(()=>p(re,q)),d(V,A)}),a(y);var ce=o(y,2);_e(ce,5,()=>e(bt),Te,(V,q)=>{var A=ts();let re;var P=r(A),ue=r(P,!0);a(P);var te=o(P,2);_e(te,5,()=>Je(e(q)),R=>R.id,(R,Y)=>{var O=es();let L;var b=r(O);{var _=T=>{var ae=Za(),X=r(ae);ke(X),a(ae),h(se=>Jt(X,se),[()=>e(E).has(e(Y).id)],W),N("change",X,()=>_t(e(Y).id)),N("click",X,nt(function(se){qt.call(this,lt,se)})),d(T,ae)};G(b,T=>{e(be)&&T(_)})}var $=o(b,2),k=r($,!0);a($);var j=o($,2),M=r(j,!0);a(j),a(O),h((T,ae)=>{L=Ee(O,1,"event-item svelte-1o0m66v",null,L,T),Ut(O,"data-id",e(Y).id),p(k,e(Y).title),p(M,ae)},[()=>({selected:e(E).has(e(Y).id)}),()=>Ke(e(Y).startDateTime)],W),N("dragstart",O,T=>Mt(T,e(Y))),N("click",O,nt(()=>Qe(e(Y)))),N("keydown",O,T=>T.key==="Enter"&&Qe(e(Y))),d(R,O)}),a(te),a(A),h((R,Y)=>{re=Ee(A,1,"day-cell svelte-1o0m66v",null,re,R),p(ue,Y)},[()=>({today:Ct(e(q)),"other-month":!fa(e(q))}),()=>e(q).getDate()],W),N("click",A,()=>Nt(e(q))),N("keydown",A,R=>R.key==="Enter"&&Nt(e(q))),N("dragover",A,Ht),N("dragleave",A,jt),N("drop",A,R=>Pt(R,e(q))),d(V,A)}),a(ce),a(x),d(m,x)},Ae=(m,x)=>{{var y=V=>{var q=cs(),A=r(q);{var re=O=>{var L=ss(),b=r(L),_=o(r(b),2),$=r(_),k=o(r($),2),j=r(k,!0);a(k),a($);var M=o($,2),T=o(r(M),2),ae=r(T,!0);a(T),a(M);var X=o(M,2),se=o(r(X),2),oe=r(se,!0);a(se),a(X);var l=o(X,2),u=o(r(l),2),S=r(u,!0);a(u),a(l),a(_),a(b),a(L),h((I,B,fe)=>{p(j,I),p(ae,B),p(oe,fe),p(S,e(ge).totalEvents)},[()=>$t(e(ge).totalBookedHours),()=>ut(e(ge).totalEstimatedRevenue),()=>ut(e(ge).totalActualRevenue)],W),d(O,L)};G(A,O=>{e(ge)&&O(re)})}var P=o(A,2),ue=o(r(P),2);_e(ue,1,Ge,Te,(O,L,b)=>{var _=os();let $;var k=r(_),j=r(k),M=r(j,!0);a(j);var T=o(j,2),ae=r(T,!0);a(T),a(k);var X=o(k,2);{var se=oe=>{var l=rs(),u=r(l),S=o(r(u),2),I=r(S,!0);a(S),a(u);var B=o(u,2),fe=o(r(B),2),$e=r(fe,!0);a(fe),a(B);var st=o(B,2),K=o(r(st),2),ie=r(K,!0);a(K),a(st),a(l),h((he,Pe)=>{p(I,he),p($e,Pe),p(ie,e(ge).dailySummaries[b].eventCount)},[()=>$t(e(ge).dailySummaries[b].bookedHours),()=>ut(e(ge).dailySummaries[b].estimatedRevenue)],W),d(oe,l)};G(X,oe=>{e(ge)&&e(ge).dailySummaries[b]&&oe(se)})}a(_),h((oe,l,u)=>{$=Ee(_,1,"day-header svelte-1o0m66v",null,$,oe),p(M,l),p(ae,u)},[()=>({today:Ct(e(L))}),()=>e(L).toLocaleDateString("en-US",{weekday:"short"}),()=>e(L).getDate()],W),d(O,_)}),a(P);var te=o(P,2),R=r(te);_e(R,5,Et,Te,(O,L)=>{var b=is(),_=r(b,!0);a(b),h(()=>p(_,e(L))),d(O,b)}),a(R);var Y=o(R,2);_e(Y,1,Ge,Te,(O,L)=>{var b=ds();_e(b,5,Et,Te,(_,$)=>{var k=vs();_e(k,5,()=>oa(e(L),e($)),j=>j.id,(j,M)=>{var T=ls();let ae;var X=r(T);{var se=I=>{var B=ns(),fe=r(B);ke(fe),a(B),h($e=>Jt(fe,$e),[()=>e(E).has(e(M).id)],W),N("change",fe,()=>_t(e(M).id)),N("click",fe,nt(function($e){qt.call(this,lt,$e)})),d(I,B)};G(X,I=>{e(be)&&I(se)})}var oe=o(X,2),l=r(oe,!0);a(oe);var u=o(oe,2),S=r(u,!0);a(u),a(T),h((I,B)=>{ae=Ee(T,1,"week-event svelte-1o0m66v",null,ae,I),Ut(T,"data-id",e(M).id),p(l,e(M).title),p(S,B)},[()=>({selected:e(E).has(e(M).id)}),()=>Ke(e(M).startDateTime)],W),N("dragstart",T,I=>Mt(I,e(M))),N("click",T,nt(()=>Qe(e(M)))),N("keydown",T,I=>I.key==="Enter"&&Qe(e(M))),d(j,T)}),a(k),N("click",k,()=>At(e(L),e($))),N("keydown",k,j=>j.key==="Enter"&&At(e(L),e($))),N("dragover",k,Ht),N("dragleave",k,jt),N("drop",k,j=>Pt(j,e(L),e($))),d(_,k)}),a(b),d(O,b)}),a(te),a(q),d(V,q)},ce=(V,q)=>{{var A=re=>{var P=gs(),ue=r(P),te=r(ue,!0);a(ue);var R=o(ue,2),Y=r(R);_e(Y,1,()=>Je(e(Q)),Te,(b,_)=>{var $=ps(),k=r($),j=r(k),M=r(j,!0);a(j);var T=o(j,2),ae=r(T);ve(ae,{variant:"tertiary",size:"small",$$events:{click:()=>va(e(_))},children:(K,ie)=>{Z();var he=le("Delete");d(K,he)},$$slots:{default:!0}}),a(T),a(k);var X=o(k,2),se=r(X),oe=o(r(se));a(se);var l=o(se,2),u=o(r(l));a(l);var S=o(l,2);{var I=K=>{var ie=us(),he=o(r(ie));a(ie),h(()=>p(he,` ${e(_).description??""}`)),d(K,ie)};G(S,K=>{e(_).description&&K(I)})}var B=o(S,2);{var fe=K=>{var ie=ms(),he=o(r(ie));a(ie),h(Pe=>p(he,` ${Pe??""}`),[()=>ua(e(_).jobId)],W),d(K,ie)};G(B,K=>{e(_).jobId&&K(fe)})}var $e=o(B,2);{var st=K=>{var ie=fs(),he=o(r(ie));a(ie),h(Pe=>p(he,` ${Pe??""}`),[()=>e(_).assignedStaff.map(Pe=>Pe.staffName).join(", ")],W),d(K,ie)};G($e,K=>{e(_).assignedStaff.length>0&&K(st)})}a(X),a($),h((K,ie,he)=>{p(M,e(_).title),p(oe,` ${K??""} - ${ie??""}`),p(u,` ${he??""}`)},[()=>Ke(e(_).startDateTime),()=>Ke(e(_).endDateTime),()=>ma(e(_).customerId)],W),N("click",$,()=>ft(e(_))),N("keydown",$,K=>K.key==="Enter"&&ft(e(_))),d(b,$)});var O=o(Y,2);{var L=b=>{var _=_s(),$=o(r(_),2);ve($,{$$events:{click:()=>mt(e(Q))},children:(k,j)=>{Z();var M=le("Add Event");d(k,M)},$$slots:{default:!0}}),a(_),d(b,_)};G(O,b=>{Je(e(Q)).length===0&&b(L)})}a(R),a(P),h(b=>p(te,b),[()=>It(e(Q))],W),d(re,P)};G(V,re=>{e(H)==="day"&&re(A)},q)}};G(m,V=>{e(H)==="week"?V(y):V(ce,!1)},x)}};G(ht,m=>{e(H)==="month"?m(at):m(Ae,!1)})}a(tt),h(()=>p(U,e(Tt))),it(je,()=>e(ze),m=>f(ze,m)),d(t,s)};G(ya,t=>{e(ct)?t(ha):t(wa,!1)})}a(Ft),a(gt);var Sa=o(gt,2);const ba=W(()=>e(de)?"Edit Event":"Create Event");Pa(Sa,{get show(){return e(Ue)},get title(){return e(ba)},$$events:{close:Ye},children:(t,s)=>{var i=ks(),v=r(i),c=o(r(v),2);ke(c);let w;var F=o(c,2);{var z=l=>{var u=ys(),S=r(u,!0);a(u),h(()=>p(S,e(D).title)),d(l,u)};G(F,l=>{e(me)&&e(D).title&&l(z)})}a(v);var U=o(v,2),J=r(U);const De=W(()=>e(me)&&!!e(D).customerId);La(J,{get customerId(){return e(n).customerId},get customerSearch(){return e(n).customerSearch},get hasError(){return e(De)},get errorMessage(){return e(D).customerId},$$events:{selectcustomer:ca}}),a(U);var ye=o(U,2),pe=o(r(ye),2);Ia(pe),a(ye);var Ie=o(ye,2),Me=r(Ie),Oe=o(r(Me),2);ke(Oe);let qe;var Be=o(Oe,2);{var Ve=l=>{var u=hs(),S=r(u,!0);a(u),h(()=>p(S,e(D).startDate)),d(l,u)};G(Be,l=>{e(me)&&e(D).startDate&&l(Ve)})}a(Me);var Xe=o(Me,2),He=o(r(Xe),2);ke(He);let Ze;var Dt=o(He,2);{var yt=l=>{var u=ws(),S=r(u,!0);a(u),h(()=>p(S,e(D).startTime)),d(l,u)};G(Dt,l=>{e(me)&&e(D).startTime&&l(yt)})}a(Xe),a(Ie);var Ne=o(Ie,2),je=r(Ne),Ce=o(r(je),2);ke(Ce);let et;var tt=o(Ce,2);{var ht=l=>{var u=Ss(),S=r(u,!0);a(u),h(()=>p(S,e(D).endDate)),d(l,u)};G(tt,l=>{e(me)&&e(D).endDate&&l(ht)})}a(je);var at=o(je,2),Ae=o(r(at),2);ke(Ae);let m;var x=o(Ae,2);{var y=l=>{var u=bs(),S=r(u,!0);a(u),h(()=>p(S,e(D).endTime)),d(l,u)};G(x,l=>{e(me)&&e(D).endTime&&l(y)})}a(at),a(Ne);var ce=o(Ne,2),V=o(r(ce),2);h(()=>{e(n),ot(()=>{Le()})});var q=r(V);q.value=q.__value="";var A=o(q);_e(A,1,Le,Te,(l,u)=>{var S=Ts(),I={},B=r(S);a(S),h(()=>{I!==(I=e(u).id)&&(S.value=(S.__value=e(u).id)??""),p(B,`${e(u).title??""} - ${e(u).customerName??""}`)}),d(l,S)}),a(V),a(ce);var re=o(ce,2),P=r(re),ue=o(r(P),2);h(()=>{e(n),ot(()=>{})});var te=r(ue);te.value=te.__value="Low";var R=o(te);R.value=R.__value="Medium";var Y=o(R);Y.value=Y.__value="High";var O=o(Y);O.value=O.__value="Urgent",a(ue),a(P);var L=o(P,2),b=o(r(L),2);h(()=>{e(n),ot(()=>{})});var _=r(b);_.value=_.__value="Scheduled";var $=o(_);$.value=$.__value="In Progress";var k=o($);k.value=k.__value="Completed";var j=o(k);j.value=j.__value="Cancelled",a(b),a(L),a(re);var M=o(re,2),T=r(M),ae=o(r(T),2);_e(ae,5,()=>Fe().filter(l=>l.isActive),Te,(l,u)=>{var S=$s(),I=r(S);ke(I);var B,fe=o(I,3);a(S),h(()=>{B!==(B=e(u).id)&&(I.value=(I.__value=e(u).id)??""),p(fe,` ${e(u).fullName??""}`)}),Ma(Kt,[],I,()=>(e(u).id,e(n).assignedStaffIds),$e=>C(n,e(n).assignedStaffIds=$e)),d(l,S)}),a(ae),a(T),a(M);var X=o(M,2),se=r(X),oe=r(se);ke(oe),Z(3),a(se),a(X),a(i),h((l,u,S,I,B)=>{w=Ee(c,1,"",null,w,l),qe=Ee(Oe,1,"",null,qe,u),Ze=Ee(He,1,"",null,Ze,S),et=Ee(Ce,1,"",null,et,I),m=Ee(Ae,1,"",null,m,B)},[()=>({error:e(me)&&e(D).title}),()=>({error:e(me)&&e(D).startDate}),()=>({error:e(me)&&e(D).startTime}),()=>({error:e(me)&&e(D).endDate}),()=>({error:e(me)&&e(D).endTime})],W),Re(c,()=>e(n).title,l=>C(n,e(n).title=l)),Re(pe,()=>e(n).description,l=>C(n,e(n).description=l)),Re(Oe,()=>e(n).startDate,l=>C(n,e(n).startDate=l)),Re(He,()=>e(n).startTime,l=>C(n,e(n).startTime=l)),Re(Ce,()=>e(n).endDate,l=>C(n,e(n).endDate=l)),Re(Ae,()=>e(n).endTime,l=>C(n,e(n).endTime=l)),it(V,()=>e(n).jobId,l=>C(n,e(n).jobId=l)),it(ue,()=>e(n).priority,l=>C(n,e(n).priority=l)),it(b,()=>e(n).status,l=>C(n,e(n).status=l)),Ca(oe,()=>e(n).allDay,l=>C(n,e(n).allDay=l)),N("submit",i,Ha(xt)),d(t,i)},$$slots:{default:!0,footer:(t,s)=>{var i=Es(),v=wt(i);ve(v,{type:"button",variant:"tertiary",$$events:{click:Ye},children:(z,U)=>{Z();var J=le("Cancel");d(z,J)},$$slots:{default:!0}});var c=o(v,2);{var w=z=>{ve(z,{type:"button",variant:"secondary",$$events:{click:da},children:(U,J)=>{Z();var De=le("Create Invoice");d(U,De)},$$slots:{default:!0}})};G(c,z=>{e(de)&&e(de).jobId&&z(w)})}var F=o(c,2);ve(F,{type:"button",$$events:{click:xt},children:(z,U)=>{Z();var J=le();h(()=>p(J,`${e(de)?"Update":"Create"} Event`)),d(z,J)},$$slots:{default:!0}}),d(t,i)}}}),d(Gt,Rt),ka(),Yt()}export{tr as component};
