import"../chunks/CWj6FrbW.js";import"../chunks/69_IOA4Y.js";import{e as g,f as h,t as l,g as v,i as a,r as o,s as d}from"../chunks/p3DoyA09.js";import{s as p}from"../chunks/BUelSUke.js";import{t as x,a as _}from"../chunks/B67foYpL.js";import{i as $}from"../chunks/D3pqaimu.js";import{s as k,p as i}from"../chunks/CSyJhG7e.js";const b={get error(){return i.error},get status(){return i.status}};k.updated.check;const m=b;var E=x("<h1> </h1> <p> </p>",1);function C(f,n){g(n,!1),$();var r=E(),t=h(r),c=a(t,!0);o(t);var e=d(t,2),u=a(e,!0);o(e),l(()=>{var s;p(c,m.status),p(u,(s=m.error)==null?void 0:s.message)}),_(f,r),v()}export{C as component};
