:root{--br: 10px;--shadow: 0 5px 30px 0 var(--primary-fade);--shadow-small: 0 1px 2px 0 var(--primary-fade);--black: #222;--white: #fff;--grey: #4f5561;--darkgrey: #6e7583;--solid-secondary-fade: #e1ebfc;--white-on-dark: #fff;--primary-hs: 236 46%;--primary-l: 20%;--secondary-hs: 220 92%;--secondary-l: 55%;--tertiary-hs: 11 100%;--tertiary-l: 68%;--bg: hsl(220, 60%, 98.04%);--border: hsl(240, 55%, 89%);--cal-cell-h: 36px;--red-hs: 0 100%;--red-l: 50%;--red: hsl(var(--red-hs) var(--red-l));--red-darker: hsl(var(--red-hs) calc(var(--red-l) - 5%));--red-darkest: hsl(var(--red-hs) calc(var(--red-l) - 10%));--red-fade: hsl(var(--red-hs) var(--red-l) / 10%);--red-fade2: hsl(var(--red-hs) var(--red-l) / 20%);--red-fade3: hsl(var(--red-hs) var(--red-l) / 5%);--yellow-hs: 50 100%;--yellow-l: 50%;--yellow: hsl(var(--yellow-hs) var(--yellow-l));--yellow-darker: hsl(var(--yellow-hs) calc(var(--yellow-l) - 5%));--yellow-darkest: hsl(var(--yellow-hs) calc(var(--yellow-l) - 10%));--yellow-fade: hsl(var(--yellow-hs) var(--yellow-l) / 10%);--yellow-fade2: hsl(var(--yellow-hs) var(--yellow-l) / 20%);--yellow-fade3: hsl(var(--yellow-hs) var(--yellow-l) / 5%);--orange-hs: 30 100%;--orange-l: 50%;--orange: hsl(var(--orange-hs) var(--orange-l));--orange-darker: hsl(var(--orange-hs) calc(var(--orange-l) - 5%));--orange-darkest: hsl(var(--orange-hs) calc(var(--orange-l) - 10%));--orange-fade: hsl(var(--orange-hs) var(--orange-l) / 10%);--orange-fade2: hsl(var(--orange-hs) var(--orange-l) / 20%);--orange-fade3: hsl(var(--orange-hs) var(--orange-l) / 5%);--green-hs: 156 100%;--green-l: 27%;--green: hsl(var(--green-hs) var(--green-l));--green-darker: hsl(var(--green-hs) calc(var(--green-l) - 5%));--green-darkest: hsl(var(--green-hs) calc(var(--green-l) - 10%));--green-fade: hsl(var(--green-hs) var(--green-l) / 10%);--green-fade2: hsl(var(--green-hs) var(--green-l) / 20%);--green-fade3: hsl(var(--green-hs) var(--green-l) / 5%);--primary: hsl(var(--primary-hs) var(--primary-l));--primary-darker: hsl(var(--primary-hs) calc(var(--primary-l) - 5%));--primary-darkest: hsl(var(--primary-hs) calc(var(--primary-l) - 10%));--primary-fade: hsl(var(--primary-hs) var(--primary-l) / 10%);--primary-fade2: hsl(var(--primary-hs) var(--primary-l) / 20%);--primary-fade3: hsl(var(--primary-hs) var(--primary-l) / 5%);--secondary: hsl(var(--secondary-hs) var(--secondary-l));--secondary-darker: hsl(var(--secondary-hs) calc(var(--secondary-l) - 5%));--secondary-darkest: hsl(var(--secondary-hs) calc(var(--secondary-l) - 10%));--secondary-fade: hsl(var(--secondary-hs) var(--secondary-l) / 10%);--secondary-fade2: hsl(var(--secondary-hs) var(--secondary-l) / 20%);--secondary-fade3: hsl(var(--secondary-hs) var(--secondary-l) / 5%);--secondary-fade9: hsl(var(--secondary-hs) var(--secondary-l) / 90%);--secondary-fade-solid: hsl(var(--secondary-hs) 90%);--secondary-fade-solid2: hsl(var(--secondary-hs) 90%);--tertiary: hsl(var(--tertiary-hs) var(--tertiary-l));--tertiary-darker: hsl(var(--tertiary-hs) calc(var(--tertiary-l) - 5%));--tertiary-darkest: hsl(var(--tertiary-hs) calc(var(--tertiary-l) - 10%));--tertiary-fade: hsl(var(--tertiary-hs) var(--tertiary-l) / 10%);--tertiary-fade2: hsl(var(--tertiary-hs) var(--tertiary-l) / 20%);--tertiary-fade3: hsl(var(--tertiary-hs) var(--tertiary-l) / 5%);--yellow: hsl(50, 100%, 90%);--yellow-darker: hsl(50, 100%, 75%);--yellow-darkest: hsl(50, 100%, 25%)}.grid.svelte-bcvkzs{display:grid;width:100%;border-radius:var(--br);border:1px solid var(--border);background:var(--white);overflow:hidden}.grid.svelte-bcvkzs .grid-row-empty:where(.svelte-bcvkzs){padding:100px 20px;box-sizing:border-box;color:var(--grey);text-align:center;grid-column:1 / -1}.grid.svelte-bcvkzs .grid-row:where(.svelte-bcvkzs),.grid.svelte-bcvkzs .grid-row-header:where(.svelte-bcvkzs){display:grid;grid-column:1 / -1}.grid.svelte-bcvkzs .grid-row:where(.svelte-bcvkzs){border-bottom:1px solid var(--border)}.grid.svelte-bcvkzs .grid-row:where(.svelte-bcvkzs):last-child{border-bottom:none}.grid.svelte-bcvkzs .grid-row:where(.svelte-bcvkzs) .cell:where(.svelte-bcvkzs){padding:20px;color:var(--black);overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.grid.svelte-bcvkzs .grid-row-header:where(.svelte-bcvkzs){border-bottom:2px solid var(--border);font-weight:600;background:var(--bg);font-size:14px}.grid.svelte-bcvkzs .grid-row-header:where(.svelte-bcvkzs) .header-cell:where(.svelte-bcvkzs){background:none;border:none;padding:10px 20px;margin:0;font:inherit;color:var(--black);cursor:pointer;text-align:left;transition:background .2s;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;width:100%}.grid.svelte-bcvkzs .grid-row-header:where(.svelte-bcvkzs) .header-cell:where(.svelte-bcvkzs):hover:not(:disabled){background:var(--secondary-fade)}.grid.svelte-bcvkzs .grid-row-header:where(.svelte-bcvkzs) .header-cell:where(.svelte-bcvkzs):disabled{cursor:default;color:var(--grey)}.grid.svelte-bcvkzs .grid-row-header:where(.svelte-bcvkzs) .header-cell.is-sorting:where(.svelte-bcvkzs){background:var(--secondary-fade3)}.grid.svelte-bcvkzs .grid-row-header:where(.svelte-bcvkzs) .header-cell.is-sorting.ascending:where(.svelte-bcvkzs):after{content:"↑";margin-left:10px;color:var(--primary)}.grid.svelte-bcvkzs .grid-row-header:where(.svelte-bcvkzs) .header-cell.is-sorting.descending:where(.svelte-bcvkzs):after{content:"↓";margin-left:10px;color:var(--primary)}.grid-top.svelte-bcvkzs{display:flex;justify-content:space-between;align-items:center;padding:10px;border-bottom:1px solid var(--border);width:100%;grid-column:1 / -1;border-radius:var(--br) var(--br) 0 0;background:var(--bg);box-sizing:border-box;gap:20px}.grid-top.svelte-bcvkzs label:where(.svelte-bcvkzs){color:var(--primary);font-size:14px;white-space:nowrap}.grid-top.svelte-bcvkzs input[type=text]:where(.svelte-bcvkzs){height:30px;width:140px;margin-right:5px;padding:0 5px;font-size:14px;border:1px solid var(--border, #ccc);border-radius:var(--br, 4px)}.grid-top.svelte-bcvkzs select:where(.svelte-bcvkzs){height:30px;padding:0 5px;border:1px solid var(--border, #ccc);border-radius:var(--br, 4px);background-color:var(--white, #fff)}.grid-top.svelte-bcvkzs .grid-search:where(.svelte-bcvkzs){flex-grow:1;display:flex;align-items:center;gap:5px}.grid-top.svelte-bcvkzs .grid-search:where(.svelte-bcvkzs) .button:where(.svelte-bcvkzs){padding:0 10px;height:30px;line-height:30px;color:var(--primary);font-size:14px;font-weight:600;background-color:transparent;border:1px solid var(--primary);border-radius:var(--br);cursor:pointer}.grid-top.svelte-bcvkzs .grid-search:where(.svelte-bcvkzs) .button:where(.svelte-bcvkzs):hover{background-color:var(--primary-fade, rgba(0, 0, 0, .05))}.grid-top.svelte-bcvkzs .grid-search-and-items-per-page:where(.svelte-bcvkzs){display:flex;gap:20px;align-items:center;white-space:nowrap}.grid-top.svelte-bcvkzs .grid-pager:where(.svelte-bcvkzs){display:flex;gap:5px;align-items:center}.grid-top.svelte-bcvkzs .grid-pager:where(.svelte-bcvkzs) button:where(.svelte-bcvkzs){background:var(--button-bg, var(--bg));border-radius:var(--br);padding:5px 10px;border:1px solid var(--border);color:var(--black);cursor:pointer;font-size:14px;line-height:1.2}.grid-top.svelte-bcvkzs .grid-pager:where(.svelte-bcvkzs) button:where(.svelte-bcvkzs):hover:not(.selected){background:var(--secondary-fade, #f0f0f0)}.grid-top.svelte-bcvkzs .grid-pager:where(.svelte-bcvkzs) button.selected:where(.svelte-bcvkzs){background:var(--primary, #007bff);color:var(--white, #fff);border-color:var(--primary, #007bff)}.grid-top.svelte-bcvkzs>div:where(.svelte-bcvkzs):last-child{white-space:nowrap;font-size:14px;color:var(--grey, #555)}
