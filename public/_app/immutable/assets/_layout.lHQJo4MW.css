:root{--br: 10px;--shadow: 0 5px 30px 0 var(--primary-fade);--shadow-small: 0 1px 2px 0 var(--primary-fade);--black: #222;--white: #fff;--grey: #4f5561;--darkgrey: #6e7583;--solid-secondary-fade: #e1ebfc;--white-on-dark: #fff;--primary-hs: 236 46%;--primary-l: 20%;--secondary-hs: 220 92%;--secondary-l: 55%;--tertiary-hs: 11 100%;--tertiary-l: 68%;--bg: hsl(220, 60%, 98.04%);--border: hsl(240, 55%, 89%);--cal-cell-h: 36px;--red-hs: 0 100%;--red-l: 50%;--red: hsl(var(--red-hs) var(--red-l));--red-darker: hsl(var(--red-hs) calc(var(--red-l) - 5%));--red-darkest: hsl(var(--red-hs) calc(var(--red-l) - 10%));--red-fade: hsl(var(--red-hs) var(--red-l) / 10%);--red-fade2: hsl(var(--red-hs) var(--red-l) / 20%);--red-fade3: hsl(var(--red-hs) var(--red-l) / 5%);--yellow-hs: 50 100%;--yellow-l: 50%;--yellow: hsl(var(--yellow-hs) var(--yellow-l));--yellow-darker: hsl(var(--yellow-hs) calc(var(--yellow-l) - 5%));--yellow-darkest: hsl(var(--yellow-hs) calc(var(--yellow-l) - 10%));--yellow-fade: hsl(var(--yellow-hs) var(--yellow-l) / 10%);--yellow-fade2: hsl(var(--yellow-hs) var(--yellow-l) / 20%);--yellow-fade3: hsl(var(--yellow-hs) var(--yellow-l) / 5%);--orange-hs: 30 100%;--orange-l: 50%;--orange: hsl(var(--orange-hs) var(--orange-l));--orange-darker: hsl(var(--orange-hs) calc(var(--orange-l) - 5%));--orange-darkest: hsl(var(--orange-hs) calc(var(--orange-l) - 10%));--orange-fade: hsl(var(--orange-hs) var(--orange-l) / 10%);--orange-fade2: hsl(var(--orange-hs) var(--orange-l) / 20%);--orange-fade3: hsl(var(--orange-hs) var(--orange-l) / 5%);--green-hs: 156 100%;--green-l: 27%;--green: hsl(var(--green-hs) var(--green-l));--green-darker: hsl(var(--green-hs) calc(var(--green-l) - 5%));--green-darkest: hsl(var(--green-hs) calc(var(--green-l) - 10%));--green-fade: hsl(var(--green-hs) var(--green-l) / 10%);--green-fade2: hsl(var(--green-hs) var(--green-l) / 20%);--green-fade3: hsl(var(--green-hs) var(--green-l) / 5%);--primary: hsl(var(--primary-hs) var(--primary-l));--primary-darker: hsl(var(--primary-hs) calc(var(--primary-l) - 5%));--primary-darkest: hsl(var(--primary-hs) calc(var(--primary-l) - 10%));--primary-fade: hsl(var(--primary-hs) var(--primary-l) / 10%);--primary-fade2: hsl(var(--primary-hs) var(--primary-l) / 20%);--primary-fade3: hsl(var(--primary-hs) var(--primary-l) / 5%);--secondary: hsl(var(--secondary-hs) var(--secondary-l));--secondary-darker: hsl(var(--secondary-hs) calc(var(--secondary-l) - 5%));--secondary-darkest: hsl(var(--secondary-hs) calc(var(--secondary-l) - 10%));--secondary-fade: hsl(var(--secondary-hs) var(--secondary-l) / 10%);--secondary-fade2: hsl(var(--secondary-hs) var(--secondary-l) / 20%);--secondary-fade3: hsl(var(--secondary-hs) var(--secondary-l) / 5%);--secondary-fade9: hsl(var(--secondary-hs) var(--secondary-l) / 90%);--secondary-fade-solid: hsl(var(--secondary-hs) 90%);--secondary-fade-solid2: hsl(var(--secondary-hs) 90%);--tertiary: hsl(var(--tertiary-hs) var(--tertiary-l));--tertiary-darker: hsl(var(--tertiary-hs) calc(var(--tertiary-l) - 5%));--tertiary-darkest: hsl(var(--tertiary-hs) calc(var(--tertiary-l) - 10%));--tertiary-fade: hsl(var(--tertiary-hs) var(--tertiary-l) / 10%);--tertiary-fade2: hsl(var(--tertiary-hs) var(--tertiary-l) / 20%);--tertiary-fade3: hsl(var(--tertiary-hs) var(--tertiary-l) / 5%);--yellow: hsl(50, 100%, 90%);--yellow-darker: hsl(50, 100%, 75%);--yellow-darkest: hsl(50, 100%, 25%)}body{padding:0;margin:0;font-family:IBM Plex Sans,sans-serif;background:var(--bg)}body *{font-family:IBM Plex Sans,sans-serif}.app-layout{display:grid;grid-template-columns:200px 1fr}main{padding:2rem}.container{overflow:auto;height:100vh}.form-group{flex:1;margin-bottom:1rem;display:flex;flex-direction:column;gap:.5rem}.form-group.full-width{grid-column:1 / -1}.form-group label{display:block;margin-bottom:.5rem;font-weight:500;font-size:.9rem;color:var(--grey)}.form-group label:has(input[type=checkbox]){display:flex;align-items:center;gap:.5rem;padding:.5rem;border:1px solid var(--border);border-radius:var(--br);cursor:pointer;transition:all .2s ease;margin-bottom:0}.form-group label:has(input[type=checkbox]):hover{background:var(--bg);border-color:var(--primary)}.form-group label:has(input[type=checkbox]) input[type=checkbox]{display:none}.form-group label:has(input[type=checkbox]) .checkbox-custom{width:18px;height:18px;border:2px solid var(--border);border-radius:3px;position:relative;flex-shrink:0;transition:all .2s ease}.form-group label:has(input[type=checkbox]) .checkbox-custom:after{content:"";position:absolute;top:2px;left:6px;width:4px;height:8px;border:solid white;border-width:0 2px 2px 0;transform:rotate(45deg);opacity:0;transition:opacity .2s ease}.form-group label:has(input[type=checkbox]) input[type=checkbox]:checked+.checkbox-custom{background:var(--primary);border-color:var(--primary)}.form-group label:has(input[type=checkbox]) input[type=checkbox]:checked+.checkbox-custom:after{opacity:1}.form-group label input[type=text],.form-group label input[type=number],.form-group label select,.form-group label textarea{width:100%;padding:.5rem;border:1px solid var(--border);border-radius:var(--br);font-size:.9rem}.form-group label input[type=text]:focus,.form-group label input[type=number]:focus,.form-group label select:focus,.form-group label textarea:focus{outline:none;border-color:var(--primary);box-shadow:0 0 0 2px var(--primary-fade)}.form-group label input[type=text].error,.form-group label input[type=number].error,.form-group label select.error,.form-group label textarea.error{border-color:var(--red)}.form-group label select[multiple]{min-height:120px}.form-group .error-message{color:var(--red);font-size:.8rem;margin-top:.25rem}.form-group .help-text{font-size:.8rem;color:var(--grey);margin-top:.25rem}input,select,textarea{width:100%;padding:8px 12px;border:1px solid var(--border);border-radius:4px;font-size:14px;box-sizing:border-box;font-family:var(--font-family)}input:focus,select:focus,textarea:focus{outline:none;border-color:var(--primary);box-shadow:0 0 0 2px var(--primary-fade)}input.error,select.error,textarea.error{border-color:var(--red)}textarea{resize:vertical;min-height:100px}.error-message{color:var(--red);font-size:12px;margin-top:4px}.vertical-sidebar-layout{display:grid;grid-template-columns:250px 1fr;gap:2rem;min-height:calc(100vh - 200px)}.vertical-sidebar-layout .vertical-sidebar-content{background:#fff;border:1px solid var(--border);border-radius:var(--br);padding:2rem}.component-docs-container h1{margin:0 0 .5rem;color:var(--black);font-size:1.75rem}.component-docs-container h2{margin:2rem 0 1rem;color:var(--black);font-size:1.2rem}.component-docs-container h3{margin:1rem 0 .5rem;color:var(--black);font-size:1.2rem}.component-docs-container h4{margin-top:0;margin-bottom:1rem;color:var(--primary);font-size:1rem}.component-docs-container .description{margin:0 0 2rem;color:var(--grey);font-size:1rem;line-height:1.5}.component-docs-container .props-table{border:1px solid var(--border);border-radius:var(--br);overflow:hidden}.component-docs-container .props-table .prop-row{display:grid;grid-template-columns:1fr 1fr 2fr;border-bottom:1px solid var(--border)}.component-docs-container .props-table .prop-row:last-child{border-bottom:none}.component-docs-container .props-table .prop-row:first-child{background:var(--bg);font-weight:600}.component-docs-container .props-table .prop-row div{padding:.75rem 1rem;border-right:1px solid var(--border)}.component-docs-container .props-table .prop-row div:last-child{border-right:none}.component-docs-container .props-table .prop-row .prop-name{font-family:monospace;color:var(--primary)}.component-docs-container .props-table .prop-row .prop-type{font-family:monospace;color:var(--grey)}.component-docs-container .props-table .prop-row .prop-description{color:var(--black)}
