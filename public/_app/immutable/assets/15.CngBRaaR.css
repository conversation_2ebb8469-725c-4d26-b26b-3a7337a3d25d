:root{--br: 10px;--shadow: 0 5px 30px 0 var(--primary-fade);--shadow-small: 0 1px 2px 0 var(--primary-fade);--black: #222;--white: #fff;--grey: #4f5561;--darkgrey: #6e7583;--solid-secondary-fade: #e1ebfc;--white-on-dark: #fff;--primary-hs: 236 46%;--primary-l: 20%;--secondary-hs: 220 92%;--secondary-l: 55%;--tertiary-hs: 11 100%;--tertiary-l: 68%;--bg: hsl(220, 60%, 98.04%);--border: hsl(240, 55%, 89%);--cal-cell-h: 36px;--red-hs: 0 100%;--red-l: 50%;--red: hsl(var(--red-hs) var(--red-l));--red-darker: hsl(var(--red-hs) calc(var(--red-l) - 5%));--red-darkest: hsl(var(--red-hs) calc(var(--red-l) - 10%));--red-fade: hsl(var(--red-hs) var(--red-l) / 10%);--red-fade2: hsl(var(--red-hs) var(--red-l) / 20%);--red-fade3: hsl(var(--red-hs) var(--red-l) / 5%);--yellow-hs: 50 100%;--yellow-l: 50%;--yellow: hsl(var(--yellow-hs) var(--yellow-l));--yellow-darker: hsl(var(--yellow-hs) calc(var(--yellow-l) - 5%));--yellow-darkest: hsl(var(--yellow-hs) calc(var(--yellow-l) - 10%));--yellow-fade: hsl(var(--yellow-hs) var(--yellow-l) / 10%);--yellow-fade2: hsl(var(--yellow-hs) var(--yellow-l) / 20%);--yellow-fade3: hsl(var(--yellow-hs) var(--yellow-l) / 5%);--orange-hs: 30 100%;--orange-l: 50%;--orange: hsl(var(--orange-hs) var(--orange-l));--orange-darker: hsl(var(--orange-hs) calc(var(--orange-l) - 5%));--orange-darkest: hsl(var(--orange-hs) calc(var(--orange-l) - 10%));--orange-fade: hsl(var(--orange-hs) var(--orange-l) / 10%);--orange-fade2: hsl(var(--orange-hs) var(--orange-l) / 20%);--orange-fade3: hsl(var(--orange-hs) var(--orange-l) / 5%);--green-hs: 156 100%;--green-l: 27%;--green: hsl(var(--green-hs) var(--green-l));--green-darker: hsl(var(--green-hs) calc(var(--green-l) - 5%));--green-darkest: hsl(var(--green-hs) calc(var(--green-l) - 10%));--green-fade: hsl(var(--green-hs) var(--green-l) / 10%);--green-fade2: hsl(var(--green-hs) var(--green-l) / 20%);--green-fade3: hsl(var(--green-hs) var(--green-l) / 5%);--primary: hsl(var(--primary-hs) var(--primary-l));--primary-darker: hsl(var(--primary-hs) calc(var(--primary-l) - 5%));--primary-darkest: hsl(var(--primary-hs) calc(var(--primary-l) - 10%));--primary-fade: hsl(var(--primary-hs) var(--primary-l) / 10%);--primary-fade2: hsl(var(--primary-hs) var(--primary-l) / 20%);--primary-fade3: hsl(var(--primary-hs) var(--primary-l) / 5%);--secondary: hsl(var(--secondary-hs) var(--secondary-l));--secondary-darker: hsl(var(--secondary-hs) calc(var(--secondary-l) - 5%));--secondary-darkest: hsl(var(--secondary-hs) calc(var(--secondary-l) - 10%));--secondary-fade: hsl(var(--secondary-hs) var(--secondary-l) / 10%);--secondary-fade2: hsl(var(--secondary-hs) var(--secondary-l) / 20%);--secondary-fade3: hsl(var(--secondary-hs) var(--secondary-l) / 5%);--secondary-fade9: hsl(var(--secondary-hs) var(--secondary-l) / 90%);--secondary-fade-solid: hsl(var(--secondary-hs) 90%);--secondary-fade-solid2: hsl(var(--secondary-hs) 90%);--tertiary: hsl(var(--tertiary-hs) var(--tertiary-l));--tertiary-darker: hsl(var(--tertiary-hs) calc(var(--tertiary-l) - 5%));--tertiary-darkest: hsl(var(--tertiary-hs) calc(var(--tertiary-l) - 10%));--tertiary-fade: hsl(var(--tertiary-hs) var(--tertiary-l) / 10%);--tertiary-fade2: hsl(var(--tertiary-hs) var(--tertiary-l) / 20%);--tertiary-fade3: hsl(var(--tertiary-hs) var(--tertiary-l) / 5%);--yellow: hsl(50, 100%, 90%);--yellow-darker: hsl(50, 100%, 75%);--yellow-darkest: hsl(50, 100%, 25%)}.recurring-jobs-grid.svelte-ffh941{display:grid;grid-template-columns:repeat(auto-fill,minmax(400px,1fr));gap:1.5rem;padding:2rem}.template-card.svelte-ffh941{background:#fff;border:1px solid var(--border);border-radius:var(--br);overflow:hidden;transition:box-shadow .2s ease}.template-card.svelte-ffh941:hover{box-shadow:0 4px 12px #0000001a}.template-card.svelte-ffh941 .card-header:where(.svelte-ffh941){display:flex;justify-content:space-between;align-items:center;padding:1.5rem;border-bottom:1px solid var(--border);background:var(--bg)}.template-card.svelte-ffh941 .card-header:where(.svelte-ffh941) h3:where(.svelte-ffh941){margin:0;color:var(--primary);font-size:1.125rem}.template-card.svelte-ffh941 .card-header:where(.svelte-ffh941) .card-actions:where(.svelte-ffh941){display:flex;gap:.5rem}.template-card.svelte-ffh941 .card-content:where(.svelte-ffh941){padding:1.5rem}.template-card.svelte-ffh941 .card-content:where(.svelte-ffh941) .template-info:where(.svelte-ffh941) p:where(.svelte-ffh941){margin:.5rem 0;font-size:.9rem}.template-card.svelte-ffh941 .card-content:where(.svelte-ffh941) .template-info:where(.svelte-ffh941) p:where(.svelte-ffh941) strong:where(.svelte-ffh941){color:var(--black)}.template-card.svelte-ffh941 .card-content:where(.svelte-ffh941) .template-info:where(.svelte-ffh941) .template-status:where(.svelte-ffh941){margin-top:1rem}.template-card.svelte-ffh941 .card-content:where(.svelte-ffh941) .template-info:where(.svelte-ffh941) .template-status:where(.svelte-ffh941) .status-badge:where(.svelte-ffh941){padding:.25rem .75rem;border-radius:12px;font-size:.8rem;font-weight:500;background:var(--grey-light);color:var(--grey)}.template-card.svelte-ffh941 .card-content:where(.svelte-ffh941) .template-info:where(.svelte-ffh941) .template-status:where(.svelte-ffh941) .status-badge.active:where(.svelte-ffh941){background:var(--green-light);color:var(--green)}.empty-state.svelte-ffh941{grid-column:1 / -1;text-align:center;padding:4rem 2rem;color:var(--grey)}.empty-state.svelte-ffh941 h3:where(.svelte-ffh941){margin:0 0 1rem;color:var(--black)}.empty-state.svelte-ffh941 p:where(.svelte-ffh941){margin:0 0 2rem}.recurring-form.svelte-ffh941 .form-section:where(.svelte-ffh941){margin-bottom:2rem;padding-bottom:1.5rem;border-bottom:1px solid var(--border)}.recurring-form.svelte-ffh941 .form-section:where(.svelte-ffh941):last-of-type{border-bottom:none;margin-bottom:0;padding-bottom:0}.recurring-form.svelte-ffh941 .form-section:where(.svelte-ffh941) h4:where(.svelte-ffh941){margin:0 0 1rem;color:var(--primary);font-size:1rem;font-weight:600}.recurring-form.svelte-ffh941 .form-row:where(.svelte-ffh941){display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:1rem}.recurring-form.svelte-ffh941 .staff-checkboxes:where(.svelte-ffh941),.recurring-form.svelte-ffh941 .resource-checkboxes:where(.svelte-ffh941){display:grid;grid-template-columns:repeat(auto-fit,minmax(200px,1fr));gap:.5rem}.recurring-form.svelte-ffh941 .checkbox-label:where(.svelte-ffh941){display:flex;align-items:center;gap:.5rem;cursor:pointer;padding:.5rem;border-radius:var(--br);transition:background-color .2s ease}.recurring-form.svelte-ffh941 .checkbox-label:where(.svelte-ffh941):hover{background:var(--bg)}.recurring-form.svelte-ffh941 .checkbox-label:where(.svelte-ffh941) input[type=checkbox]:where(.svelte-ffh941){margin:0}.recurring-form.svelte-ffh941 .days-of-week:where(.svelte-ffh941){display:flex;gap:.5rem;flex-wrap:wrap}.recurring-form.svelte-ffh941 .days-of-week:where(.svelte-ffh941) .day-checkbox:where(.svelte-ffh941){display:flex;flex-direction:column;align-items:center;gap:.25rem;cursor:pointer;padding:.5rem;border:1px solid var(--border);border-radius:var(--br);transition:all .2s ease}.recurring-form.svelte-ffh941 .days-of-week:where(.svelte-ffh941) .day-checkbox:where(.svelte-ffh941):hover{border-color:var(--primary)}.recurring-form.svelte-ffh941 .days-of-week:where(.svelte-ffh941) .day-checkbox:where(.svelte-ffh941) input[type=checkbox]:where(.svelte-ffh941){margin:0}.recurring-form.svelte-ffh941 .days-of-week:where(.svelte-ffh941) .day-checkbox:where(.svelte-ffh941) .day-label:where(.svelte-ffh941){font-size:.8rem;font-weight:500}.recurring-form.svelte-ffh941 .interval-label:where(.svelte-ffh941){margin-left:.5rem;color:var(--grey);font-size:.9rem}.recurring-form.svelte-ffh941 .form-actions:where(.svelte-ffh941){display:flex;justify-content:flex-end;gap:1rem;margin-top:2rem;padding-top:1.5rem;border-top:1px solid var(--border)}@media (max-width: 768px){.recurring-jobs-grid.svelte-ffh941{grid-template-columns:1fr;padding:1rem}.template-card.svelte-ffh941 .card-header:where(.svelte-ffh941){flex-direction:column;align-items:flex-start;gap:1rem}.template-card.svelte-ffh941 .card-header:where(.svelte-ffh941) .card-actions:where(.svelte-ffh941){align-self:stretch;justify-content:space-between}.recurring-form.svelte-ffh941 .form-row:where(.svelte-ffh941){grid-template-columns:1fr}.days-of-week.svelte-ffh941{justify-content:center}}
