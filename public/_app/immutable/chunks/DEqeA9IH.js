import{_ as ae,b as re,K as S,h as C,L as ne,a as ie,k as K,n as fe,M as le,O as se,P,Q as g,d as N,$ as ue,R as z,c as G,p as te,a0 as k,a1 as J,A as ve,F as Q,a2 as y,a3 as U,a4 as L,m as _e,a5 as X,a6 as W,a7 as de,a8 as ce,a9 as oe,aa as he,ab as Ee,ac as pe,ad as Ae,H as me}from"./p3DoyA09.js";let D=null;function we(l,e){return e}function Te(l,e,a,u){for(var _=[],c=e.length,t=0;t<c;t++)oe(e[t].e,_,!0);var o=c>0&&_.length===0&&a!==null;if(o){var m=a.parentNode;he(m),m.append(a),u.clear(),x(l,e[0].prev,e[c-1].next)}Ee(_,()=>{for(var E=0;E<c;E++){var d=e[E];o||(u.delete(d.k),x(l,d.prev,d.next)),pe(d.e,!o)}})}function He(l,e,a,u,_,c=null){var t=l,o={flags:e,items:new Map,first:null},m=(e&W)!==0;if(m){var E=l;t=C?S(ne(E)):E.appendChild(ae())}C&&ie();var d=null,w=!1,n=fe(()=>{var s=a();return de(s)?s:s==null?[]:J(s)});re(()=>{var s=K(n),i=s.length;if(w&&i===0)return;w=i===0;let v=!1;if(C){var p=le(t)===se;p!==(i===0)&&(t=P(),S(t),g(!1),v=!0)}if(C){for(var A=null,T,h=0;h<i;h++){if(N.nodeType===8&&N.data===ue){t=N,v=!0,g(!1);break}var r=s[h],f=u(r,h);T=Z(N,o,A,null,r,f,h,_,e,a),o.items.set(f,T),A=T}i>0&&S(P())}C||Ie(s,o,t,_,e,u,a),c!==null&&(i===0?d?z(d):d=G(()=>c(t)):d!==null&&te(d,()=>{d=null})),v&&g(!0),K(n)}),C&&(t=N)}function Ie(l,e,a,u,_,c,t){var q,V,Y,B;var o=(_&ce)!==0,m=(_&(y|L))!==0,E=l.length,d=e.items,w=e.first,n=w,s,i=null,v,p=[],A=[],T,h,r,f;if(o)for(f=0;f<E;f+=1)T=l[f],h=c(T,f),r=d.get(h),r!==void 0&&((q=r.a)==null||q.measure(),(v??(v=new Set)).add(r));for(f=0;f<E;f+=1){if(T=l[f],h=c(T,f),r=d.get(h),r===void 0){var j=n?n.e.nodes_start:a;i=Z(j,e,i,i===null?e.first:i.next,T,h,f,u,_,t),d.set(h,i),p=[],A=[],n=i.next;continue}if(m&&xe(r,T,f,_),(r.e.f&k)!==0&&(z(r.e),o&&((V=r.a)==null||V.unfix(),(v??(v=new Set)).delete(r))),r!==n){if(s!==void 0&&s.has(r)){if(p.length<A.length){var R=A[0],I;i=R.prev;var O=p[0],b=p[p.length-1];for(I=0;I<p.length;I+=1)$(p[I],R,a);for(I=0;I<A.length;I+=1)s.delete(A[I]);x(e,O.prev,b.next),x(e,i,O),x(e,b,R),n=R,i=b,f-=1,p=[],A=[]}else s.delete(r),$(r,n,a),x(e,r.prev,r.next),x(e,r,i===null?e.first:i.next),x(e,i,r),i=r;continue}for(p=[],A=[];n!==null&&n.k!==h;)(n.e.f&k)===0&&(s??(s=new Set)).add(n),A.push(n),n=n.next;if(n===null)continue;r=n}p.push(r),i=r,n=r.next}if(n!==null||s!==void 0){for(var H=s===void 0?[]:J(s);n!==null;)(n.e.f&k)===0&&H.push(n),n=n.next;var M=H.length;if(M>0){var ee=(_&W)!==0&&E===0?a:null;if(o){for(f=0;f<M;f+=1)(Y=H[f].a)==null||Y.measure();for(f=0;f<M;f+=1)(B=H[f].a)==null||B.fix()}Te(e,H,ee,d)}}o&&ve(()=>{var F;if(v!==void 0)for(r of v)(F=r.a)==null||F.apply()}),Q.first=e.first&&e.first.e,Q.last=i&&i.e}function xe(l,e,a,u){(u&y)!==0&&U(l.v,e),(u&L)!==0?U(l.i,a):l.i=a}function Z(l,e,a,u,_,c,t,o,m,E){var d=D,w=(m&y)!==0,n=(m&Ae)===0,s=w?n?_e(_):X(_):_,i=(m&L)===0?t:X(t),v={i,v:s,k:c,a:null,e:null,prev:a,next:u};D=v;try{return v.e=G(()=>o(l,s,i,E),C),v.e.prev=a&&a.e,v.e.next=u&&u.e,a===null?e.first=v:(a.next=v,a.e.next=v.e),u!==null&&(u.prev=v,u.e.prev=v.e),v}finally{D=d}}function $(l,e,a){for(var u=l.next?l.next.e.nodes_start:a,_=e?e.e.nodes_start:a,c=l.e.nodes_start;c!==u;){var t=me(c);_.before(c),c=t}}function x(l,e,a){e===null?l.first=a:(e.next=a,e.e.next=a&&a.e),a!==null&&(a.prev=e,a.e.prev=e&&e.e)}export{D as c,He as e,we as i};
