import{A as H,h as p,L as I,aa as M,aJ as E,aK as T,aL as j,F as P,D as K,al as Q,a7 as x,_ as F,b as z,aP as G,N as W,H as A,Q as w,K as L,d as h,aQ as N,J as S,a as U,$ as X,I as Z,aR as ee,a1 as te,aS as re,c as ae,e as ne,T as oe,g as ie}from"./p3DoyA09.js";import{c as se}from"./B67foYpL.js";function ge(e){return e.endsWith("capture")&&e!=="gotpointercapture"&&e!=="lostpointercapture"}const ue=["beforeinput","click","change","dblclick","contextmenu","focusin","focusout","input","keydown","keyup","mousedown","mousemove","mouseout","mouseover","mouseup","pointerdown","pointermove","pointerout","pointerover","pointerup","touchend","touchmove","touchstart"];function we(e){return ue.includes(e)}const le={formnovalidate:"formNoValidate",ismap:"isMap",nomodule:"noModule",playsinline:"playsInline",readonly:"readOnly",defaultvalue:"defaultValue",defaultchecked:"defaultChecked",srcobject:"srcObject",novalidate:"noValidate",allowfullscreen:"allowFullscreen",disablepictureinpicture:"disablePictureInPicture",disableremoteplayback:"disableRemotePlayback"};function be(e){return e=e.toLowerCase(),le[e]??e}const ce=["touchstart","touchmove"];function de(e){return ce.includes(e)}function me(e,t){if(t){const r=document.body;e.autofocus=!0,H(()=>{document.activeElement===r&&e.focus()})}}function Ee(e){p&&I(e)!==null&&M(e)}let V=!1;function fe(){V||(V=!0,document.addEventListener("reset",e=>{Promise.resolve().then(()=>{var t;if(!e.defaultPrevented)for(const r of e.target.elements)(t=r.__on_r)==null||t.call(r)})},{capture:!0}))}function B(e){var t=j,r=P;E(null),T(null);try{return e()}finally{E(t),T(r)}}function Te(e,t,r,n=r){e.addEventListener(t,()=>B(r));const o=e.__on_r;o?e.__on_r=()=>{o(),n(!0)}:e.__on_r=()=>n(!0),fe()}const Y=new Set,R=new Set;function _e(e,t,r,n={}){function o(a){if(n.capture||b.call(t,a),!a.cancelBubble)return B(()=>r==null?void 0:r.call(this,a))}return e.startsWith("pointer")||e.startsWith("touch")||e==="wheel"?H(()=>{t.addEventListener(e,o,n)}):t.addEventListener(e,o,n),o}function Le(e,t,r,n,o){var a={capture:n,passive:o},l=_e(e,t,r,a);(t===document.body||t===window||t===document)&&K(()=>{t.removeEventListener(e,l,a)})}function ke(e){for(var t=0;t<e.length;t++)Y.add(e[t]);for(var r of R)r(e)}function b(e){var O;var t=this,r=t.ownerDocument,n=e.type,o=((O=e.composedPath)==null?void 0:O.call(e))||[],a=o[0]||e.target,l=0,v=e.__root;if(v){var f=o.indexOf(v);if(f!==-1&&(t===document||t===window)){e.__root=t;return}var y=o.indexOf(t);if(y===-1)return;f<=y&&(l=f)}if(a=o[l]||e.target,a!==t){Q(e,"currentTarget",{configurable:!0,get(){return a||r}});var k=j,c=P;E(null),T(null);try{for(var i,s=[];a!==null;){var d=a.assignedSlot||a.parentNode||a.host||null;try{var _=a["__"+n];if(_!=null&&(!a.disabled||e.target===a))if(x(_)){var[q,...J]=_;q.apply(a,[e,...J])}else _.call(a,e)}catch(m){i?s.push(m):i=m}if(e.cancelBubble||d===t||d===null)break;a=d}if(i){for(let m of s)queueMicrotask(()=>{throw m});throw i}}finally{e.__root=t,delete e.currentTarget,E(k),T(c)}}}let u;function he(){u=void 0}function Se(e){let t=null,r=p;var n;if(p){for(t=h,u===void 0&&(u=I(document.head));u!==null&&(u.nodeType!==8||u.data!==W);)u=A(u);u===null?w(!1):u=L(A(u))}p||(n=document.head.appendChild(F()));try{z(()=>e(n),G)}finally{r&&(w(!0),u=h,L(t))}}let C=!0;function Ae(e,t){var r=t==null?"":typeof t=="object"?t+"":t;r!==(e.__t??(e.__t=e.nodeValue))&&(e.__t=r,e.nodeValue=r+"")}function pe(e,t){return $(e,t)}function Ne(e,t){N(),t.intro=t.intro??!1;const r=t.target,n=p,o=h;try{for(var a=I(r);a&&(a.nodeType!==8||a.data!==W);)a=A(a);if(!a)throw S;w(!0),L(a),U();const l=$(e,{...t,anchor:a});if(h===null||h.nodeType!==8||h.data!==X)throw Z(),S;return w(!1),l}catch(l){if(l===S)return t.recover===!1&&ee(),N(),M(r),w(!1),pe(e,t);throw l}finally{w(n),L(o),he()}}const g=new Map;function $(e,{target:t,anchor:r,props:n={},events:o,context:a,intro:l=!0}){N();var v=new Set,f=c=>{for(var i=0;i<c.length;i++){var s=c[i];if(!v.has(s)){v.add(s);var d=de(s);t.addEventListener(s,b,{passive:d});var _=g.get(s);_===void 0?(document.addEventListener(s,b,{passive:d}),g.set(s,1)):g.set(s,_+1)}}};f(te(Y)),R.add(f);var y=void 0,k=re(()=>{var c=r??t.appendChild(F());return ae(()=>{if(a){ne({});var i=oe;i.c=a}o&&(n.$$events=o),p&&se(c,null),C=l,y=e(c,n)||{},C=!0,p&&(P.nodes_end=h),a&&ie()}),()=>{var d;for(var i of v){t.removeEventListener(i,b);var s=g.get(i);--s===0?(document.removeEventListener(i,b),g.delete(i)):g.set(i,s)}R.delete(f),c!==r&&((d=c.parentNode)==null||d.removeChild(c))}});return D.set(y,k),y}let D=new WeakMap;function Re(e,t){const r=D.get(e);return r?(D.delete(e),r(t)):Promise.resolve()}export{fe as a,me as b,_e as c,ke as d,Le as e,we as f,C as g,Se as h,ge as i,Ne as j,Te as l,pe as m,be as n,Ee as r,Ae as s,Re as u,B as w};
