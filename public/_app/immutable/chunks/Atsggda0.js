import{d as c,w as i}from"./DTQDE1SG.js";import{d,u as p,c as l,g as u}from"./B_pWBBGB.js";async function y(){if((await u()).length===0){const t=[{fullName:"<PERSON>",companyName:"Smith Construction",emails:[{id:"1",email:"<EMAIL>",type:"Work",isPrimary:!0}],phones:[{id:"1",phone:"(*************",type:"Work",isPrimary:!0}],addresses:[{id:"1",street:"123 Main St",city:"Springfield",state:"IL",zipCode:"62701",country:"US",type:"Work",isPrimary:!0}],status:"Customer",notes:[],checklists:[],communicationTimeline:[]},{fullName:"Sarah Johnson",companyName:"Johnson Enterprises",emails:[{id:"1",email:"<EMAIL>",type:"Work",isPrimary:!0}],phones:[{id:"1",phone:"(*************",type:"Work",isPrimary:!0}],addresses:[{id:"1",street:"456 Oak Ave",city:"Springfield",state:"IL",zipCode:"62702",country:"US",type:"Work",isPrimary:!0}],status:"Customer",notes:[],checklists:[],communicationTimeline:[]},{fullName:"Mike Wilson",emails:[{id:"1",email:"<EMAIL>",type:"Personal",isPrimary:!0}],phones:[{id:"1",phone:"(*************",type:"Mobile",isPrimary:!0}],addresses:[{id:"1",street:"789 Pine St",city:"Springfield",state:"IL",zipCode:"62703",country:"US",type:"Home",isPrimary:!0}],status:"Lead",notes:[],checklists:[],communicationTimeline:[]}];for(const o of t)await l(o)}}const r=i([]),m=i(!1),n=i(null),f=c(r,e=>e.filter(t=>t.status==="Customer"));c(r,e=>e.filter(t=>t.status==="Lead"));const w={async loadContacts(){m.set(!0);try{await y();const e=await u();r.set(e)}catch(e){console.error("Error loading contacts:",e)}finally{m.set(!1)}},async addContact(e){try{const t=await l(e);return r.update(o=>[...o,t]),t}catch(t){throw console.error("Error adding contact:",t),t}},async updateContact(e,t){try{const o=await p(e,t);return r.update(a=>a.map(s=>s.id===e?o:s)),n.update(a=>(a==null?void 0:a.id)===e?o:a),o}catch(o){throw console.error("Error updating contact:",o),o}},async deleteContact(e){try{await d(e),r.update(t=>t.filter(o=>o.id!==e)),n.update(t=>(t==null?void 0:t.id)===e?null:t)}catch(t){throw console.error("Error deleting contact:",t),t}},selectContact(e){n.set(e)},searchContacts(e){return c(r,t=>t.filter(o=>{var a;return o.fullName.toLowerCase().includes(e.toLowerCase())||((a=o.companyName)==null?void 0:a.toLowerCase().includes(e.toLowerCase()))||o.emails.some(s=>s.email.toLowerCase().includes(e.toLowerCase()))}))}},S=i({});export{w as a,S as b,f as c,r as d};
