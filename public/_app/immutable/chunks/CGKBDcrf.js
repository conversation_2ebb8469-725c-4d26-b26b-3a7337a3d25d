import{w as c}from"./DTQDE1SG.js";import{g as u}from"./CSyJhG7e.js";const n=localStorage.getItem("user"),g=localStorage.getItem("authToken"),h=n?JSON.parse(n):null,a=c(h),s=c(g);a.subscribe(t=>{t?(localStorage.setItem("user",JSON.stringify(t)),localStorage.setItem("authToken",t.token),s.set(t.token)):(localStorage.removeItem("user"),localStorage.removeItem("authToken"),s.set(null))});async function m(t,r){try{const e=await fetch("https://app-ejp-api.azurewebsites.net/Auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:r})});if(!e.ok)return e.status===401?{success:!1,error:"Invalid email or password"}:{success:!1,error:"<PERSON><PERSON> failed. Please try again."};const o=await e.json();return a.set({email:o.email,token:o.token}),{success:!0}}catch(e){return console.error("Login error:",e),{success:!1,error:"Network error. Please check your connection."}}}function i(){a.set(null),u("/login")}async function k(t,r={}){let e=null;if(s.subscribe(l=>{e=l})(),!e)throw i(),new Error("No authentication token available");const o=await fetch(t,{...r,headers:{...r.headers,Authorization:`Bearer ${e}`,"Content-Type":"application/json"}});if(o.status===401)throw i(),new Error("Session expired. Please login again.");return o}export{k as a,i as b,m as l,a as u};
