import{a}from"./CGKBDcrf.js";const n="https://app-ejp-api-test-gpfsbbe0gdgahkhp.uksouth-01.azurewebsites.net";class i extends Error{constructor(o,s){super(s),this.status=o,this.name="ApiError"}}async function e(t,o={}){const s=`${n}${t}`;try{const r=await a(s,o);if(!r.ok)throw new i(r.status,`API call failed: ${r.statusText}`);return r.status===204?null:await r.json()}catch(r){throw r instanceof i?r:new i(500,"Network error occurred")}}const p={get:t=>e(t,{method:"GET"}),post:(t,o)=>e(t,{method:"POST",body:o?JSON.stringify(o):void 0}),put:(t,o)=>e(t,{method:"PUT",body:o?JSON.stringify(o):void 0}),patch:(t,o)=>e(t,{method:"PATCH",body:o?JSON.stringify(o):void 0}),delete:t=>e(t,{method:"DELETE"})};export{i as ApiError,p as api,e as apiCall};
