import{D as $,al as z,ag as A,m as q,k as i,j as N,am as C,an as G,ao as K,Z as D,n as Z,ap as U,aq as j,ar as y,z as g,as as V,S as F,at as H,au as J,av as Q,a5 as W,aw as X,ax as L,ay as k}from"./p3DoyA09.js";import{s as ee,g as re}from"./DTQDE1SG.js";import{l as ne}from"./DIeogL5L.js";let v=!1,E=Symbol();function fe(e,r,u){const s=u[r]??(u[r]={store:null,source:q(void 0),unsubscribe:A});if(s.store!==e&&!(E in u))if(s.unsubscribe(),s.store=e??null,e==null)s.source.v=void 0,s.unsubscribe=A;else{var o=!0;s.unsubscribe=ee(e,t=>{o?s.source.v=t:N(s.source,t)}),o=!1}return e&&E in u?re(e):i(s.source)}function ce(e,r){return e.set(r),r}function _e(){const e={};function r(){$(()=>{for(var u in e)e[u].unsubscribe();z(e,E,{enumerable:!1,value:!0})})}return[e,r]}function oe(){v=!0}function ue(e){var r=v;try{return v=!1,[e(),v]}finally{v=r}}const se={get(e,r){if(!e.exclude.includes(r))return i(e.version),r in e.special?e.special[r]():e.props[r]},set(e,r,u){return r in e.special||(e.special[r]=ae({get[r](){return e.props[r]}},r,U)),e.special[r](u),L(e.version),!0},getOwnPropertyDescriptor(e,r){if(!e.exclude.includes(r)&&r in e.props)return{enumerable:!0,configurable:!0,value:e.props[r]}},deleteProperty(e,r){return e.exclude.includes(r)||(e.exclude.push(r),L(e.version)),!0},has(e,r){return e.exclude.includes(r)?!1:r in e.props},ownKeys(e){return Reflect.ownKeys(e.props).filter(r=>!e.exclude.includes(r))}};function de(e,r){return new Proxy({props:e,exclude:r,special:{},version:W(0)},se)}function T(e){var r;return((r=e.ctx)==null?void 0:r.d)??!1}function ae(e,r,u,s){var w;var o=(u&Q)!==0,t=!ne||(u&J)!==0,p=(u&V)!==0,B=(u&X)!==0,O=!1,c;p?[c,O]=ue(()=>e[r]):c=e[r];var M=F in e||H in e,d=p&&(((w=C(e,r))==null?void 0:w.set)??(M&&r in e&&(n=>e[r]=n)))||void 0,l=s,m=!0,S=!1,R=()=>(S=!0,m&&(m=!1,B?l=g(s):l=s),l);c===void 0&&s!==void 0&&(d&&t&&G(),c=R(),d&&d(c));var f;if(t)f=()=>{var n=e[r];return n===void 0?R():(m=!0,S=!1,n)};else{var h=(o?D:Z)(()=>e[r]);h.f|=K,f=()=>{var n=i(h);return n!==void 0&&(l=void 0),n===void 0?l:n}}if((u&U)===0)return f;if(d){var Y=e.$$legacy;return function(n,_){return arguments.length>0?((!t||!_||Y||O)&&d(_?f():n),n):f()}}var b=!1,x=!1,P=q(c),a=D(()=>{var n=f(),_=i(P);return b?(b=!1,x=!0,_):(x=!1,P.v=n)});return p&&i(a),o||(a.equals=j),function(n,_){if(k!==null&&(b=x,f(),i(P)),arguments.length>0){const I=_?i(a):t&&p?y(n):n;if(!a.equals(I)){if(b=!0,N(P,I),S&&l!==void 0&&(l=I),T(a))return n;g(()=>i(a))}return n}return T(a)?a.v:i(a)}}export{fe as a,ce as b,de as l,oe as m,ae as p,_e as s};
