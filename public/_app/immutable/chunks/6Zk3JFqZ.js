import"./CWj6FrbW.js";import"./69_IOA4Y.js";import{e as x,s as C,t as D,g as E,i as c,r as m}from"./p3DoyA09.js";import{e as P}from"./BUelSUke.js";import{t as d,a as p}from"./B67foYpL.js";import{i as q}from"./DwdToawP.js";import{h as w}from"./FLKOLPZJ.js";import{s as A}from"./D5jlwPKM.js";import{c as F}from"./DdRd56Yq.js";import{i as G}from"./D3pqaimu.js";import{l as f,p as o}from"./qYb16FSw.js";import{c as H}from"./C_WNR8j8.js";import"./C8F602cz.js";var I=d('<span class="icon"><!></span>'),J=d("<button><!> <!></button>");function Y(u,t){const v=f(t,["children","$$slots","$$events","$$legacy"]),b=f(v,["type","variant","size","disabled","icon"]);x(t,!1);let y=o(t,"type",8,"button"),h=o(t,"variant",8,"primary"),_=o(t,"size",8,"default"),r=o(t,"disabled",8,!1),e=o(t,"icon",8,null);const g=H();function k(i){r()||g("click",i)}G();var a=J();let n;var l=c(a);{var z=i=>{var s=I(),j=c(s);w(j,()=>typeof e()=="function"?e()():typeof e()=="string"?e():typeof e()=="object"&&e().render?e().render():""),m(s),p(i,s)};q(l,i=>{e()&&i(z)})}var B=C(l,2);A(B,t,"default",{},null),m(a),D(()=>n=F(a,n,{type:y(),class:`button ${h()??""} ${_()??""}`,disabled:r(),...b},"svelte-dc6hom")),P("click",a,k),p(u,a),E()}export{Y as B};
