import"./CWj6FrbW.js";import"./69_IOA4Y.js";import{a7 as B,e as F,u as G,v as H,f as I,g as J,j as K,m as L,w as x,k as N,i,r as l,s as k,t as Q}from"./p3DoyA09.js";import{s as R,e as r}from"./BUelSUke.js";import{i as S}from"./DwdToawP.js";import{e as U,a as h,t as g}from"./B67foYpL.js";import{s as C}from"./D5jlwPKM.js";import{t as E,s as W,a as X}from"./nu3w91fk.js";import{s as O}from"./Bfc47y5P.js";import{i as Y}from"./D3pqaimu.js";import{p as y}from"./qYb16FSw.js";import{c as Z}from"./C_WNR8j8.js";function j(t,a){var v;var s=(v=t.$$events)==null?void 0:v[a.type],c=B(s)?s.slice():s==null?[]:[s];for(var n of c)n.call(this,a)}function ua(t){const a=t-1;return a*a*a+1}function $(t){return--t*t*t*t*t+1}var aa=g("<p>Modal body content goes here.</p>"),ta=g("<button>Close</button>"),ea=g('<div class="modal-backdrop svelte-c0hahk" role="presentation"><div class="modal-content svelte-c0hahk" role="dialog" aria-modal="true" tabindex="-1"><div class="modal-header svelte-c0hahk"><h2 class="svelte-c0hahk"> </h2> <button class="close-button svelte-c0hahk" aria-label="Close modal">&times;</button></div> <div class="modal-body svelte-c0hahk"><!></div> <div class="modal-footer svelte-c0hahk"><!></div></div></div>');function pa(t,a){F(a,!1);const s=L();let c=y(a,"show",12,!1),n=y(a,"showModal",12,!1),v=y(a,"title",8,"Modal Title");const q=Z();function f(){c(!1),n(!1),q("close")}G(()=>(x(c()),x(n())),()=>{K(s,c()||n())}),H(),Y();var w=U(),D=I(w);{var P=u=>{var o=ea(),d=i(o),p=i(d),_=i(p),T=i(_,!0);l(_);var V=k(_,2);l(p);var b=k(p,2),z=i(b);C(z,a,"default",{},e=>{var m=aa();h(e,m)}),l(b);var M=k(b,2),A=i(M);C(A,a,"footer",{},e=>{var m=ta();r("click",m,f),h(e,m)}),l(M),l(d),l(o),Q(()=>R(T,v())),r("click",V,f),r("click",d,O(function(e){j.call(this,a,e)})),r("keydown",d,O(function(e){j.call(this,a,e)})),E(3,d,()=>W,()=>({duration:300,delay:150,opacity:0,start:.7,easing:$})),r("click",o,f),r("keydown",o,e=>e.key==="Escape"&&f()),E(3,o,()=>X,()=>({duration:300})),h(u,o)};S(D,u=>{N(s)&&u(P)})}h(t,w),J()}export{pa as M,j as b,ua as c};
