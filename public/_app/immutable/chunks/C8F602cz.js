import"./CWj6FrbW.js";import"./69_IOA4Y.js";import{i as o,s as _,r as m,t as p}from"./p3DoyA09.js";import{s as b}from"./BUelSUke.js";import{i as j}from"./DwdToawP.js";import{t as n,a as v}from"./B67foYpL.js";import{s as u}from"./D7jLSc-x.js";import{p as l}from"./qYb16FSw.js";var w=n('<p class="loading-message svelte-zwj7bz"> </p>'),x=n('<div class="loading-container svelte-zwj7bz"><div></div> <!></div>');function C(d,i){let t=l(i,"message",8,"Loading..."),f=l(i,"size",8,"medium");var e=x(),r=o(e),g=_(r,2);{var c=a=>{var s=w(),z=o(s,!0);m(s),p(()=>b(z,t())),v(a,s)};j(g,a=>{t()&&a(c)})}m(e),p(()=>u(r,1,`loading-spinner ${f()??""}`,"svelte-zwj7bz")),v(d,e)}export{C as L};
