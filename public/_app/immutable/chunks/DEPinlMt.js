import"./CWj6FrbW.js";import"./69_IOA4Y.js";import{e as ze,u as se,j as ie,m as ne,w as D,k as t,v as Me,i as b,s as y,r as o,t as p,g as Se,f as H,n as O}from"./p3DoyA09.js";import{s as M,e as P}from"./BUelSUke.js";import{i as C}from"./DwdToawP.js";import{e as Q}from"./DEqeA9IH.js";import{t as _,a as c,e as we,b as Ce}from"./B67foYpL.js";import{s as qe}from"./D5jlwPKM.js";import{d as J,r as Ie,s as U,a as Te}from"./DdRd56Yq.js";import{s as le}from"./D7jLSc-x.js";import{i as Qe,s as Fe}from"./DSjDIsro.js";import{i as Ke}from"./D3pqaimu.js";import{p as f}from"./qYb16FSw.js";var Ne=_('<label class="svelte-bcvkzs"> </label> <input type="text" class="svelte-bcvkzs">',1),Re=_('<button type="button" class="button secondary svelte-bcvkzs">Search</button>'),Ee=_("<option> </option>"),Ge=_("<div> </div>"),He=_('<button type="button" title="First Page" class="svelte-bcvkzs">«</button> <button type="button" title="Previous Page" class="svelte-bcvkzs">Prev</button>',1),Oe=_('<button type="button"> </button>'),Le=_('<button type="button" class="navigate_next svelte-bcvkzs" title="Next Page">Next</button> <button type="button" class="last_page svelte-bcvkzs" title="Last Page">»</button>',1),Ve=_('<div class="grid-top svelte-bcvkzs"><div class="grid-search svelte-bcvkzs"><!> <!></div> <div class="grid-search-and-items-per-page svelte-bcvkzs"><select title="Records per Page" class="svelte-bcvkzs"></select> <!></div> <div class="grid-pager svelte-bcvkzs"><!> <!> <!></div> <div class="svelte-bcvkzs"> </div></div>'),je=_('<button type="button" role="columnheader"> </button>'),Ae=_('<div class="grid-row-header svelte-bcvkzs"></div>'),Be=_('<div class="cell svelte-bcvkzs"><!></div>'),De=_('<div class="grid-row svelte-bcvkzs"></div>'),Je=_('<div class="grid-row-empty svelte-bcvkzs"> </div>'),Ue=_('<div class="grid svelte-bcvkzs"><!> <!> <!></div>');function vt(ve,u){ze(u,!1);const h=ne(),W=ne();let q=f(u,"headers",24,()=>[]),X=f(u,"dataRows",24,()=>[]),oe=f(u,"emptyMessage",8,"No data available."),ce=f(u,"onHeaderClick",8,()=>{}),S=f(u,"currentSort",24,()=>({key:"",direction:""})),w=f(u,"searchFields",28,()=>[]),Y=f(u,"itemsPerPageOptions",24,()=>[10,25,50,100]),I=f(u,"itemsPerPage",8,25),g=f(u,"currentPage",8,1),E=f(u,"totalItems",8,0),ue=f(u,"onSearch",8,()=>{}),de=f(u,"onItemsPerPageChange",8,()=>{}),_e=f(u,"onPageChange",8,()=>{});function Z(a,e){return e?e.split(".").reduce((l,i)=>l&&l[i],a):""}function ge(a,e){w()[a]&&w(w()[a].currentQuery=e,!0)}function $(){const a={};w().forEach(e=>{e.currentQuery&&e.currentQuery.trim()!==""&&(a[e.queryKey]=e.currentQuery)}),ue()(a)}function me(a){const e=a.target;de()(parseInt(e.value,10))}function F(a){let e=g();a==="first"?e=1:a==="prev"?e=Math.max(1,g()-1):a==="next"?e=Math.min(t(h),g()+1):a==="last"?e=t(h):typeof a=="number"&&(e=Math.max(1,Math.min(t(h),a))),e!==g()&&_e()(e)}se(()=>(D(E()),D(I())),()=>{ie(h,Math.ceil(E()/I())||1)}),se(()=>(D(g()),t(h)),()=>{ie(W,(()=>{const e=Math.floor(2.5);let l=Math.max(1,g()-e),i=Math.min(t(h),g()+e);g()-e<1&&(i=Math.min(t(h),l+5-1)),g()+e>t(h)&&(l=Math.max(1,i-5+1));const v=[];for(let m=l;m<=i;m++)v.push(m);return v})())}),Me(),Ke();var G=Ue(),ee=b(G);{var be=a=>{var e=Ve(),l=b(e),i=b(l);Q(i,3,w,r=>r.queryKey,(r,s,n)=>{var k=Ne(),z=H(k),A=b(z,!0);o(z);var R=y(z,2);Ie(R),p(()=>{U(z,"for",`search-${t(s).queryKey}`),M(A,t(s).displayName),U(R,"id",`search-${t(s).queryKey}`),Te(R,t(s).currentQuery)}),P("input",R,B=>ge(t(n),B.currentTarget.value)),P("keypress",R,B=>B.key==="Enter"&&$()),c(r,k)});var v=y(i,2);{var m=r=>{var s=Re();P("click",s,$),c(r,s)};C(v,r=>{w().length>0&&r(m)})}o(l);var x=y(l,2),d=b(x);Qe(d,I);var T;Q(d,5,Y,r=>r,(r,s)=>{var n=Ee(),k={},z=b(n);o(n),p(()=>{k!==(k=t(s))&&(n.value=(n.__value=t(s))??""),M(z,`${t(s)??""} per page`)}),c(r,n)}),o(d);var L=y(d,2);{var V=r=>{var s=Ge(),n=b(s);o(s),p(()=>M(n,`${g()??""} / ${t(h)??""}`)),c(r,s)};C(L,r=>{E()>0&&r(V)})}o(x);var K=y(x,2),N=b(K);{var j=r=>{var s=He(),n=H(s),k=y(n,2);P("click",n,()=>F("first")),P("click",k,()=>F("prev")),c(r,s)};C(N,r=>{g()!==1&&r(j)})}var ae=y(N,2);Q(ae,1,()=>t(W),r=>r,(r,s)=>{var n=Oe();let k;var z=b(n,!0);o(n),p(A=>{k=le(n,1,"page_number svelte-bcvkzs",null,k,A),M(z,t(s))},[()=>({selected:t(s)===g()})],O),P("click",n,()=>F(t(s))),c(r,n)});var pe=y(ae,2);{var Pe=r=>{var s=Le(),n=H(s),k=y(n,2);P("click",n,()=>F("next")),P("click",k,()=>F("last")),c(r,s)};C(pe,r=>{g()!==t(h)&&t(h)>0&&r(Pe)})}o(K);var re=y(K,2),xe=b(re);o(re),o(e),p(()=>{T!==(T=I())&&(d.value=(d.__value=I())??"",Fe(d,I())),M(xe,`${E()??""} results`)}),P("change",d,me),c(a,e)};C(ee,a=>{(w().length>0||Y().length>0)&&a(be)})}var te=y(ee,2);{var fe=a=>{var e=Ae();Q(e,5,q,l=>l.key,(l,i)=>{var v=je();let m;var x=b(v,!0);o(v),p(d=>{m=le(v,1,"header-cell svelte-bcvkzs",null,m,d),U(v,"aria-sort",S().key===t(i).key?S().direction==="ascending"?"ascending":"descending":"none"),v.disabled=!t(i).sortable,M(x,t(i).text)},[()=>({"is-sorting":S().key===t(i).key,ascending:S().key===t(i).key&&S().direction==="ascending",descending:S().key===t(i).key&&S().direction==="descending"})],O),P("click",v,()=>t(i).sortable&&ce()(t(i).key)),c(l,v)}),o(e),p(()=>J(e,`grid-template-columns: repeat(${q().length}, 1fr);`)),c(a,e)};C(te,a=>{q().length>0&&a(fe)})}var ke=y(te,2);{var ye=a=>{var e=we(),l=H(e);Q(l,3,X,(i,v)=>i.id||v,(i,v)=>{var m=De();Q(m,5,q,x=>x.key,(x,d)=>{var T=Be(),L=b(T);const V=O(()=>Z(t(v),t(d).key));qe(L,u,"cell",{get row(){return t(v)},get headerKey(){return t(d).key},get value(){return t(V)}},K=>{var N=Ce();p(j=>M(N,j),[()=>Z(t(v),t(d).key)],O),c(K,N)}),o(T),c(x,T)}),o(m),p(()=>J(m,`grid-template-columns: repeat(${q().length}, 1fr);`)),c(i,m)}),c(a,e)},he=a=>{var e=Je(),l=b(e,!0);o(e),p(()=>M(l,oe())),c(a,e)};C(ke,a=>{X().length>0?a(ye):a(he,!1)})}o(G),p(()=>J(G,`--grid-column-count: ${q().length||1}`)),c(ve,G),Se()}export{vt as G};
