import{F as T,E as I,aF as q,aG as G,x as K,z as M,aH as P,aI as j,A as H,ag as w,aJ as R,aK as B,aL as J}from"./p3DoyA09.js";import{g as U,w as W}from"./BUelSUke.js";import{c as D}from"./DEqeA9IH.js";const Q=()=>performance.now(),m={tick:r=>requestAnimationFrame(r),now:()=>Q(),tasks:new Set};function O(){const r=m.now();m.tasks.forEach(t=>{t.c(r)||(m.tasks.delete(t),t.f())}),m.tasks.size!==0&&m.tick(O)}function V(r){let t;return m.tasks.size===0&&m.tick(O),{promise:new Promise(i=>{m.tasks.add(t={c:r,f:i})}),abort(){m.tasks.delete(t)}}}function b(r,t){W(()=>{r.dispatchEvent(new CustomEvent(t))})}function X(r){if(r==="float")return"cssFloat";if(r==="offset")return"cssOffset";if(r.startsWith("--"))return r;const t=r.split("-");return t.length===1?t[0]:t[0]+t.slice(1).map(i=>i[0].toUpperCase()+i.slice(1)).join("")}function N(r){const t={},i=r.split(";");for(const a of i){const[n,e]=a.split(":");if(!n||e===void 0)break;const o=X(n.trim());t[o]=e.trim()}return t}const Y=r=>r;function at(r,t,i){var a=D,n,e,o,v=null;a.a??(a.a={element:r,measure(){n=this.element.getBoundingClientRect()},apply(){if(o==null||o.abort(),e=this.element.getBoundingClientRect(),n.left!==e.left||n.right!==e.right||n.top!==e.top||n.bottom!==e.bottom){const d=t()(this.element,{from:n,to:e},i==null?void 0:i());o=C(this.element,d,void 0,1,()=>{o==null||o.abort(),o=void 0})}},fix(){if(!r.getAnimations().length){var{position:d,width:f,height:s}=getComputedStyle(r);if(d!=="absolute"&&d!=="fixed"){var u=r.style;v={position:u.position,width:u.width,height:u.height,transform:u.transform},u.position="absolute",u.width=f,u.height=s;var h=r.getBoundingClientRect();if(n.left!==h.left||n.top!==h.top){var p=`translate(${n.left-h.left}px, ${n.top-h.top}px)`;u.transform=u.transform?`${u.transform} ${p}`:p}}}},unfix(){if(v){var d=r.style;d.position=v.position,d.width=v.width,d.height=v.height,d.transform=v.transform}}}),a.a.element=r}function nt(r,t,i,a){var n=(r&P)!==0,e="both",o,v=t.inert,d=t.style.overflow,f,s;function u(){var l=J,g=T;R(null),B(null);try{return o??(o=i()(t,(a==null?void 0:a())??{},{direction:e}))}finally{R(l),B(g)}}var h={is_global:n,in(){t.inert=v,b(t,"introstart"),f=C(t,u(),s,1,()=>{b(t,"introend"),f==null||f.abort(),f=o=void 0,t.style.overflow=d})},out(l){t.inert=!0,b(t,"outrostart"),s=C(t,u(),f,0,()=>{b(t,"outroend"),l==null||l()})},stop:()=>{f==null||f.abort(),s==null||s.abort()}},p=T;if((p.transitions??(p.transitions=[])).push(h),U){var _=n;if(!_){for(var c=p.parent;c&&(c.f&I)!==0;)for(;(c=c.parent)&&(c.f&q)===0;);_=!c||(c.f&G)!==0}_&&K(()=>{M(()=>h.in())})}}function C(r,t,i,a,n){var e=a===1;if(j(t)){var o,v=!1;return H(()=>{if(!v){var l=t({direction:e?"in":"out"});o=C(r,l,i,a,n)}}),{abort:()=>{v=!0,o==null||o.abort()},deactivate:()=>o.deactivate(),reset:()=>o.reset(),t:()=>o.t()}}if(i==null||i.deactivate(),!(t!=null&&t.duration))return n(),{abort:w,deactivate:w,reset:w,t:()=>a};const{delay:d=0,css:f,tick:s,easing:u=Y}=t;var h=[];if(e&&i===void 0&&(s&&s(0,1),f)){var p=N(f(0,1));h.push(p,p)}var _=()=>1-a,c=r.animate(h,{duration:d});return c.onfinish=()=>{var l=(i==null?void 0:i.t())??1-a;i==null||i.abort();var g=a-l,y=t.duration*Math.abs(g),E=[];if(y>0){var F=!1;if(f)for(var k=Math.ceil(y/16.666666666666668),x=0;x<=k;x+=1){var A=l+g*u(x/k),S=N(f(A,1-A));E.push(S),F||(F=S.overflow==="hidden")}F&&(r.style.overflow="hidden"),_=()=>{var $=c.currentTime;return l+g*u($/y)},s&&V(()=>{if(c.playState!=="running")return!1;var $=_();return s($,1-$),!0})}c=r.animate(E,{duration:y,fill:"forwards"}),c.onfinish=()=>{_=()=>a,s==null||s(a,1-a),n()}},{abort:()=>{c&&(c.cancel(),c.effect=null,c.onfinish=w)},deactivate:()=>{n=w},reset:()=>{a===0&&(s==null||s(1,0))},t:()=>_()}}const Z=r=>r;function z(r){const t=r-1;return t*t*t+1}function L(r){const t=typeof r=="string"&&r.match(/^\s*(-?[\d.]+)([^\s]*)\s*$/);return t?[parseFloat(t[1]),t[2]||"px"]:[r,"px"]}function ot(r,{delay:t=0,duration:i=400,easing:a=Z}={}){const n=+getComputedStyle(r).opacity;return{delay:t,duration:i,easing:a,css:e=>`opacity: ${e*n}`}}function st(r,{delay:t=0,duration:i=400,easing:a=z,x:n=0,y:e=0,opacity:o=0}={}){const v=getComputedStyle(r),d=+v.opacity,f=v.transform==="none"?"":v.transform,s=d*(1-o),[u,h]=L(n),[p,_]=L(e);return{delay:t,duration:i,easing:a,css:(c,l)=>`
			transform: ${f} translate(${(1-c)*u}${h}, ${(1-c)*p}${_});
			opacity: ${d-s*l}`}}function et(r,{delay:t=0,duration:i=400,easing:a=z,start:n=0,opacity:e=0}={}){const o=getComputedStyle(r),v=+o.opacity,d=o.transform==="none"?"":o.transform,f=1-n,s=v*(1-e);return{delay:t,duration:i,easing:a,css:(u,h)=>`
			transform: ${d} scale(${1-f*h});
			opacity: ${v-s*h}
		`}}export{ot as a,at as b,st as f,et as s,nt as t};
