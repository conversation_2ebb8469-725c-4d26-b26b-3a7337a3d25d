import{l as xt,t as hn}from"./DIeogL5L.js";const Ct=!1;var dn=Array.isArray,wn=Array.prototype.indexOf,re=Array.from,ae=Object.defineProperty,$=Object.getOwnPropertyDescriptor,yn=Object.getOwnPropertyDescriptors,En=Object.prototype,gn=Array.prototype,Ht=Object.getPrototypeOf,Pt=Object.isExtensible;function le(t){return typeof t=="function"}const se=()=>{};function fe(t){return t()}function Yt(t){for(var e=0;e<t.length;e++)t[e]()}const A=2,Bt=4,ot=8,bt=16,S=32,G=64,rt=128,T=256,at=512,y=1024,N=2048,P=4096,B=8192,_t=16384,Tn=32768,Ut=65536,mn=1<<17,An=1<<19,Gt=1<<20,Et=1<<21,M=Symbol("$state"),ie=Symbol("legacy props"),ue=Symbol("");function Kt(t){return t===this.v}function xn(t,e){return t!=t?e==e:t!==e||t!==null&&typeof t=="object"||typeof t=="function"}function Vt(t){return!xn(t,this.v)}function bn(t){throw new Error("https://svelte.dev/e/effect_in_teardown")}function In(){throw new Error("https://svelte.dev/e/effect_in_unowned_derived")}function On(t){throw new Error("https://svelte.dev/e/effect_orphan")}function Rn(){throw new Error("https://svelte.dev/e/effect_update_depth_exceeded")}function oe(){throw new Error("https://svelte.dev/e/hydration_failed")}function _e(t){throw new Error("https://svelte.dev/e/props_invalid_value")}function Nn(){throw new Error("https://svelte.dev/e/state_descriptors_fixed")}function Sn(){throw new Error("https://svelte.dev/e/state_prototype_fixed")}function kn(){throw new Error("https://svelte.dev/e/state_unsafe_mutation")}const ce=1,ve=2,pe=4,he=8,de=16,we=1,ye=2,Ee=4,ge=8,Te=16,me=4,Ae=1,xe=2,Dn="[",Cn="[!",Pn="]",It={},E=Symbol(),be="http://www.w3.org/1999/xhtml",Ie="@attach";let h=null;function Ft(t){h=t}function Oe(t,e=!1,n){var r=h={p:h,c:null,d:!1,e:null,m:!1,s:t,x:null,l:null};xt&&!e&&(h.l={s:null,u:null,r1:[],r2:Rt(!1)}),jn(()=>{r.d=!0})}function Re(t){const e=h;if(e!==null){const u=e.e;if(u!==null){var n=p,r=v;e.e=null;try{for(var a=0;a<u.length;a++){var l=u[a];ft(l.effect),U(l.reaction),tn(l.fn)}}finally{ft(n),U(r)}}h=e.p,e.m=!0}return{}}function ct(){return!xt||h!==null&&h.l===null}function Y(t){if(typeof t!="object"||t===null||M in t)return t;const e=Ht(t);if(e!==En&&e!==gn)return t;var n=new Map,r=dn(t),a=k(0),l=v,u=i=>{var s=v;U(l);var f=i();return U(s),f};return r&&n.set("length",k(t.length)),new Proxy(t,{defineProperty(i,s,f){(!("value"in f)||f.configurable===!1||f.enumerable===!1||f.writable===!1)&&Nn();var _=n.get(s);return _===void 0?(_=u(()=>k(f.value)),n.set(s,_)):I(_,u(()=>Y(f.value))),!0},deleteProperty(i,s){var f=n.get(s);if(f===void 0)s in i&&(n.set(s,u(()=>k(E))),yt(a));else{if(r&&typeof s=="string"){var _=n.get("length"),o=Number(s);Number.isInteger(o)&&o<_.v&&I(_,o)}I(f,E),yt(a)}return!0},get(i,s,f){var b;if(s===M)return t;var _=n.get(s),o=s in i;if(_===void 0&&(!o||(b=$(i,s))!=null&&b.writable)&&(_=u(()=>k(Y(o?i[s]:E))),n.set(s,_)),_!==void 0){var c=C(_);return c===E?void 0:c}return Reflect.get(i,s,f)},getOwnPropertyDescriptor(i,s){var f=Reflect.getOwnPropertyDescriptor(i,s);if(f&&"value"in f){var _=n.get(s);_&&(f.value=C(_))}else if(f===void 0){var o=n.get(s),c=o==null?void 0:o.v;if(o!==void 0&&c!==E)return{enumerable:!0,configurable:!0,value:c,writable:!0}}return f},has(i,s){var c;if(s===M)return!0;var f=n.get(s),_=f!==void 0&&f.v!==E||Reflect.has(i,s);if(f!==void 0||p!==null&&(!_||(c=$(i,s))!=null&&c.writable)){f===void 0&&(f=u(()=>k(_?Y(i[s]):E)),n.set(s,f));var o=C(f);if(o===E)return!1}return _},set(i,s,f,_){var Dt;var o=n.get(s),c=s in i;if(r&&s==="length")for(var b=f;b<o.v;b+=1){var tt=n.get(b+"");tt!==void 0?I(tt,E):b in i&&(tt=u(()=>k(E)),n.set(b+"",tt))}o===void 0?(!c||(Dt=$(i,s))!=null&&Dt.writable)&&(o=u(()=>k(void 0)),I(o,u(()=>Y(f))),n.set(s,o)):(c=o.v!==E,I(o,u(()=>Y(f))));var nt=Reflect.getOwnPropertyDescriptor(i,s);if(nt!=null&&nt.set&&nt.set.call(_,f),!c){if(r&&typeof s=="string"){var kt=n.get("length"),wt=Number(s);Number.isInteger(wt)&&wt>=kt.v&&I(kt,wt+1)}yt(a)}return!0},ownKeys(i){C(a);var s=Reflect.ownKeys(i).filter(o=>{var c=n.get(o);return c===void 0||c.v!==E});for(var[f,_]of n)_.v!==E&&!(f in i)&&s.push(f);return s},setPrototypeOf(){Sn()}})}function yt(t,e=1){I(t,t.v+e)}function Mt(t){try{if(t!==null&&typeof t=="object"&&M in t)return t[M]}catch{}return t}function Ne(t,e){return Object.is(Mt(t),Mt(e))}function Ot(t){var e=A|N,n=v!==null&&(v.f&A)!==0?v:null;return p===null||n!==null&&(n.f&T)!==0?e|=T:p.f|=Gt,{ctx:h,deps:null,effects:null,equals:Kt,f:e,fn:t,reactions:null,rv:0,v:null,wv:0,parent:n??p}}function Se(t){const e=Ot(t);return un(e),e}function ke(t){const e=Ot(t);return e.equals=Vt,e}function $t(t){var e=t.effects;if(e!==null){t.effects=null;for(var n=0;n<e.length;n+=1)j(e[n])}}function Fn(t){for(var e=t.parent;e!==null;){if((e.f&A)===0)return e;e=e.parent}return null}function Zt(t){var e,n=p;ft(Fn(t));try{$t(t),e=vn(t)}finally{ft(n)}return e}function zt(t){var e=Zt(t),n=(D||(t.f&T)!==0)&&t.deps!==null?P:y;x(t,n),t.equals(e)||(t.v=e,t.wv=_n())}const z=new Map;function Rt(t,e){var n={f:0,v:t,reactions:null,equals:Kt,rv:0,wv:0};return n}function k(t,e){const n=Rt(t);return un(n),n}function De(t,e=!1){var r;const n=Rt(t);return e||(n.equals=Vt),xt&&h!==null&&h.l!==null&&((r=h.l).s??(r.s=[])).push(n),n}function Ce(t,e){return I(t,dt(()=>C(t))),e}function I(t,e,n=!1){v!==null&&!R&&ct()&&(v.f&(A|bt))!==0&&!(w!=null&&w.includes(t))&&kn();let r=n?Y(e):e;return gt(t,r)}function gt(t,e){if(!t.equals(e)){var n=t.v;X?z.set(t,e):z.set(t,n),t.v=e,(t.f&A)!==0&&((t.f&N)!==0&&Zt(t),x(t,(t.f&T)===0?y:P)),t.wv=_n(),Jt(t,N),ct()&&p!==null&&(p.f&y)!==0&&(p.f&(S|G))===0&&(m===null?$n([t]):m.push(t))}return e}function Pe(t,e=1){var n=C(t),r=e===1?n++:n--;return I(t,n),r}function Jt(t,e){var n=t.reactions;if(n!==null)for(var r=ct(),a=n.length,l=0;l<a;l++){var u=n[l],i=u.f;(i&N)===0&&(!r&&u===p||(x(u,e),(i&(y|T))!==0&&((i&A)!==0?Jt(u,P):ht(u))))}}function Nt(t){console.warn("https://svelte.dev/e/hydration_mismatch")}let q=!1;function Fe(t){q=t}let O;function J(t){if(t===null)throw Nt(),It;return O=t}function Me(){return J(H(O))}function Le(t){if(q){if(H(O)!==null)throw Nt(),It;O=t}}function qe(t=1){if(q){for(var e=t,n=O;e--;)n=H(n);O=n}}function je(){for(var t=0,e=O;;){if(e.nodeType===8){var n=e.data;if(n===Pn){if(t===0)return e;t-=1}else(n===Dn||n===Cn)&&(t+=1)}var r=H(e);e.remove(),e=r}}function He(t){if(!t||t.nodeType!==8)throw Nt(),It;return t.data}var Lt,Mn,Ln,Qt,Wt;function Ye(){if(Lt===void 0){Lt=window,Mn=document,Ln=/Firefox/.test(navigator.userAgent);var t=Element.prototype,e=Node.prototype,n=Text.prototype;Qt=$(e,"firstChild").get,Wt=$(e,"nextSibling").get,Pt(t)&&(t.__click=void 0,t.__className=void 0,t.__attributes=null,t.__style=void 0,t.__e=void 0),Pt(n)&&(n.__t=void 0)}}function Tt(t=""){return document.createTextNode(t)}function mt(t){return Qt.call(t)}function H(t){return Wt.call(t)}function Be(t,e){if(!q)return mt(t);var n=mt(O);if(n===null)n=O.appendChild(Tt());else if(e&&n.nodeType!==3){var r=Tt();return n==null||n.before(r),J(r),r}return J(n),n}function Ue(t,e){if(!q){var n=mt(t);return n instanceof Comment&&n.data===""?H(n):n}return O}function Ge(t,e=1,n=!1){let r=q?O:t;for(var a;e--;)a=r,r=H(r);if(!q)return r;var l=r==null?void 0:r.nodeType;if(n&&l!==3){var u=Tt();return r===null?a==null||a.after(u):r.before(u),J(u),u}return J(r),r}function Ke(t){t.textContent=""}function Xt(t){p===null&&v===null&&On(),v!==null&&(v.f&T)!==0&&p===null&&In(),X&&bn()}function qn(t,e){var n=e.last;n===null?e.last=e.first=t:(n.next=t,t.prev=n,e.last=t)}function K(t,e,n,r=!0){var a=p,l={ctx:h,deps:null,nodes_start:null,nodes_end:null,f:t|N,first:null,fn:e,last:null,next:null,parent:a,prev:null,teardown:null,transitions:null,wv:0};if(n)try{pt(l),l.f|=Tn}catch(s){throw j(l),s}else e!==null&&ht(l);var u=n&&l.deps===null&&l.first===null&&l.nodes_start===null&&l.teardown===null&&(l.f&(Gt|rt))===0;if(!u&&r&&(a!==null&&qn(l,a),v!==null&&(v.f&A)!==0)){var i=v;(i.effects??(i.effects=[])).push(l)}return l}function jn(t){const e=K(ot,null,!1);return x(e,y),e.teardown=t,e}function Ve(t){Xt();var e=p!==null&&(p.f&S)!==0&&h!==null&&!h.m;if(e){var n=h;(n.e??(n.e=[])).push({fn:t,effect:p,reaction:v})}else{var r=tn(t);return r}}function $e(t){return Xt(),St(t)}function Ze(t){const e=K(G,t,!0);return(n={})=>new Promise(r=>{n.outro?Un(e,()=>{j(e),r(void 0)}):(j(e),r(void 0))})}function tn(t){return K(Bt,t,!1)}function ze(t,e){var n=h,r={effect:null,ran:!1};n.l.r1.push(r),r.effect=St(()=>{t(),!r.ran&&(r.ran=!0,I(n.l.r2,!0),dt(e))})}function Je(){var t=h;St(()=>{if(C(t.l.r2)){for(var e of t.l.r1){var n=e.effect;(n.f&y)!==0&&x(n,P),V(n)&&pt(n),e.ran=!1}t.l.r2.v=!1}})}function St(t){return K(ot,t,!0)}function Qe(t,e=[],n=Ot){const r=e.map(n);return Hn(()=>t(...r.map(C)))}function Hn(t,e=0){return K(ot|bt|e,t,!0)}function We(t,e=!0){return K(ot|S,t,!0,e)}function nn(t){var e=t.teardown;if(e!==null){const n=X,r=v;qt(!0),U(null);try{e.call(null)}finally{qt(n),U(r)}}}function en(t,e=!1){var n=t.first;for(t.first=t.last=null;n!==null;){var r=n.next;(n.f&G)!==0?n.parent=null:j(n,e),n=r}}function Yn(t){for(var e=t.first;e!==null;){var n=e.next;(e.f&S)===0&&j(e),e=n}}function j(t,e=!0){var n=!1;(e||(t.f&An)!==0)&&t.nodes_start!==null&&(Bn(t.nodes_start,t.nodes_end),n=!0),en(t,e&&!n),ut(t,0),x(t,_t);var r=t.transitions;if(r!==null)for(const l of r)l.stop();nn(t);var a=t.parent;a!==null&&a.first!==null&&rn(t),t.next=t.prev=t.teardown=t.ctx=t.deps=t.fn=t.nodes_start=t.nodes_end=null}function Bn(t,e){for(;t!==null;){var n=t===e?null:H(t);t.remove(),t=n}}function rn(t){var e=t.parent,n=t.prev,r=t.next;n!==null&&(n.next=r),r!==null&&(r.prev=n),e!==null&&(e.first===t&&(e.first=r),e.last===t&&(e.last=n))}function Un(t,e){var n=[];an(t,n,!0),Gn(n,()=>{j(t),e&&e()})}function Gn(t,e){var n=t.length;if(n>0){var r=()=>--n||e();for(var a of t)a.out(r)}else e()}function an(t,e,n){if((t.f&B)===0){if(t.f^=B,t.transitions!==null)for(const u of t.transitions)(u.is_global||n)&&e.push(u);for(var r=t.first;r!==null;){var a=r.next,l=(r.f&Ut)!==0||(r.f&S)!==0;an(r,e,l?n:!1),r=a}}}function Xe(t){ln(t,!0)}function ln(t,e){if((t.f&B)!==0){t.f^=B,(t.f&y)===0&&(t.f^=y),V(t)&&(x(t,N),ht(t));for(var n=t.first;n!==null;){var r=n.next,a=(n.f&Ut)!==0||(n.f&S)!==0;ln(n,a?e:!1),n=r}if(t.transitions!==null)for(const l of t.transitions)(l.is_global||e)&&l.in()}}const Kn=typeof requestIdleCallback>"u"?t=>setTimeout(t,1):requestIdleCallback;let Q=[],W=[];function sn(){var t=Q;Q=[],Yt(t)}function fn(){var t=W;W=[],Yt(t)}function tr(t){Q.length===0&&queueMicrotask(sn),Q.push(t)}function nr(t){W.length===0&&Kn(fn),W.push(t)}function Vn(){Q.length>0&&sn(),W.length>0&&fn()}let et=!1,lt=!1,st=null,L=!1,X=!1;function qt(t){X=t}let Z=[];let v=null,R=!1;function U(t){v=t}let p=null;function ft(t){p=t}let w=null;function un(t){v!==null&&v.f&Et&&(w===null?w=[t]:w.push(t))}let d=null,g=0,m=null;function $n(t){m=t}let on=1,it=0,D=!1,F=null;function _n(){return++on}function V(t){var o;var e=t.f;if((e&N)!==0)return!0;if((e&P)!==0){var n=t.deps,r=(e&T)!==0;if(n!==null){var a,l,u=(e&at)!==0,i=r&&p!==null&&!D,s=n.length;if(u||i){var f=t,_=f.parent;for(a=0;a<s;a++)l=n[a],(u||!((o=l==null?void 0:l.reactions)!=null&&o.includes(f)))&&(l.reactions??(l.reactions=[])).push(f);u&&(f.f^=at),i&&_!==null&&(_.f&T)===0&&(f.f^=T)}for(a=0;a<s;a++)if(l=n[a],V(l)&&zt(l),l.wv>t.wv)return!0}(!r||p!==null&&!D)&&x(t,y)}return!1}function Zn(t,e){for(var n=e;n!==null;){if((n.f&rt)!==0)try{n.fn(t);return}catch{n.f^=rt}n=n.parent}throw et=!1,t}function jt(t){return(t.f&_t)===0&&(t.parent===null||(t.parent.f&rt)===0)}function vt(t,e,n,r){if(et){if(n===null&&(et=!1),jt(e))throw t;return}if(n!==null&&(et=!0),Zn(t,e),jt(e))throw t}function cn(t,e,n=!0){var r=t.reactions;if(r!==null)for(var a=0;a<r.length;a++){var l=r[a];w!=null&&w.includes(t)||((l.f&A)!==0?cn(l,e,!1):e===l&&(n?x(l,N):(l.f&y)!==0&&x(l,P),ht(l)))}}function vn(t){var b;var e=d,n=g,r=m,a=v,l=D,u=w,i=h,s=R,f=t.f;d=null,g=0,m=null,D=(f&T)!==0&&(R||!L||v===null),v=(f&(S|G))===0?t:null,w=null,Ft(t.ctx),R=!1,it++,t.f|=Et;try{var _=(0,t.fn)(),o=t.deps;if(d!==null){var c;if(ut(t,g),o!==null&&g>0)for(o.length=g+d.length,c=0;c<d.length;c++)o[g+c]=d[c];else t.deps=o=d;if(!D)for(c=g;c<o.length;c++)((b=o[c]).reactions??(b.reactions=[])).push(t)}else o!==null&&g<o.length&&(ut(t,g),o.length=g);if(ct()&&m!==null&&!R&&o!==null&&(t.f&(A|P|N))===0)for(c=0;c<m.length;c++)cn(m[c],t);return a!==null&&a!==t&&(it++,m!==null&&(r===null?r=m:r.push(...m))),_}finally{d=e,g=n,m=r,v=a,D=l,w=u,Ft(i),R=s,t.f^=Et}}function zn(t,e){let n=e.reactions;if(n!==null){var r=wn.call(n,t);if(r!==-1){var a=n.length-1;a===0?n=e.reactions=null:(n[r]=n[a],n.pop())}}n===null&&(e.f&A)!==0&&(d===null||!d.includes(e))&&(x(e,P),(e.f&(T|at))===0&&(e.f^=at),$t(e),ut(e,0))}function ut(t,e){var n=t.deps;if(n!==null)for(var r=e;r<n.length;r++)zn(t,n[r])}function pt(t){var e=t.f;if((e&_t)===0){x(t,y);var n=p,r=h,a=L;p=t,L=!0;try{(e&bt)!==0?Yn(t):en(t),nn(t);var l=vn(t);t.teardown=typeof l=="function"?l:null,t.wv=on;var u=t.deps,i;Ct&&hn&&t.f&N}catch(s){vt(s,t,n,r||t.ctx)}finally{L=a,p=n}}}function Jn(){try{Rn()}catch(t){if(st!==null)vt(t,st,null);else throw t}}function pn(){var t=L;try{var e=0;for(L=!0;Z.length>0;){e++>1e3&&Jn();var n=Z,r=n.length;Z=[];for(var a=0;a<r;a++){var l=Wn(n[a]);Qn(l)}z.clear()}}finally{lt=!1,L=t,st=null}}function Qn(t){var e=t.length;if(e!==0)for(var n=0;n<e;n++){var r=t[n];if((r.f&(_t|B))===0)try{V(r)&&(pt(r),r.deps===null&&r.first===null&&r.nodes_start===null&&(r.teardown===null?rn(r):r.fn=null))}catch(a){vt(a,r,null,r.ctx)}}}function ht(t){lt||(lt=!0,queueMicrotask(pn));for(var e=st=t;e.parent!==null;){e=e.parent;var n=e.f;if((n&(G|S))!==0){if((n&y)===0)return;e.f^=y}}Z.push(e)}function Wn(t){for(var e=[],n=t;n!==null;){var r=n.f,a=(r&(S|G))!==0,l=a&&(r&y)!==0;if(!l&&(r&B)===0){if((r&Bt)!==0)e.push(n);else if(a)n.f^=y;else try{V(n)&&pt(n)}catch(s){vt(s,n,null,n.ctx)}var u=n.first;if(u!==null){n=u;continue}}var i=n.parent;for(n=n.next;n===null&&i!==null;)n=i.next,i=i.parent}return e}function Xn(t){for(var e;;){if(Vn(),Z.length===0)return e;lt=!0,pn()}}async function er(){await Promise.resolve(),Xn()}function C(t){var e=t.f,n=(e&A)!==0;if(F!==null&&F.add(t),v!==null&&!R){if(!(w!=null&&w.includes(t))){var r=v.deps;t.rv<it&&(t.rv=it,d===null&&r!==null&&r[g]===t?g++:d===null?d=[t]:(!D||!d.includes(t))&&d.push(t))}}else if(n&&t.deps===null&&t.effects===null){var a=t,l=a.parent;l!==null&&(l.f&T)===0&&(a.f^=T)}return n&&(a=t,V(a)&&zt(a)),X&&z.has(t)?z.get(t):t.v}function te(t){var e=F;F=new Set;var n=F,r;try{if(dt(t),e!==null)for(r of F)e.add(r)}finally{F=e}return n}function rr(t){var e=te(()=>dt(t));for(var n of e)if((n.f&mn)!==0)for(const r of n.deps||[])(r.f&A)===0&&gt(r,r.v);else gt(n,n.v)}function dt(t){var e=R;try{return R=!0,t()}finally{R=e}}const ne=-7169;function x(t,e){t.f=t.f&ne|e}function ar(t){if(!(typeof t!="object"||!t||t instanceof EventTarget)){if(M in t)At(t);else if(!Array.isArray(t))for(let e in t){const n=t[e];typeof n=="object"&&n&&M in n&&At(n)}}}function At(t,e=new Set){if(typeof t=="object"&&t!==null&&!(t instanceof EventTarget)&&!e.has(t)){e.add(t),t instanceof Date&&t.getTime();for(let r in t)try{At(t[r],e)}catch{}const n=Ht(t);if(n!==Object.prototype&&n!==Array.prototype&&n!==Map.prototype&&n!==Set.prototype&&n!==Date.prototype){const r=yn(n);for(let a in r){const l=r[a].get;if(l)try{l.call(t)}catch{}}}}}export{Pn as $,tr as A,ct as B,Ne as C,jn as D,Ut as E,p as F,Bn as G,H,Nt as I,It as J,J as K,mt as L,He as M,Dn as N,Cn as O,je as P,Fe as Q,Xe as R,M as S,h as T,E as U,$e as V,Ve as W,Yt as X,fe as Y,Ot as Z,Tt as _,Me as a,B as a0,re as a1,ce as a2,gt as a3,ve as a4,Rt as a5,pe as a6,dn as a7,he as a8,an as a9,Ht as aA,yn as aB,nr as aC,Ie as aD,ue as aE,bt as aF,Tn as aG,me as aH,le as aI,U as aJ,ft as aK,v as aL,Xn as aM,k as aN,Se as aO,An as aP,Ye as aQ,oe as aR,Ze as aS,Ke as aa,Gn as ab,j as ac,de as ad,Mn as ae,er as af,se as ag,xn as ah,Ln as ai,Ae as aj,xe as ak,ae as al,$ as am,_e as an,mn as ao,Ee as ap,Vt as aq,Y as ar,ge as as,ie as at,ye as au,we as av,Te as aw,Pe as ax,F as ay,be as az,Hn as b,We as c,O as d,Oe as e,Ue as f,Re as g,q as h,Be as i,I as j,C as k,Ce as l,De as m,ke as n,qe as o,Un as p,rr as q,Le as r,Ge as s,Qe as t,ze as u,Je as v,ar as w,tn as x,St as y,dt as z};
