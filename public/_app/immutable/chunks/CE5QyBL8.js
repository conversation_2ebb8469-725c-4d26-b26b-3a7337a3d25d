import{d as c,w as s}from"./DTQDE1SG.js";import{g as f,a as g,d as J,u as w,b as m,c as h,e as b,f as j,h as L,i as C}from"./D9cKHHet.js";async function v(){if((await b()).length===0){const e=[{name:"Plumbing",description:"General plumbing services",defaultDuration:120,defaultFields:[],isActive:!0},{name:"Electrical",description:"Electrical installation and repair",defaultDuration:180,defaultFields:[],isActive:!0},{name:"HVAC",description:"Heating, ventilation, and air conditioning",defaultDuration:240,defaultFields:[],isActive:!0},{name:"General Maintenance",description:"General maintenance and repair work",defaultDuration:90,defaultFields:[],isActive:!0},{name:"Inspection",description:"Property inspection services",defaultDuration:60,defaultFields:[],isActive:!0}];for(const r of e)await C(r)}}const a=s([]),y=s([]),E=s([]),l=s(!1),u=s(!1),d=s(!1),n=s(null),p=s(!1),T=s("create"),M=c([a,y],([t,e])=>{const r={};return e.forEach(o=>{r[o.id]=t.filter(i=>i.status.id===o.id)}),r});c(a,t=>t.filter(e=>e.status.isCompleted));c(a,t=>t.filter(e=>!e.status.isCompleted));c(a,t=>t.filter(e=>e.priority==="Urgent"||e.priority==="High"));const B={async loadJobs(){l.set(!0);try{const t=await L();a.set(t)}catch(t){console.error("Error loading jobs:",t)}finally{l.set(!1)}},async loadJobStatuses(){u.set(!0);try{const t=await j();y.set(t)}catch(t){console.error("Error loading job statuses:",t)}finally{u.set(!1)}},async loadJobTypes(){d.set(!0);try{await v();const t=await b();E.set(t)}catch(t){console.error("Error loading job types:",t)}finally{d.set(!1)}},async addJob(t){try{const e=await h(t);return a.update(r=>[...r,e]),e}catch(e){throw console.error("Error adding job:",e),e}},async updateJob(t,e){try{const r=await m(t,e);return a.update(o=>o.map(i=>i.id===t?r:i)),n.update(o=>(o==null?void 0:o.id)===t?r:o),r}catch(r){throw console.error("Error updating job:",r),r}},async updateJobStatus(t,e){try{const r=await w(t,e);return a.update(o=>o.map(i=>i.id===t?r:i)),n.update(o=>(o==null?void 0:o.id)===t?r:o),r}catch(r){throw console.error("Error updating job status:",r),r}},async deleteJob(t){try{await J(t),a.update(e=>e.filter(r=>r.id!==t)),n.update(e=>(e==null?void 0:e.id)===t?null:e)}catch(e){throw console.error("Error deleting job:",e),e}},selectJob(t){n.set(t)},openJobModal(t="create",e){T.set(t),t==="edit"&&e?n.set(e):n.set(null),p.set(!0)},closeJobModal(){p.set(!1),n.set(null)},async getJobsByCustomer(t){try{return await g(t)}catch(e){throw console.error("Error getting jobs by customer:",e),e}},async getUninvoicedJobs(t){try{return await f(t)}catch(e){throw console.error("Error getting uninvoiced jobs:",e),e}},searchJobs(t){return c(a,e=>e.filter(r=>{var o;return r.title.toLowerCase().includes(t.toLowerCase())||r.description.toLowerCase().includes(t.toLowerCase())||((o=r.customerName)==null?void 0:o.toLowerCase().includes(t.toLowerCase()))||r.jobType.toLowerCase().includes(t.toLowerCase())}))},filterJobsByStatus(t){return c(a,e=>e.filter(r=>r.status.id===t))},filterJobsByPriority(t){return c(a,e=>e.filter(r=>r.priority===t))}};export{a,p as b,T as c,E as d,M as e,l as f,u as g,y as h,B as j,n as s};
