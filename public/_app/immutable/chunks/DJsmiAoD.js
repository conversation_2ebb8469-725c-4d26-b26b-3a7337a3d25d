const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./C5jwvbV4.js","./CGKBDcrf.js","./DTQDE1SG.js","./p3DoyA09.js","./DIeogL5L.js","./CSyJhG7e.js","./C_WNR8j8.js"])))=>i.map(i=>d[i]);
import{_ as m}from"./Dp1pzeXC.js";const u="ejp_invoices",v="ejp_products",y="ejp_invoice_templates",S=[{id:"1",name:"Draft",color:"#6B7280"},{id:"2",name:"Sen<PERSON>",color:"#3B82F6"},{id:"3",name:"Paid",color:"#10B981"},{id:"4",name:"Overdue",color:"#EF4444"},{id:"5",name:"Cancelled",color:"#6B7280"}],I={Draft:0,Sent:1,Paid:2,Overdue:3,Cancelled:4},f={0:{id:"1",name:"Draft",color:"#6B7280"},1:{id:"2",name:"Sent",color:"#3B82F6"},2:{id:"3",name:"Paid",color:"#10B981"},3:{id:"4",name:"Overdue",color:"#EF4444"},4:{id:"5",name:"Cancelled",color:"#6B7280"}};function D(e){return f[e]||S[0]}function E(e){return I[e]??0}function l(){const e=localStorage.getItem(u);if(e)return JSON.parse(e);w();const i=localStorage.getItem(u);return i?JSON.parse(i):[]}function g(e){localStorage.setItem(u,JSON.stringify(e))}function p(){const e=localStorage.getItem(v);return e?JSON.parse(e):[]}function T(e){localStorage.setItem(v,JSON.stringify(e))}function r(){const e=localStorage.getItem(y);return e?JSON.parse(e):[]}function d(e){localStorage.setItem(y,JSON.stringify(e))}function a(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}async function _(){try{const{api:e}=await m(async()=>{const{api:o}=await import("./C5jwvbV4.js");return{api:o}},__vite__mapDeps([0,1,2,3,4,5,6]),import.meta.url);return(await e.get("/Invoices")).map(o=>({...o,invoiceLines:o.invoiceLines||[]}))}catch(e){return console.error("Error fetching invoices:",e),new Promise(i=>{setTimeout(()=>{const t=l().map(n=>({...n,invoiceLines:n.invoiceLines||[]}));i(t)},100)})}}async function x(e){try{const{api:i}=await m(async()=>{const{api:t}=await import("./C5jwvbV4.js");return{api:t}},__vite__mapDeps([0,1,2,3,4,5,6]),import.meta.url),o=await i.get(`/Invoices/${e}`);return{...o,invoiceLines:o.invoiceLines||[]}}catch(i){return console.error("Error fetching invoice:",i),new Promise(o=>{setTimeout(()=>{const n=l().find(s=>s.id===e)||null;n&&(n.invoiceLines=n.invoiceLines||[]),o(n)},100)})}}async function O(e){try{const{api:i}=await m(async()=>{const{api:t}=await import("./C5jwvbV4.js");return{api:t}},__vite__mapDeps([0,1,2,3,4,5,6]),import.meta.url);return(await i.get(`/Invoices?customerId=${e}`)).map(t=>({...t,invoiceLines:t.invoiceLines||[]}))}catch(i){return console.error("Error fetching customer invoices:",i),new Promise(o=>{setTimeout(()=>{const n=l().filter(s=>!1).map(s=>({...s,invoiceLines:s.invoiceLines||[]}));o(n)},100)})}}async function L(e){try{const{api:i}=await m(async()=>{const{api:c}=await import("./C5jwvbV4.js");return{api:c}},__vite__mapDeps([0,1,2,3,4,5,6]),import.meta.url),o={...e,invoiceLines:e.invoiceLines||[]},t=await i.post("/Invoices",o),n=l(),s=n.findIndex(c=>c.id===t.id);return s>=0?n[s]=t:n.push(t),g(n),{...t,invoiceLines:t.invoiceLines||[]}}catch(i){return console.error("Error saving invoice:",i),new Promise(o=>{setTimeout(()=>{const t=l();e.id||(e.id=a(),e.invoiceNumber=t.length+1),e.invoiceLines=e.invoiceLines||[];const n=t.findIndex(s=>s.id===e.id);n>=0?t[n]=e:t.push(e),g(t),o(e)},100)})}}function h(e=1){return{lineNumber:e,description:"",quantity:1,unitPrice:0,discountType:0,discountValue:0,discountAmount:0,subtotal:0,taxRate:.1,taxAmount:0,total:0}}function N(e){const i=e.reduce((n,s)=>n+s.subtotal,0),o=e.reduce((n,s)=>n+s.taxAmount,0),t=e.reduce((n,s)=>n+s.total,0);return{subtotal:i,taxAmount:o,totalAmount:t}}function V(e){const i=e.quantity*e.unitPrice,o=e.discountType===1?i*(e.discountValue/100):e.discountValue,t=i-o,n=t*e.taxRate,s=t+n;return{...e,subtotal:i,discountAmount:o,taxAmount:n,total:s}}function w(){if(!localStorage.getItem(u)){const i=[{id:a(),invoiceNumber:1001,status:1,issueDate:new Date(Date.now()-6048e5).toISOString().split("T")[0],dueDate:new Date(Date.now()+19872e5).toISOString().split("T")[0],notes:"Thank you for your business!",paymentTerms:"Payment due within 30 days",invoiceLines:[{lineNumber:1,description:"Plumbing Service - Basic",quantity:2,unitPrice:150,discountType:0,discountValue:0,discountAmount:0,subtotal:300,taxRate:.1,taxAmount:30,total:330},{lineNumber:2,description:"Emergency Call-out",quantity:1,unitPrice:100,discountType:0,discountValue:0,discountAmount:0,subtotal:100,taxRate:.1,taxAmount:10,total:110}]},{id:a(),invoiceNumber:1002,status:2,issueDate:new Date(Date.now()-12096e5).toISOString().split("T")[0],dueDate:new Date(Date.now()-864e5).toISOString().split("T")[0],notes:"Electrical work completed successfully.",paymentTerms:"Payment due within 30 days",invoiceLines:[{lineNumber:1,description:"Electrical Installation",quantity:1,unitPrice:200,discountType:0,discountValue:0,discountAmount:0,subtotal:200,taxRate:.1,taxAmount:20,total:220}]},{id:a(),invoiceNumber:1003,status:3,issueDate:new Date(Date.now()-3888e6).toISOString().split("T")[0],dueDate:new Date(Date.now()-1296e6).toISOString().split("T")[0],notes:"HVAC maintenance service.",paymentTerms:"Payment due within 30 days",invoiceLines:[{lineNumber:1,description:"HVAC Maintenance",quantity:1,unitPrice:180,discountType:0,discountValue:0,discountAmount:0,subtotal:180,taxRate:.1,taxAmount:18,total:198}]},{id:a(),status:0,issueDate:new Date().toISOString().split("T")[0],dueDate:new Date(Date.now()+2592e6).toISOString().split("T")[0],notes:"Draft invoice for general labor.",paymentTerms:"Payment due within 30 days",invoiceLines:[{lineNumber:1,description:"General Labor",quantity:4,unitPrice:75,discountType:0,discountValue:0,discountAmount:0,subtotal:300,taxRate:.1,taxAmount:30,total:330}]}];localStorage.setItem(u,JSON.stringify(i))}}async function P(){if(p().length===0){const i=[{name:"Plumbing Service - Basic",description:"Basic plumbing service including inspection and minor repairs",price:150,taxRate:10,category:"Plumbing",isActive:!0},{name:"Electrical Installation",description:"Standard electrical installation and wiring",price:200,taxRate:10,category:"Electrical",isActive:!0},{name:"HVAC Maintenance",description:"Heating, ventilation, and air conditioning maintenance service",price:180,taxRate:10,category:"HVAC",isActive:!0},{name:"General Labor",description:"General labor and maintenance work",price:75,taxRate:10,category:"General",isActive:!0},{name:"Emergency Call-out",description:"Emergency service call-out fee",price:100,taxRate:10,category:"Emergency",isActive:!0}],o=p();for(const t of i){const n={...t,id:a(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};o.push(n)}T(o)}}async function A(){if(r().length===0){const i=[{name:"Default Template",colorScheme:{primary:"#3B82F6",secondary:"#6B7280",accent:"#10B981"},sections:[{id:a(),type:"header",content:"Easy Job Planner Invoice",order:1,isVisible:!0},{id:a(),type:"terms",content:"Payment due within 30 days. Please make payment via bank transfer.",order:2,isVisible:!0},{id:a(),type:"footer",content:"Thank you for your business!",order:3,isVisible:!0}],isDefault:!0},{name:"Professional Template",colorScheme:{primary:"#1F2937",secondary:"#374151",accent:"#059669"},sections:[{id:a(),type:"header",content:"Professional Services Invoice",order:1,isVisible:!0},{id:a(),type:"terms",content:"Payment terms: Net 30 days. Late payments subject to 1.5% monthly service charge.",order:2,isVisible:!0}],isDefault:!1}],o=r();for(const t of i){const n={...t,id:a(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};o.push(n)}d(o)}}async function R(){return new Promise(async e=>{setTimeout(async()=>{await P(),e(p())},100)})}async function B(){return new Promise(async e=>{setTimeout(async()=>{await A(),e(r())},100)})}async function C(e){return new Promise(i=>{setTimeout(()=>{const o=r(),t={...e,id:a(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};o.push(t),d(o),i(t)},100)})}async function F(e,i){return new Promise((o,t)=>{setTimeout(()=>{const n=r(),s=n.findIndex(c=>c.id===e);if(s===-1){t(new Error("Template not found"));return}n[s]={...n[s],...i,updatedAt:new Date().toISOString()},d(n),o(n[s])},100)})}async function G(e){return new Promise((i,o)=>{setTimeout(()=>{const t=r(),n=t.findIndex(s=>s.id===e);if(n===-1){o(new Error("Template not found"));return}t.splice(n,1),d(t),i()},100)})}async function J(){return new Promise(e=>{setTimeout(()=>{e(S)},100)})}export{D as a,N as b,h as c,R as d,B as e,J as f,O as g,V as h,E as i,x as j,_ as k,C as l,G as m,L as s,F as u};
