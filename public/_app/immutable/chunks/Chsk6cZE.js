const c={GBP:{code:"GBP",symbol:"£",name:"British Pound Sterling",locale:"en-GB"},USD:{code:"USD",symbol:"$",name:"US Dollar",locale:"en-US"},EUR:{code:"EUR",symbol:"€",name:"Euro",locale:"en-EU"}},r=c.GBP;function t(){const e=localStorage.getItem("ejp_currency_config");if(e)try{const n=JSON.parse(e);return c[n.code]||r}catch{return r}return r}function l(e,n){const o=t();return new Intl.NumberFormat(o.locale,{style:"currency",currency:o.code}).format(e)}export{l as f};
