const S="ejp_customers";function i(){const t=localStorage.getItem(S);return t?JSON.parse(t):[]}function d(t){localStorage.setItem(S,JSON.stringify(t))}function f(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}async function g(){return new Promise(t=>{setTimeout(()=>{t(i())},100)})}async function m(t){return new Promise(s=>{setTimeout(()=>{const r=i(),o={...t,id:f(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};r.push(o),d(r),s(o)},100)})}async function w(t,s){return new Promise((r,o)=>{setTimeout(()=>{const e=i(),n=e.findIndex(a=>a.id===t);if(n===-1){o(new Error("Customer not found"));return}e[n]={...e[n],...s,updatedAt:new Date().toISOString()},d(e),r(e[n])},100)})}async function C(t){return new Promise((s,r)=>{setTimeout(()=>{const o=i(),e=o.findIndex(n=>n.id===t);if(e===-1){r(new Error("Customer not found"));return}o.splice(e,1),d(o),s()},100)})}async function p(t,s){return new Promise((r,o)=>{setTimeout(()=>{const e=getContactsFromStorage(),n=e.find(c=>c.id===t);if(!n){o(new Error("Contact not found"));return}const a={id:f(),content:s,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};n.notes.push(a),n.updatedAt=new Date().toISOString(),saveContactsToStorage(e),r(a)},100)})}async function I(t,s,r){return new Promise((o,e)=>{setTimeout(()=>{const n=getContactsFromStorage(),a=n.find(u=>u.id===t);if(!a){e(new Error("Contact not found"));return}const c=a.notes.find(u=>u.id===s);if(!c){e(new Error("Note not found"));return}c.content=r,c.updatedAt=new Date().toISOString(),a.updatedAt=new Date().toISOString(),saveContactsToStorage(n),o(c)},100)})}async function O(t,s){return new Promise((r,o)=>{setTimeout(()=>{const e=getContactsFromStorage(),n=e.find(c=>c.id===t);if(!n){o(new Error("Contact not found"));return}const a=n.notes.findIndex(c=>c.id===s);if(a===-1){o(new Error("Note not found"));return}n.notes.splice(a,1),n.updatedAt=new Date().toISOString(),saveContactsToStorage(e),r()},100)})}export{p as a,I as b,m as c,C as d,O as e,g,w as u};
