import"./CWj6FrbW.js";import"./69_IOA4Y.js";import{i as r,r as l,s as _,t as g,n as h}from"./p3DoyA09.js";import{s as o}from"./BUelSUke.js";import{t as x,a as w}from"./B67foYpL.js";import{s as C}from"./D7jLSc-x.js";import{p as i}from"./qYb16FSw.js";var b=x('<div class="stat-card svelte-1ta1grw"><h3 class="svelte-1ta1grw"> </h3> <p> </p></div>');function B(m,t){let p=i(t,"title",8),d=i(t,"value",8),u=i(t,"valueClass",8,void 0);var e=b(),a=r(e),f=r(a,!0);l(a);var s=_(a,2);let v;var n=r(s,!0);l(s),l(e),g(c=>{o(f,p()),v=C(s,1,"stat-number svelte-1ta1grw",null,v,c),o(n,d())},[()=>({overdue:u()==="overdue"})],h),w(m,e)}export{B as S};
