import{x as v,C as s,z as _}from"./p3DoyA09.js";import{l as d}from"./BUelSUke.js";function o(e,r,n){if(e.multiple)return b(e,r);for(var t of e.options){var u=i(t);if(s(u,r)){t.selected=!0;return}}(!n||r!==void 0)&&(e.selectedIndex=-1)}function c(e,r){let n=!0;v(()=>{r&&o(e,_(r),n),n=!1;var t=new MutationObserver(()=>{var u=e.__value;o(e,u)});return t.observe(e,{childList:!0,subtree:!0,attributes:!0,attributeFilter:["value"]}),()=>{t.disconnect()}})}function q(e,r,n=r){var t=!0;d(e,"change",u=>{var a=u?"[selected]":":checked",f;if(e.multiple)f=[].map.call(e.querySelectorAll(a),i);else{var l=e.querySelector(a)??e.querySelector("option:not([disabled])");f=l&&i(l)}n(f)}),v(()=>{var u=r();if(o(e,u,t),t&&u===void 0){var a=e.querySelector(":checked");a!==null&&(u=i(a),n(u))}e.__value=u,t=!1}),c(e)}function b(e,r){for(var n of e.options)n.selected=~r.indexOf(i(n))}function i(e){return"__value"in e?e.__value:e.value}export{q as b,c as i,o as s};
