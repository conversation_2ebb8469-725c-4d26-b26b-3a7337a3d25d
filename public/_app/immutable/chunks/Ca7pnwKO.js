const i="ejp_staff";function s(){const e=localStorage.getItem(i);return e?JSON.parse(e):[]}function o(e){localStorage.setItem(i,JSON.stringify(e))}function m(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}async function u(){return new Promise(e=>{setTimeout(()=>{const a=s();a.forEach(n=>{n.fullName=`${n.firstName} ${n.lastName}`}),e(a)},100)})}async function S(e){return new Promise(a=>{setTimeout(()=>{const t=s().find(f=>f.id===e)||null;t&&(t.fullName=`${t.firstName} ${t.lastName}`),a(t)},100)})}async function l(){return new Promise(e=>{setTimeout(()=>{const n=s().filter(t=>t.isActive);n.forEach(t=>{t.fullName=`${t.firstName} ${t.lastName}`}),e(n)},100)})}async function d(e){return new Promise(a=>{setTimeout(()=>{const n=s(),t={...e,id:m(),fullName:`${e.firstName} ${e.lastName}`,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};n.push(t),o(n),a(t)},100)})}async function N(e,a){return new Promise((n,t)=>{setTimeout(()=>{const f=s(),r=f.findIndex(c=>c.id===e);if(r===-1){t(new Error("Staff member not found"));return}f[r]={...f[r],...a,updatedAt:new Date().toISOString()},(a.firstName||a.lastName)&&(f[r].fullName=`${f[r].firstName} ${f[r].lastName}`),o(f),n(f[r])},100)})}async function g(e){return new Promise((a,n)=>{setTimeout(()=>{const t=s(),f=t.findIndex(r=>r.id===e);if(f===-1){n(new Error("Staff member not found"));return}t.splice(f,1),o(t),a()},100)})}export{u as a,S as b,d as c,g as d,l as g,N as u};
