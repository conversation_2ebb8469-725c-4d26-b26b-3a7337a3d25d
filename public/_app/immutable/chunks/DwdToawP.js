import{b as p,h as u,a as h,E as g,M as S,N as D,O,P as k,K as F,Q as I,R as b,c as _,p as v,U as H,d as L}from"./p3DoyA09.js";function U(A,E,[t,s]=[0,0]){u&&t===0&&h();var a=A,f=null,e=null,i=H,N=t>0?g:0,n=!1;const R=(c,l=!0)=>{n=!0,o(l,c)},o=(c,l)=>{if(i===(i=c))return;let T=!1;if(u&&s!==-1){if(t===0){const r=S(a);r===D?s=0:r===O?s=1/0:(s=parseInt(r.substring(1)),s!==s&&(s=i?1/0:-1))}const m=s>t;!!i===m&&(a=k(),F(a),I(!1),T=!0,s=-1)}i?(f?b(f):l&&(f=_(()=>l(a))),e&&v(e,()=>{e=null})):(e?b(e):l&&(e=_(()=>l(a,[t+1,s]))),f&&v(f,()=>{f=null})),T&&I(!0)};p(()=>{n=!1,E(R),n||o(null,null)},N),u&&(a=L)}export{U as i};
