import"./CWj6FrbW.js";import"./69_IOA4Y.js";import{e as N,f as A,g as E,i as r,s as f,r as s,o as x,t as q}from"./p3DoyA09.js";import{s as z,e as _}from"./BUelSUke.js";import{i as F}from"./DwdToawP.js";import{e as G,a as o,t as H,b}from"./B67foYpL.js";import{s as I}from"./Bfc47y5P.js";import{i as J}from"./D3pqaimu.js";import{b as K}from"./BWn8tY11.js";import{p as w}from"./qYb16FSw.js";import{c as L}from"./C_WNR8j8.js";import{B as $}from"./6Zk3JFqZ.js";var M=H('<div class="confirm-delete-backdrop svelte-wjxrms" role="presentation"><div class="confirm-delete-dialog svelte-wjxrms" role="dialog" aria-modal="true"><h3 class="svelte-wjxrms">Confirm Deletion</h3> <p class="svelte-wjxrms"> </p> <div class="actions svelte-wjxrms"><!> <!></div></div></div>');function te(g,t){N(t,!1);let i=w(t,"showConfirm",12,!1),k=w(t,"itemName",8,"this item");const v=L();function C(){v("confirm"),i(!1)}function p(){v("cancel"),i(!1)}J();var d=G(),j=A(d);{var y=l=>{var a=M(),n=r(a),m=f(r(n),2),D=r(m);s(m);var u=f(m,2),h=r(u);$(h,{variant:"secondary",type:"button",$$events:{click:p},children:(e,B)=>{x();var c=b("Cancel");o(e,c)},$$slots:{default:!0}});var P=f(h,2);$(P,{variant:"danger",type:"button",$$events:{click:C},children:(e,B)=>{x();var c=b("Delete");o(e,c)},$$slots:{default:!0}}),s(u),s(n),s(a),q(()=>z(D,`Are you sure you want to delete ${k()??""}?`)),_("click",n,I(function(e){K.call(this,t,e)})),_("click",a,p),o(l,a)};F(j,l=>{i()&&l(y)})}o(g,d),E()}export{te as C};
