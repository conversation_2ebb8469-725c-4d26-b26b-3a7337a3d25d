const p="ejp_quotes",S="ejp_quote_templates",m=[{id:"1",name:"Draft",color:"#6B7280"},{id:"2",name:"<PERSON><PERSON>",color:"#3B82F6"},{id:"3",name:"Accepted",color:"#10B981"},{id:"4",name:"Rejected",color:"#EF4444"},{id:"5",name:"Expired",color:"#F59E0B"},{id:"6",name:"Converted",color:"#8B5CF6"}];function a(){const e=localStorage.getItem(p);return e?JSON.parse(e):[]}function c(e){localStorage.setItem(p,JSON.stringify(e))}function d(){const e=localStorage.getItem(S);return e?JSON.parse(e):[]}function l(e){localStorage.setItem(S,JSON.stringify(e))}function u(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}function f(){const e=a(),i=new Date().getFullYear(),n=e.filter(t=>t.quoteNumber.startsWith(`QUO-${i}`)).length+1;return`QUO-${i}-${n.toString().padStart(4,"0")}`}async function g(){if(a().length===0){const i=[{customerId:"customer-1",customerName:"John Smith",customerEmail:"<EMAIL>",customerAddress:{street:"123 Main St",city:"Springfield",state:"IL",zipCode:"62701",country:"USA"},issueDate:new Date().toISOString().split("T")[0],expiryDate:new Date(Date.now()+2592e6).toISOString().split("T")[0],status:m[1],sections:[{id:u(),type:"cover",title:"Project Overview",content:"Complete plumbing renovation for residential property.",order:1,isVisible:!0},{id:u(),type:"recommendations",title:"Recommendations",content:"We recommend upgrading all fixtures and installing new copper piping throughout.",order:2,isVisible:!0}],lineItems:[{id:u(),description:"Plumbing Service - Complete Renovation",quantity:1,unitPrice:2500,taxRate:10,taxAmount:250,lineTotal:2750}],subtotal:2500,taxAmount:250,discountAmount:0,totalAmount:2750,notes:"Quote valid for 30 days",terms:"Payment due within 30 days of acceptance"},{customerId:"customer-2",customerName:"Sarah Johnson",customerEmail:"<EMAIL>",customerAddress:{street:"456 Oak Ave",city:"Springfield",state:"IL",zipCode:"62702",country:"USA"},issueDate:new Date(Date.now()-6048e5).toISOString().split("T")[0],expiryDate:new Date(Date.now()+19872e5).toISOString().split("T")[0],status:m[2],sections:[{id:u(),type:"cover",title:"Electrical Installation",content:"New electrical panel and wiring installation.",order:1,isVisible:!0}],lineItems:[{id:u(),description:"Electrical Panel Upgrade",quantity:1,unitPrice:1800,taxRate:10,taxAmount:180,lineTotal:1980}],subtotal:1800,taxAmount:180,discountAmount:0,totalAmount:1980,notes:"Work to be completed within 2 weeks",terms:"Payment due within 30 days of acceptance",acceptedAt:new Date().toISOString()}],n=a();for(const t of i){const o={...t,id:u(),quoteNumber:f(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};n.push(o)}c(n)}}async function y(){return new Promise(async e=>{setTimeout(async()=>{await g(),e(a())},100)})}async function w(e){return new Promise(i=>{setTimeout(()=>{const t=a().find(o=>o.id===e)||null;i(t)},100)})}async function T(e){return new Promise(i=>{setTimeout(()=>{const n=a(),t={...e,id:u(),quoteNumber:f(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};n.push(t),c(n),i(t)},100)})}async function I(e,i){return new Promise((n,t)=>{setTimeout(()=>{const o=a(),s=o.findIndex(r=>r.id===e);if(s===-1){t(new Error("Quote not found"));return}o[s]={...o[s],...i,updatedAt:new Date().toISOString()},c(o),n(o[s])},100)})}async function A(e){return new Promise((i,n)=>{setTimeout(()=>{const t=a(),o=t.findIndex(s=>s.id===e);if(o===-1){n(new Error("Quote not found"));return}t.splice(o,1),c(t),i()},100)})}async function Q(){return new Promise(e=>{setTimeout(()=>{e(d())},100)})}async function D(e){return new Promise(i=>{setTimeout(()=>{const n=d(),t={...e,id:u(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};n.push(t),l(n),i(t)},100)})}async function v(e,i){return new Promise((n,t)=>{setTimeout(()=>{const o=d(),s=o.findIndex(r=>r.id===e);if(s===-1){t(new Error("Template not found"));return}o[s]={...o[s],...i,updatedAt:new Date().toISOString()},l(o),n(o[s])},100)})}async function b(e){return new Promise((i,n)=>{setTimeout(()=>{const t=d(),o=t.findIndex(s=>s.id===e);if(o===-1){n(new Error("Template not found"));return}t.splice(o,1),l(t),i()},100)})}async function O(e){return new Promise(i=>{setTimeout(()=>{const n={coverSection:`Dear ${e.customerInfo.name},

Thank you for considering our services for your ${e.jobDetails.jobType} project at ${e.customerInfo.address}. We have carefully reviewed your requirements and prepared this comprehensive quote.`,observationalData:`Property Assessment:
- Property Type: ${e.customerInfo.propertyType}
- Job Type: ${e.jobDetails.jobType}
- Observations: ${e.observations.join(", ")}`,recommendations:`Based on our assessment, we recommend:
${e.requirements.map(t=>`- ${t}`).join(`
`)}`,lineItems:[{description:`${e.jobDetails.jobType} - Standard Service`,quantity:1,unitPrice:150,notes:"Includes basic materials and labor"},{description:"Additional Materials",quantity:1,unitPrice:75,notes:"Premium grade materials as specified"}],totalEstimate:225,timeframe:e.jobDetails.estimatedDuration||"2-3 business days",terms:"Payment due within 30 days of completion. 50% deposit required to commence work."};i(n)},2e3)})}async function P(e){return new Promise((i,n)=>{setTimeout(()=>{const t=a(),o=t.find(r=>r.id===e);if(!o){n(new Error("Quote not found"));return}o.status={id:"6",name:"Converted",color:"#8B5CF6"},o.convertedToInvoiceAt=new Date().toISOString(),o.updatedAt=new Date().toISOString();const s=u();o.relatedInvoiceId=s,c(t),i(s)},100)})}async function h(){return new Promise(e=>{setTimeout(()=>{e(m)},100)})}function E(e,i=0){const n=e.reduce((s,r)=>s+r.quantity*r.unitPrice,0),t=e.reduce((s,r)=>s+r.taxAmount,0),o=n+t-i;return{subtotal:n,taxAmount:t,totalAmount:o}}async function x(e){return new Promise(i=>{setTimeout(()=>{const t=a().filter(o=>o.customerId===e&&o.status.name==="Accepted"&&!o.convertedToInvoiceAt);i(t)},100)})}export{y as a,h as b,Q as c,D as d,b as e,O as f,x as g,E as h,T as i,w as j,A as k,P as l,I as m,v as u};
