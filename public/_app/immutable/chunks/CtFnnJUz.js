import"./CWj6FrbW.js";import"./69_IOA4Y.js";import{t as p}from"./p3DoyA09.js";import{n as r,a as d}from"./B67foYpL.js";import{s as t}from"./DdRd56Yq.js";import{s}from"./D7jLSc-x.js";import{p as o}from"./qYb16FSw.js";var f=r('<svg viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M1.37354 5.81723C2.25895 4.76457 3.77696 4.25 6 4.25H16C18.223 4.25 19.7411 4.76457 20.6265 5.81723C21.5033 6.85966 21.5867 8.22916 21.4462 9.50612L20.6958 17.5099C20.5856 18.538 20.3275 19.6651 19.4096 20.5037C18.4985 21.3361 17.0869 21.75 15 21.75H7C4.91315 21.75 3.50153 21.3361 2.59039 20.5037C1.67245 19.6651 1.41442 18.538 1.30427 17.5099L1.30321 17.5L0.553851 9.50614C0.413322 8.22917 0.49672 6.85967 1.37354 5.81723ZM2.52146 6.78277C2.04973 7.3436 1.91695 8.18629 2.04545 9.34751L2.04683 9.35998L2.79625 17.3549C2.8963 18.2843 3.10342 18.9407 3.60212 19.3963C4.10847 19.8589 5.06685 20.25 7 20.25H15C16.9332 20.25 17.8915 19.8589 18.3979 19.3963C18.8966 18.9407 19.1037 18.2843 19.2037 17.3549L19.9544 9.3475C20.0829 8.18628 19.9503 7.3436 19.4785 6.78277C19.014 6.23043 18.047 5.75 16 5.75H6C3.95304 5.75 2.98605 6.23043 2.52146 6.78277Z" fill="#292D32"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M7.79369 3.05038C7.75121 3.37577 7.75 3.74554 7.75 4.2V5C7.75 5.41421 7.41422 5.75 7 5.75C6.58579 5.75 6.25 5.41421 6.25 5L6.25 4.17401C6.24998 3.75048 6.24995 3.28789 6.30631 2.85619C6.36471 2.40889 6.48985 1.93401 6.78302 1.50983C7.39976 0.617491 8.52951 0.25 10.2 0.25H11.8C13.4705 0.25 14.6002 0.617491 15.217 1.50983C15.5102 1.93401 15.6353 2.40889 15.6937 2.85619C15.7501 3.28789 15.75 3.75049 15.75 4.17402L15.75 5C15.75 5.41421 15.4142 5.75 15 5.75C14.5858 5.75 14.25 5.41421 14.25 5V4.2C14.25 3.74554 14.2488 3.37577 14.2063 3.05038C14.1647 2.73174 14.0899 2.51724 13.983 2.36267C13.7998 2.09751 13.3295 1.75 11.8 1.75H10.2C8.67049 1.75 8.20025 2.09751 8.01698 2.36267C7.91015 2.51724 7.83529 2.73174 7.79369 3.05038Z" fill="#292D32"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M9.75081 11.7508C9.75003 11.8222 9.75001 11.9036 9.75001 12V13.03C9.75001 13.3115 9.75247 13.5186 9.77691 13.6943C9.80042 13.8632 9.83776 13.9454 9.8694 13.9901C9.90469 14.0399 10.0806 14.25 11 14.25C11.9236 14.25 12.0974 14.0378 12.1319 13.9885C12.1638 13.9431 12.201 13.8596 12.2241 13.6887C12.2482 13.511 12.25 13.3029 12.25 13.02V12C12.25 11.9036 12.25 11.8222 12.2492 11.7508C12.1778 11.75 12.0965 11.75 12 11.75H10C9.90356 11.75 9.82219 11.75 9.75081 11.7508ZM9.96716 10.25C9.97812 10.25 9.98907 10.25 10 10.25H12C12.0109 10.25 12.0219 10.25 12.0329 10.25C12.254 10.25 12.4781 10.2499 12.661 10.2702C12.8474 10.2909 13.157 10.3463 13.4053 10.5947C13.6537 10.843 13.7091 11.1527 13.7298 11.3391C13.7501 11.5219 13.7501 11.746 13.75 11.9672C13.75 11.9781 13.75 11.9891 13.75 12V13.0312C13.75 13.291 13.75 13.5983 13.7106 13.8898C13.6696 14.1923 13.58 14.5357 13.3606 14.849C12.8926 15.5172 12.0664 15.75 11 15.75C9.93944 15.75 9.11533 15.5202 8.64562 14.8575C8.42476 14.5458 8.33335 14.2037 8.29123 13.901C8.25005 13.6052 8.25001 13.2935 8.25001 13.03V12C8.25001 11.9891 8.25001 11.9781 8.25 11.9672C8.24995 11.746 8.2499 11.5219 8.27022 11.3391C8.29093 11.1527 8.34631 10.843 8.59468 10.5947C8.84305 10.3463 9.15267 10.2909 9.33906 10.2702C9.52192 10.2499 9.74602 10.25 9.96716 10.25Z" fill="#292D32"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M21.2565 9.55886C21.5002 9.89385 21.4261 10.3629 21.0911 10.6065C18.675 12.3637 15.9145 13.4089 13.0937 13.7641C12.6827 13.8159 12.3076 13.5247 12.2559 13.1137C12.2041 12.7027 12.4953 12.3276 12.9063 12.2759C15.4855 11.9511 18.005 10.9962 20.2089 9.39344C20.5438 9.14981 21.0129 9.22387 21.2565 9.55886Z" fill="#292D32"></path><path fill-rule="evenodd" clip-rule="evenodd" d="M1.00109 9.84639C1.23505 9.50457 1.7018 9.41713 2.04362 9.65109C4.19251 11.1219 6.61575 12.0083 9.08346 12.2847C9.4951 12.3307 9.79144 12.7018 9.74535 13.1135C9.69926 13.5251 9.32819 13.8214 8.91655 13.7753C6.20426 13.4716 3.5475 12.4981 1.19639 10.8889C0.854578 10.655 0.767138 10.1882 1.00109 9.84639Z" fill="#292D32"></path></svg>');function L(i,e){let l=o(e,"size",8,"24"),a=o(e,"className",8,"");var C=f();p(()=>{t(C,"width",l()),t(C,"height",l()),s(C,0,a())}),d(i,C)}export{L as I};
