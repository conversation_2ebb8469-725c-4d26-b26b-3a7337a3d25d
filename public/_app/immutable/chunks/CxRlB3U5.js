import"./CWj6FrbW.js";import"./69_IOA4Y.js";import{e as Ue,u as ne,v as qe,t as N,n as W,k as a,m as p,i as f,s as x,g as Fe,j as s,w as Oe,r as l,f as Te}from"./p3DoyA09.js";import{e as S,s as P}from"./BUelSUke.js";import{i as y}from"./DwdToawP.js";import{e as We,i as je}from"./DEqeA9IH.js";import{t as v,a as d}from"./B67foYpL.js";import{s as Be,r as fe}from"./DdRd56Yq.js";import{s as V}from"./D7jLSc-x.js";import{b as de}from"./WI3NPOEW.js";import{b as Ke}from"./chpzhzGT.js";import{p as Ge}from"./Bfc47y5P.js";import{i as He}from"./D3pqaimu.js";import{p as j}from"./qYb16FSw.js";import{c as Je,o as Qe,a as Ve}from"./C_WNR8j8.js";import{api as Xe,ApiError as Ye}from"./C5jwvbV4.js";/* empty css        */var Ze=v('<div class="search-indicator svelte-zf3xfo"><div class="search-spinner svelte-zf3xfo"></div></div>'),$e=v('<div class="loading-item svelte-zf3xfo"><div class="loading-spinner svelte-zf3xfo"></div> <span class="svelte-zf3xfo">Searching...</span></div>'),ea=v('<div class="error-item svelte-zf3xfo"><div class="error-message svelte-zf3xfo"> </div> <button class="retry-button svelte-zf3xfo">Retry</button></div>'),aa=v('<div class="customer-email svelte-zf3xfo"> </div>'),ta=v('<li><div class="customer-option svelte-zf3xfo"><div class="customer-name svelte-zf3xfo"> </div> <!></div></li>'),sa=v('<ul class="svelte-zf3xfo"></ul>'),ra=v('<div class="no-results svelte-zf3xfo"> </div>'),oa=v('<div class="dropdown-search-indicator svelte-zf3xfo"><div class="dropdown-search-spinner svelte-zf3xfo"></div> <span class="svelte-zf3xfo">Updating...</span></div>'),ia=v('<div class="dropdown-menu svelte-zf3xfo"><!></div> <!>',1),la=v('<div class="error-message svelte-zf3xfo"> </div>'),na=v('<div class="form-group customer-search svelte-zf3xfo"><label for="customerSearch" class="svelte-zf3xfo">Customer</label> <div><input type="text" id="customerSearch" autocomplete="off"> <input type="hidden" name="customerId" class="svelte-zf3xfo"> <!> <!></div> <!></div>');function ka(ve,D){Ue(D,!1);const R=p(),X=p(),ce=Je();let Y=j(D,"customerId",12,""),u=j(D,"customerSearch",12,""),me=j(D,"hasError",8,!1),Z=j(D,"errorMessage",8,""),c=p(!1),i=p(-1),h=p([]),U=p(!1),w=p(!1),z=p(null),A,q="",$=p();Qe(async()=>{await F()}),Ve(()=>{A&&clearTimeout(A)});async function F(e){if(e!==q){e===void 0?s(U,!0):s(w,!0),s(z,null),q=e||"";try{let t="/customers";e&&e.trim()&&(t=`/customers/search?${new URLSearchParams({query:e.trim()}).toString()}`);const o=await Xe.get(t);Array.isArray(o)?s(h,o.map(r=>({id:r.id,fullName:r.name||r.fullName||"Unknown Customer",companyName:r.companyName||"",emails:r.emails||[{id:"1",email:r.email||"",type:"Work",isPrimary:!0}],phones:r.phones||[{id:"1",phone:r.phone||"",type:"Work",isPrimary:!0}],addresses:r.addresses||[],status:r.status||"Customer",notes:r.notes||[],checklists:r.checklists||[],communicationTimeline:r.communicationTimeline||[],createdAt:r.createdAt||new Date().toISOString(),updatedAt:r.updatedAt||new Date().toISOString()}))):(console.error("API response was not an array:",o),s(h,[]),s(z,"Received unexpected data format from the server."))}catch(t){console.error("Error loading customers:",t),t instanceof Ye?s(z,`Failed to load customers: ${t.message}`):s(z,"An unexpected error occurred while loading customers."),s(h,[])}finally{s(U,!1),s(w,!1)}}}function ue(e){clearTimeout(A),s(i,-1),A=setTimeout(()=>{e!==q&&(e.trim().length>=2||e.trim().length===0)&&F(e)},300)}function ee(e){if(!e.trim())return a(h);const t=e.toLowerCase();return a(h).filter(o=>o.fullName.toLowerCase().includes(t)||o.companyName&&o.companyName.toLowerCase().includes(t)||o.emails.some(r=>r.email.toLowerCase().includes(t)))}function pe(e){const t=e.target;u(t.value),a(c)||s(c,!0),s(i,-1),ue(u())}function ae(e){Y(e.id),u(e.companyName||e.fullName),s(c,!1),s(i,-1),clearTimeout(A),ce("selectcustomer",e)}function he(e){if(!a(c))return;const t=ee(u());e.key==="ArrowDown"?(e.preventDefault(),s(i,Math.min(a(i)+1,t.length-1))):e.key==="ArrowUp"?(e.preventDefault(),s(i,Math.max(a(i)-1,-1))):e.key==="Enter"&&a(i)>=0?(e.preventDefault(),ae(t[a(i)])):e.key==="Escape"&&(s(c,!1),s(i,-1))}function _e(e){const t=e.relatedTarget;(!t||!t.closest(".dropdown-menu"))&&setTimeout(()=>{s(c,!1),s(i,-1)},150)}function ge(){s(c,!0),a(z)&&a(h).length===0&&F()}function xe(){q="",F(u())}ne(()=>Oe(u()),()=>{s(R,ee(u()))}),ne(()=>(a(w),a(c),a(h)),()=>{s(X,a(w)&&a(c)&&a(h).length===0)}),qe(),He();var B=na(),O=x(f(B),2);let te;var n=f(O);fe(n);let se;Ke(n,e=>s($,e),()=>a($));var K=x(n,2);fe(K);var re=x(K,2);{var ye=e=>{var t=Ze();d(e,t)};y(re,e=>{a(w)&&e(ye)})}var we=x(re,2);{var ze=e=>{var t=ia(),o=Te(t),r=f(o);{var Ce=m=>{var I=$e();d(m,I)},ke=(m,I)=>{{var Ae=C=>{var E=ea(),L=f(E),G=f(L,!0);l(L);var _=x(L,2);l(E),N(()=>P(G,a(z))),S("click",_,xe),d(C,E)},Ie=(C,E)=>{{var L=_=>{var b=sa();We(b,5,()=>a(R),je,(H,g,oe)=>{var k=ta();let ie;var le=f(k),J=f(le),Ee=f(J,!0);l(J);var Le=x(J,2);{var Me=M=>{var Q=aa(),Pe=f(Q,!0);l(Q),N(T=>P(Pe,T),[()=>{var T;return((T=a(g).emails.find(Re=>Re.isPrimary))==null?void 0:T.email)||a(g).emails[0].email}],W),d(M,Q)};y(Le,M=>{a(g).emails.length>0&&M(Me)})}l(le),l(k),N(M=>{ie=V(k,1,"svelte-zf3xfo",null,ie,M),P(Ee,a(g).companyName||a(g).fullName)},[()=>({highlighted:oe===a(i)})],W),S("mousedown",k,Ge(()=>ae(a(g)))),S("mouseenter",k,()=>s(i,oe)),d(H,k)}),l(b),d(_,b)},G=_=>{var b=ra(),H=f(b,!0);l(b),N(g=>P(H,g),[()=>u().trim()?"No customers found":"No customers available"],W),d(_,b)};y(C,_=>{a(R).length>0?_(L):_(G,!1)},E)}};y(m,C=>{a(z)?C(Ae):C(Ie,!1)},I)}};y(r,m=>{a(X)?m(Ce):m(ke,!1)})}l(o);var Ne=x(o,2);{var De=m=>{var I=oa();d(m,I)};y(Ne,m=>{a(w)&&a(R).length>0&&m(De)})}d(e,t)};y(we,e=>{a(c)&&e(ze)})}l(O);var be=x(O,2);{var Se=e=>{var t=la(),o=f(t,!0);l(t),N(()=>P(o,Z())),d(e,t)};y(be,e=>{Z()&&e(Se)})}l(B),N((e,t)=>{te=V(O,1,"search-container svelte-zf3xfo",null,te,e),Be(n,"placeholder",a(U)?"Loading customers...":"Search customers..."),se=V(n,1,"search-input svelte-zf3xfo",null,se,t),n.disabled=a(U)},[()=>({"has-error":me()}),()=>({searching:a(w)})],W),de(n,u),S("input",n,pe),S("keydown",n,he),S("blur",n,_e),S("focus",n,ge),de(K,Y),d(ve,B),Fe()}export{ka as C};
