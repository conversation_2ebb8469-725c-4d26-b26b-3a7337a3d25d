const d="ejp_calendar_events";function s(){const e=localStorage.getItem(d);return e?JSON.parse(e):[]}function i(e){localStorage.setItem(d,JSON.stringify(e))}function c(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}async function l(e,a,o){return new Promise(n=>{setTimeout(()=>{let t=s();t=t.filter(r=>r.startDateTime>=e&&r.startDateTime<=a),n(t)},100)})}async function f(e){return new Promise(a=>{setTimeout(()=>{const o=s(),n={...e,id:c(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};o.push(n),i(o),a(n)},100)})}async function S(e,a){return new Promise((o,n)=>{setTimeout(()=>{const t=s(),r=t.findIndex(u=>u.id===e);if(r===-1){n(new Error("Calendar event not found"));return}t[r]={...t[r],...a,updatedAt:new Date().toISOString()},i(t),o(t[r])},100)})}async function g(e){return new Promise((a,o)=>{setTimeout(()=>{const n=s(),t=n.findIndex(r=>r.id===e);if(t===-1){o(new Error("Calendar event not found"));return}n.splice(t,1),i(n),a()},100)})}export{f as c,g as d,l as g,S as u};
