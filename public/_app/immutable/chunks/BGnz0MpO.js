import{d as c,w as n}from"./DTQDE1SG.js";import{d as u,u as y,c as d,g as v,a as m}from"./Ca7pnwKO.js";async function f(){if((await m()).length===0){const e=[{firstName:"Tom",lastName:"<PERSON>",email:"<EMAIL>",phone:"(*************",position:"Senior Technician",department:"Field Services",hireDate:"2022-01-15",isActive:!0,wageInfo:{type:"Hourly",rate:28,currency:"GBP",effectiveDate:"2022-01-15",overtimeRate:42},wageHistory:[],skills:["Plumbing","Electrical","HVAC"],certifications:[],availability:{monday:{isAvailable:!0,startTime:"08:00",endTime:"17:00"},tuesday:{isAvailable:!0,startTime:"08:00",endTime:"17:00"},wednesday:{isAvailable:!0,startTime:"08:00",endTime:"17:00"},thursday:{isAvailable:!0,startTime:"08:00",endTime:"17:00"},friday:{isAvailable:!0,startTime:"08:00",endTime:"17:00"},saturday:{isAvailable:!1},sunday:{isAvailable:!1},timeOff:[]}},{firstName:"Lisa",lastName:"Rodriguez",email:"<EMAIL>",phone:"(*************",position:"Technician",department:"Field Services",hireDate:"2022-06-01",isActive:!0,wageInfo:{type:"Hourly",rate:28,currency:"USD",effectiveDate:"2022-06-01",overtimeRate:42},wageHistory:[],skills:["General Maintenance","Inspection"],certifications:[],availability:{monday:{isAvailable:!0,startTime:"09:00",endTime:"18:00"},tuesday:{isAvailable:!0,startTime:"09:00",endTime:"18:00"},wednesday:{isAvailable:!0,startTime:"09:00",endTime:"18:00"},thursday:{isAvailable:!0,startTime:"09:00",endTime:"18:00"},friday:{isAvailable:!0,startTime:"09:00",endTime:"18:00"},saturday:{isAvailable:!0,startTime:"10:00",endTime:"15:00"},sunday:{isAvailable:!1},timeOff:[]}},{firstName:"David",lastName:"Chen",email:"<EMAIL>",phone:"(*************",position:"Lead Electrician",department:"Electrical",hireDate:"2021-03-10",isActive:!0,wageInfo:{type:"Hourly",rate:42,currency:"USD",effectiveDate:"2021-03-10",overtimeRate:63},wageHistory:[],skills:["Electrical","Industrial Electrical","Safety"],certifications:[],availability:{monday:{isAvailable:!0,startTime:"07:00",endTime:"16:00"},tuesday:{isAvailable:!0,startTime:"07:00",endTime:"16:00"},wednesday:{isAvailable:!0,startTime:"07:00",endTime:"16:00"},thursday:{isAvailable:!0,startTime:"07:00",endTime:"16:00"},friday:{isAvailable:!0,startTime:"07:00",endTime:"16:00"},saturday:{isAvailable:!1},sunday:{isAvailable:!1},timeOff:[]}}];for(const t of e)await d(t)}}const r=n([]),s=n(!1),l=n(null),w=c(r,a=>a.filter(e=>e.isActive)),A={async loadStaff(){s.set(!0);try{await f();const a=await m();r.set(a)}catch(a){console.error("Error loading staff:",a)}finally{s.set(!1)}},async loadActiveStaff(){s.set(!0);try{await f();const a=await v();r.set(a)}catch(a){console.error("Error loading active staff:",a)}finally{s.set(!1)}},async addStaff(a){try{const e=await d(a);return r.update(t=>[...t,e]),e}catch(e){throw console.error("Error adding staff:",e),e}},async updateStaff(a,e){try{const t=await y(a,e);return r.update(i=>i.map(o=>o.id===a?t:o)),l.update(i=>(i==null?void 0:i.id)===a?t:i),t}catch(t){throw console.error("Error updating staff:",t),t}},async deleteStaff(a){try{await u(a),r.update(e=>e.filter(t=>t.id!==a)),l.update(e=>(e==null?void 0:e.id)===a?null:e)}catch(e){throw console.error("Error deleting staff:",e),e}},selectStaff(a){l.set(a)},searchStaff(a){return c(r,e=>e.filter(t=>t.fullName.toLowerCase().includes(a.toLowerCase())||t.email.toLowerCase().includes(a.toLowerCase())||t.position.toLowerCase().includes(a.toLowerCase())||t.skills.some(i=>i.toLowerCase().includes(a.toLowerCase()))))}};export{r as a,w as b,A as s};
