const O="ejp_jobs",P="ejp_job_statuses",A="ejp_job_types",D="ejp_resources",h=[{id:"1",name:"Backlog",color:"#6B7280",order:1,isCompleted:!1},{id:"2",name:"Scheduled",color:"#3B82F6",order:2,isCompleted:!1},{id:"3",name:"In Progress",color:"#F59E0B",order:3,isCompleted:!1},{id:"4",name:"Completed",color:"#10B981",order:4,isCompleted:!0},{id:"5",name:"Cancelled",color:"#EF4444",order:5,isCompleted:!0}],T=[{id:"1",name:"Standard House Cleaning",description:"Regular house cleaning service including all rooms",pricingModel:"hourly",defaultDuration:180,defaultHourlyRate:35,defaultFields:[{id:"1",key:"rooms",value:"",type:"number",options:[]},{id:"2",key:"bathrooms",value:"",type:"number",options:[]},{id:"3",key:"pets",value:"",type:"boolean",options:[]}],requiredSkills:["General Cleaning"],defaultResources:[{resourceId:"1",resourceName:"Vacuum Cleaner",resourceType:"Equipment",quantity:1,isRequired:!0},{resourceId:"2",resourceName:"Cleaning Supplies",resourceType:"Material",quantity:1,isRequired:!0}],isActive:!0,category:"Residential",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"2",name:"Deep Cleaning",description:"Comprehensive deep cleaning service",pricingModel:"fixed",defaultDuration:360,defaultFixedPrice:250,defaultFields:[{id:"1",key:"rooms",value:"",type:"number",options:[]},{id:"2",key:"appliances",value:"",type:"boolean",options:[]},{id:"3",key:"windows",value:"",type:"boolean",options:[]}],requiredSkills:["Deep Cleaning","Appliance Cleaning"],defaultResources:[{resourceId:"1",resourceName:"Vacuum Cleaner",resourceType:"Equipment",quantity:1,isRequired:!0},{resourceId:"2",resourceName:"Steam Cleaner",resourceType:"Equipment",quantity:1,isRequired:!0},{resourceId:"3",resourceName:"Specialized Cleaning Supplies",resourceType:"Material",quantity:1,isRequired:!0}],isActive:!0,category:"Residential",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"3",name:"Office Cleaning",description:"Commercial office cleaning service",pricingModel:"per_unit",defaultDuration:120,defaultUnitPrice:2.5,defaultFields:[{id:"1",key:"square_feet",value:"",type:"number",options:[]},{id:"2",key:"floors",value:"",type:"number",options:[]},{id:"3",key:"after_hours",value:"",type:"boolean",options:[]}],requiredSkills:["Commercial Cleaning"],defaultResources:[{resourceId:"1",resourceName:"Commercial Vacuum",resourceType:"Equipment",quantity:1,isRequired:!0},{resourceId:"4",resourceName:"Office Cleaning Supplies",resourceType:"Material",quantity:1,isRequired:!0}],isActive:!0,category:"Commercial",createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}],C=[{id:"1",name:"Vacuum Cleaner",type:"Equipment",description:"Professional grade vacuum cleaner",costPerHour:5,isAvailable:!0,location:"Main Office",maintenanceSchedule:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"2",name:"Cleaning Supplies",type:"Material",description:"Standard cleaning supplies kit",costPerUnit:15,isAvailable:!0,location:"Storage Room",maintenanceSchedule:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"3",name:"Steam Cleaner",type:"Equipment",description:"Professional steam cleaning equipment",costPerHour:10,isAvailable:!0,location:"Equipment Room",maintenanceSchedule:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"4",name:"Office Cleaning Supplies",type:"Material",description:"Commercial office cleaning supplies",costPerUnit:20,isAvailable:!0,location:"Storage Room",maintenanceSchedule:[],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}];function p(){const e=localStorage.getItem(O);return(e?JSON.parse(e):[]).map(o=>({...o,customFields:o.customFields||[],assignedStaff:o.assignedStaff||[],resources:o.resources||[],tags:o.tags||[],attachments:o.attachments||[],estimatedCost:o.estimatedCost||{laborCost:0,materialCost:0,resourceCost:0,totalCost:0,breakdown:[]}}))}function g(e){localStorage.setItem(O,JSON.stringify(e))}function J(){const e=localStorage.getItem(P);return e?JSON.parse(e):h}function m(){const e=localStorage.getItem(A);return e?JSON.parse(e):(y(T),T)}function y(e){localStorage.setItem(A,JSON.stringify(e))}function b(){const e=localStorage.getItem(D);return e?JSON.parse(e):(w(C),C)}function w(e){localStorage.setItem(D,JSON.stringify(e))}function l(){return Date.now().toString(36)+Math.random().toString(36).substr(2)}function v(e,i,o,n=[]){let t=0,s=0,r=0;const c=[];if(e.pricingModel==="hourly")i.forEach(a=>{const u=a.hourlyRate*a.estimatedHours;t+=u,c.push({id:l(),type:"labor",description:`${a.staffName} - ${a.estimatedHours}h @ $${a.hourlyRate}/h`,quantity:a.estimatedHours,unitCost:a.hourlyRate,totalCost:u,staffId:a.staffId})});else if(e.pricingModel==="fixed")t=e.defaultFixedPrice||0,c.push({id:l(),type:"labor",description:`Fixed price for ${e.name}`,quantity:1,unitCost:t,totalCost:t});else if(e.pricingModel==="per_unit"){const a=n.find(f=>f.key==="square_feet"||f.key==="units"),u=a&&parseFloat(a.value)||1,S=e.defaultUnitPrice||0;t=u*S,c.push({id:l(),type:"labor",description:`${u} units @ $${S}/unit`,quantity:u,unitCost:S,totalCost:t})}o.forEach(a=>{const u=a.quantity*a.costPerUnit;a.resourceType==="Material"?s+=u:r+=u,c.push({id:l(),type:a.resourceType==="Material"?"material":"resource",description:`${a.resourceName} - ${a.quantity} @ $${a.costPerUnit}`,quantity:a.quantity,unitCost:a.costPerUnit,totalCost:u,resourceId:a.resourceId})});const d=t+s+r;return{laborCost:t,materialCost:s,resourceCost:r,totalCost:d,breakdown:c}}async function _(){return new Promise(e=>{setTimeout(()=>{const i=p(),o=m();i.forEach(n=>{n.jobType=o.find(t=>t.id===n.jobTypeId)}),e(i)},100)})}async function k(e){return new Promise(i=>{setTimeout(()=>{const o=p(),n=m(),t=o.filter(s=>s.customerId===e);t.forEach(s=>{s.jobType=n.find(r=>r.id===s.jobTypeId)}),i(t)},100)})}async function F(e){return new Promise(i=>{setTimeout(()=>{const o=p(),n={...e,id:l(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};o.push(n),g(o),i(n)},100)})}async function x(e,i){return new Promise((o,n)=>{setTimeout(()=>{const t=p(),s=t.findIndex(r=>r.id===e);if(s===-1){n(new Error("Job not found"));return}t[s]={...t[s],...i,updatedAt:new Date().toISOString()},g(t),o(t[s])},100)})}async function j(e){return new Promise((i,o)=>{setTimeout(()=>{const n=p(),t=n.findIndex(s=>s.id===e);if(t===-1){o(new Error("Job not found"));return}n.splice(t,1),g(n),i()},100)})}async function N(){return new Promise(e=>{setTimeout(()=>{e(J())},100)})}async function M(e,i){return new Promise((o,n)=>{setTimeout(()=>{const t=p(),s=J(),r=t.findIndex(d=>d.id===e),c=s.find(d=>d.id===i);if(r===-1){n(new Error("Job not found"));return}if(!c){n(new Error("Status not found"));return}t[r].status=c,t[r].updatedAt=new Date().toISOString(),c.isCompleted&&!t[r].completedAt?t[r].completedAt=new Date().toISOString():c.isCompleted||(t[r].completedAt=void 0),g(t),o(t[r])},100)})}async function U(){return new Promise(e=>{setTimeout(()=>{e(m())},100)})}async function $(e){return new Promise(i=>{setTimeout(()=>{const o=m(),n={...e,id:l()};o.push(n),y(o),i(n)},100)})}async function B(e,i){return new Promise((o,n)=>{setTimeout(()=>{const t=m(),s=t.findIndex(r=>r.id===e);if(s===-1){n(new Error("Job type not found"));return}t[s]={...t[s],...i},y(t),o(t[s])},100)})}async function Y(e){return new Promise((i,o)=>{setTimeout(()=>{const n=m(),t=n.findIndex(s=>s.id===e);if(t===-1){o(new Error("Job type not found"));return}n.splice(t,1),y(n),i()},100)})}async function G(e){return new Promise(i=>{setTimeout(()=>{const o=p(),n=m(),t=o.filter(s=>s.customerId===e&&s.status.isCompleted&&!s.completedAt);t.forEach(s=>{s.jobType=n.find(r=>r.id===s.jobTypeId)}),i(t)},100)})}async function H(){return new Promise(e=>{setTimeout(()=>{e(b())},100)})}async function K(e){return new Promise(i=>{setTimeout(()=>{const o=b(),n={...e,id:l(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};o.push(n),w(o),i(n)},100)})}async function L(e,i){return new Promise((o,n)=>{setTimeout(()=>{const t=b(),s=t.findIndex(r=>r.id===e);if(s===-1){n(new Error("Resource not found"));return}t[s]={...t[s],...i,updatedAt:new Date().toISOString()},w(t),o(t[s])},100)})}async function V(e){return new Promise((i,o)=>{setTimeout(()=>{const n=b(),t=n.findIndex(s=>s.id===e);if(t===-1){o(new Error("Resource not found"));return}n.splice(t,1),w(n),i()},100)})}async function z(e,i,o){return new Promise(n=>{setTimeout(()=>{const t=p(),s=m();let r=t.filter(c=>c.assignedStaff.some(d=>d.staffId===e));r.forEach(c=>{c.jobType=s.find(d=>d.id===c.jobTypeId)}),n(r)},100)})}const E="ejp_recurring_jobs";function R(){const e=localStorage.getItem(E);return e?JSON.parse(e):[]}function q(e){localStorage.setItem(E,JSON.stringify(e))}async function Q(e){return new Promise(i=>{setTimeout(()=>{const o=R(),n={...e,id:l(),createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};o.push(n),q(o),i(n)},100)})}async function W(e,i,o){return new Promise((n,t)=>{setTimeout(()=>{const r=R().find(I=>I.id===e);if(!r){t(new Error("Recurring job template not found"));return}const c=[],d=new Date(i),a=new Date(o);let u=new Date(d),S=0;for(;u<=a&&!(r.recurrencePattern.maxOccurrences&&S>=r.recurrencePattern.maxOccurrences||r.recurrencePattern.endDate&&u>new Date(r.recurrencePattern.endDate));){const I={id:l(),title:`${r.name} - ${u.toLocaleDateString()}`,customerId:r.customerId,jobTypeId:r.jobTypeId,status:{id:"2",name:"Scheduled",color:"#3B82F6",order:2,isCompleted:!1},description:r.description,scheduledDateTime:u.toISOString(),assignedStaff:r.assignedStaff,jobAddress:r.jobAddress,customFields:r.customFields,resources:r.resources,estimatedCost:{laborCost:0,materialCost:0,resourceCost:0,totalCost:0,breakdown:[]},priority:"Medium",tags:["recurring"],attachments:[],notes:`Generated from recurring template: ${r.name}`,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()};switch(c.push(I),S++,r.recurrencePattern.type){case"daily":u.setDate(u.getDate()+r.recurrencePattern.interval);break;case"weekly":u.setDate(u.getDate()+7*r.recurrencePattern.interval);break;case"monthly":u.setMonth(u.getMonth()+r.recurrencePattern.interval);break;case"yearly":u.setFullYear(u.getFullYear()+r.recurrencePattern.interval);break}}const f=p();f.push(...c),g(f),n(c)},100)})}export{k as a,x as b,F as c,j as d,U as e,N as f,G as g,_ as h,$ as i,H as j,B as k,Y as l,Q as m,W as n,L as o,K as p,V as q,z as r,v as s,M as u};
