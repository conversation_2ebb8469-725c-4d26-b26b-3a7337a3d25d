import"./CWj6FrbW.js";import"./69_IOA4Y.js";import{e as j,t as d,g as B,n as T,k as s,j as M,m as L,r,i as l,s as p}from"./p3DoyA09.js";import{s as q,e as z}from"./BUelSUke.js";import{e as F}from"./DEqeA9IH.js";import{h as I}from"./FLKOLPZJ.js";import{t as h,a as u}from"./B67foYpL.js";import{s as m}from"./D7jLSc-x.js";import{t as V,f as A}from"./nu3w91fk.js";import{i as D}from"./D3pqaimu.js";import{s as E,a as G}from"./qYb16FSw.js";import{o as H}from"./C_WNR8j8.js";import{t as J,r as K}from"./Ce-0qAhV.js";var N=h('<div><div class="toast-icon svelte-cny1si"><!></div> <div class="toast-content svelte-cny1si"> </div> <button class="toast-close svelte-cny1si" aria-label="Close notification"><svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg></button></div>'),O=h("<div></div>");function ie(w,g){j(g,!1);const[x,y]=E(),f=()=>G(J,"$toasts",x);let c=L(!1);H(()=>{M(c,!0)});function k(e){K(e)}function _(e){switch(e){case"success":return`
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
            <polyline points="22 4 12 14.01 9 11.01"></polyline>
          </svg>
        `;case"error":return`
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="15" y1="9" x2="9" y2="15"></line>
            <line x1="9" y1="9" x2="15" y2="15"></line>
          </svg>
        `;case"warning":return`
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
            <line x1="12" y1="9" x2="12" y2="13"></line>
            <line x1="12" y1="17" x2="12.01" y2="17"></line>
          </svg>
        `;case"info":default:return`
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="16" x2="12" y2="12"></line>
            <line x1="12" y1="8" x2="12.01" y2="8"></line>
          </svg>
        `}}D();var o=O();let v;F(o,5,f,e=>e.id,(e,i)=>{var t=N(),n=l(t),C=l(n);I(C,()=>_(s(i).type)),r(n);var a=p(n,2),$=l(a,!0);r(a);var b=p(a,2);r(t),d(()=>{m(t,1,`toast-item ${s(i).type??""}`,"svelte-cny1si"),q($,s(i).message)}),z("click",b,()=>k(s(i).id||"")),V(3,t,()=>A,()=>({y:50,duration:300})),u(e,t)}),r(o),d(e=>v=m(o,1,"toasts-container svelte-cny1si",null,v,e),[()=>({mounted:s(c)})],T),u(w,o),B(),y()}export{ie as T};
