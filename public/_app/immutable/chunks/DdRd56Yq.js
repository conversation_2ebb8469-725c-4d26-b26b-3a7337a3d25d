import{x as U,h as v,az as V,aA as Y,aB as z,aC as D,Q as C,aD as G,aE as K}from"./p3DoyA09.js";import{a as j,i as q,c as Q,d as F,b as J,n as W,f as X}from"./BUelSUke.js";import{t as Z,c as m,s as x}from"./D7jLSc-x.js";function ss(s,i){U(()=>{const a=i();return a&&a(s)})}function y(s,i={},a,f){for(var u in a){var o=a[u];i[u]!==o&&(a[u]==null?s.style.removeProperty(u):s.style.setProperty(u,o,f))}}function as(s,i,a,f){var u=s.__style;if(v||u!==i){var o=Z(i,f);(!v||o!==s.getAttribute("style"))&&(o==null?s.removeAttribute("style"):s.style.cssText=o),s.__style=i}else f&&(Array.isArray(f)?(y(s,a==null?void 0:a[0],f[0]),y(s,a==null?void 0:a[1],f[1],"important")):y(s,a,f));return f}const A=Symbol("class"),n=Symbol("style"),O=Symbol("is custom element"),I=Symbol("is html");function us(s){if(v){var i=!1,a=()=>{if(!i){if(i=!0,s.hasAttribute("value")){var f=s.value;b(s,"value",null),s.value=f}if(s.hasAttribute("checked")){var u=s.checked;b(s,"checked",null),s.checked=u}}};s.__on_r=a,D(a),j()}}function es(s,i){var a=g(s);a.value===(a.value=i??void 0)||s.value===i&&(i!==0||s.nodeName!=="PROGRESS")||(s.value=i??"")}function os(s,i){var a=g(s);a.checked!==(a.checked=i??void 0)&&(s.checked=i)}function is(s,i){i?s.hasAttribute("selected")||s.setAttribute("selected",""):s.removeAttribute("selected")}function b(s,i,a,f){var u=g(s);v&&(u[i]=s.getAttribute(i),i==="src"||i==="srcset"||i==="href"&&s.nodeName==="LINK")||u[i]!==(u[i]=a)&&(i==="loading"&&(s[K]=a),a==null?s.removeAttribute(i):typeof a!="string"&&p(s).includes(i)?s[i]=a:s.setAttribute(i,a))}function cs(s,i,a,f,u=!1){var o=g(s),h=o[O],P=!o[I];let E=v&&h;E&&C(!1);var l=i||{},N=s.tagName==="OPTION";for(var T in i)T in a||(a[T]=null);a.class?a.class=m(a.class):a.class=null,a[n]&&(a.style??(a.style=null));var $=p(s);for(const r in a){let t=a[r];if(N&&r==="value"&&t==null){s.value=s.__value="",l[r]=t;continue}if(r==="class"){var R=s.namespaceURI==="http://www.w3.org/1999/xhtml";x(s,R,t,f,i==null?void 0:i[A],a[A]),l[r]=t,l[A]=a[A];continue}if(r==="style"){as(s,t,i==null?void 0:i[n],a[n]),l[r]=t,l[n]=a[n];continue}var k=l[r];if(t!==k){l[r]=t;var w=r[0]+r[1];if(w!=="$$")if(w==="on"){const c={},d="$$"+r;let e=r.slice(2);var S=X(e);if(q(e)&&(e=e.slice(0,-7),c.capture=!0),!S&&k){if(t!=null)continue;s.removeEventListener(e,l[d],c),l[d]=null}if(t!=null)if(S)s[`__${e}`]=t,F([e]);else{let B=function(H){l[r].call(this,H)};l[d]=Q(e,s,B,c)}else S&&(s[`__${e}`]=void 0)}else if(r==="style")b(s,r,t);else if(r==="autofocus")J(s,!!t);else if(!h&&(r==="__value"||r==="value"&&t!=null))s.value=s.__value=t;else if(r==="selected"&&N)is(s,t);else{var _=r;P||(_=W(_));var L=_==="defaultValue"||_==="defaultChecked";if(t==null&&!h&&!L)if(o[r]=null,_==="value"||_==="checked"){let c=s;const d=i===void 0;if(_==="value"){let e=c.defaultValue;c.removeAttribute(_),c.defaultValue=e,c.value=c.__value=d?e:null}else{let e=c.defaultChecked;c.removeAttribute(_),c.defaultChecked=e,c.checked=d?e:!1}}else s.removeAttribute(r);else L||$.includes(_)&&(h||typeof t!="string")?s[_]=t:typeof t!="function"&&b(s,_,t)}}}E&&C(!0);for(let r of Object.getOwnPropertySymbols(a))r.description===G&&ss(s,()=>a[r]);return l}function g(s){return s.__attributes??(s.__attributes={[O]:s.nodeName.includes("-"),[I]:s.namespaceURI===V})}var M=new Map;function p(s){var i=M.get(s.nodeName);if(i)return i;M.set(s.nodeName,i=[]);for(var a,f=s,u=Element.prototype;u!==f;){a=z(f);for(var o in a)a[o].set&&i.push(o);f=Y(f)}return i}export{es as a,os as b,cs as c,as as d,us as r,b as s};
