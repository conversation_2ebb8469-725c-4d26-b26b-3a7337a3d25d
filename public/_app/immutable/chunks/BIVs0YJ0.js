import"./CWj6FrbW.js";import"./69_IOA4Y.js";import{e as C,t as c,g as D,r as f,i as w,n as E,k as i}from"./p3DoyA09.js";import{s as q,e as v}from"./BUelSUke.js";import{e as K}from"./DEqeA9IH.js";import{t as k,a as u}from"./B67foYpL.js";import{s as b}from"./DdRd56Yq.js";import{s as h}from"./D7jLSc-x.js";import{i as j}from"./D3pqaimu.js";import{p as n}from"./qYb16FSw.js";import{c as z}from"./C_WNR8j8.js";var A=k('<button type="button" role="tab"> </button>'),B=k("<div></div>");function S(_,s){C(s,!1);let d=n(s,"tabs",24,()=>[]),r=n(s,"activeTab",12,""),g=n(s,"navClass",8,"");const y=z();function m(t){var e;(e=d().find(a=>a.id===t))!=null&&e.disabled||(r(t),y("change",{tabId:t}))}function T(t,e){(t.key==="Enter"||t.key===" ")&&(t.preventDefault(),m(e))}j();var o=B();K(o,5,d,t=>t.id,(t,e)=>{var a=A();let p;var x=w(a,!0);f(a),c(l=>{p=h(a,1,"tab-button svelte-1gmkhpa",null,p,l),a.disabled=i(e).disabled,b(a,"aria-selected",r()===i(e).id),b(a,"tabindex",r()===i(e).id?0:-1),q(x,i(e).label)},[()=>({active:r()===i(e).id,disabled:i(e).disabled})],E),v("click",a,()=>m(i(e).id)),v("keydown",a,l=>T(l,i(e).id)),u(t,a)}),f(o),c(()=>h(o,1,`tab-navigation ${g()??""}`,"svelte-1gmkhpa")),u(_,o),D()}export{S as T};
