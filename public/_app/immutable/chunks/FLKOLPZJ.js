import{t as u,h as n,a as l,F as g,G as p,d as h,H as y,I as w,J as R,K as b,L as f}from"./p3DoyA09.js";import{c as m,d as H}from"./B67foYpL.js";function $(c,v,i=!1,d=!1,I=!1){var o=c,t="";u(()=>{var r=g;if(t===(t=v()??"")){n&&l();return}if(r.nodes_start!==null&&(p(r.nodes_start,r.nodes_end),r.nodes_start=r.nodes_end=null),t!==""){if(n){h.data;for(var e=l(),_=e;e!==null&&(e.nodeType!==8||e.data!=="");)_=e,e=y(e);if(e===null)throw w(),R;m(h,_),o=b(e);return}var s=t+"";i?s=`<svg>${s}</svg>`:d&&(s=`<math>${s}</math>`);var a=H(s);if((i||d)&&(a=f(a)),m(f(a),a.lastChild),i||d)for(;f(a);)o.before(f(a));else o.before(a)}})}export{$ as h};
