import{B as w,z as C,y as m,h as n,C as A,D as B,A as t}from"./p3DoyA09.js";import{l as b}from"./BUelSUke.js";function V(e,c,a=c){var f=w();b(e,"input",r=>{var d=r?e.defaultValue:e.value;if(d=k(e)?u(d):d,a(d),f&&d!==(d=c())){var v=e.selectionStart,s=e.selectionEnd;e.value=d??"",s!==null&&(e.selectionStart=v,e.selectionEnd=Math.min(s,e.value.length))}}),(n&&e.defaultValue!==e.value||C(c)==null&&e.value)&&a(k(e)?u(e.value):e.value),m(()=>{var r=c();k(e)&&r===u(e.value)||e.type==="date"&&!r&&!e.value||r!==e.value&&(e.value=r??"")})}const _=new Set;function g(e,c,a,f,r=f){var d=a.getAttribute("type")==="checkbox",v=e;let s=!1;if(c!==null)for(var h of c)v=v[h]??(v[h]=[]);v.push(a),b(a,"change",()=>{var l=a.__value;d&&(l=y(v,l,a.checked)),r(l)},()=>r(d?[]:null)),m(()=>{var l=f();if(n&&a.defaultChecked!==a.checked){s=!0;return}d?(l=l||[],a.checked=l.includes(a.__value)):a.checked=A(a.__value,l)}),B(()=>{var l=v.indexOf(a);l!==-1&&v.splice(l,1)}),_.has(v)||(_.add(v),t(()=>{v.sort((l,o)=>l.compareDocumentPosition(o)===4?-1:1),_.delete(v)})),t(()=>{if(s){var l;if(d)l=y(v,l,a.checked);else{var o=v.find(S=>S.checked);l=o==null?void 0:o.__value}r(l)}})}function q(e,c,a=c){b(e,"change",f=>{var r=f?e.defaultChecked:e.checked;a(r)}),(n&&e.defaultChecked!==e.checked||C(c)==null)&&a(e.checked),m(()=>{var f=c();e.checked=!!f})}function y(e,c,a){for(var f=new Set,r=0;r<e.length;r+=1)e[r].checked&&f.add(e[r].__value);return a||f.delete(c),Array.from(f)}function k(e){var c=e.type;return c==="number"||c==="range"}function u(e){return e===""?null:+e}export{q as a,V as b,g as c};
