<!doctype html>
<html lang="en">
	<head>
		<meta charset="utf-8" />
		<link rel="icon" href="/favicon.png" />
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<link rel="stylesheet" href="/styles/variables.css" />
		
		
		<link rel="modulepreload" href="/_app/immutable/entry/start.C65mAuup.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/CSyJhG7e.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/C_WNR8j8.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/p3DoyA09.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/DIeogL5L.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/DTQDE1SG.js">
		<link rel="modulepreload" href="/_app/immutable/entry/app.B9hN51FK.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/Dp1pzeXC.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/BUelSUke.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/B67foYpL.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/CWj6FrbW.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/DwdToawP.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/DTrhvrmD.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/chpzhzGT.js">
		<link rel="modulepreload" href="/_app/immutable/chunks/qYb16FSw.js">
	</head>
	<body data-sveltekit-preload-data="hover">
		<div style="display: contents">
			<script>
				{
					__sveltekit_1tckygg = {
						base: ""
					};

					const element = document.currentScript.parentElement;

					Promise.all([
						import("/_app/immutable/entry/start.C65mAuup.js"),
						import("/_app/immutable/entry/app.B9hN51FK.js")
					]).then(([kit, app]) => {
						kit.start(app, element);
					});
				}
			</script>
		</div>
	</body>
</html>
